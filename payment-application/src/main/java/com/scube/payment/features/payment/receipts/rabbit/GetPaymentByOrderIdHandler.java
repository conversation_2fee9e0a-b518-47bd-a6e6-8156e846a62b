package com.scube.payment.features.payment.receipts.rabbit;

import com.scube.payment.features.payment.processing.dto.GetPaymentResponseDto;
import com.scube.payment.features.payment.storage.service.PaymentStorageService;
import com.scube.rabbit.core.RabbitResult;
import com.scube.rabbit.core.fanout.subscriber.FanoutListenerRpc;
import com.scube.rabbit.core.fanout.subscriber.IRabbitFanoutSubscriberRpc;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.UUID;

@Component
@RequiredArgsConstructor
public class GetPaymentByOrderIdHandler extends FanoutListenerRpc<GetPaymentByOrderIdHandler.GetPaymentByOrderIdQuery, GetPaymentByOrderIdHandler.GetPaymentByOrderIdResponse> {
    private final PaymentStorageService paymentService;

    @Override
    public RabbitResult<GetPaymentByOrderIdResponse> consume(GetPaymentByOrderIdQuery event) {
        return RabbitResult.of(() -> {
            List<GetPaymentResponseDto> payments = paymentService.getPaymentResponseDtos(event.orderId());
            return new GetPaymentByOrderIdResponse(payments);
        });
    }

    public record GetPaymentByOrderIdQuery(
            UUID orderId) implements IRabbitFanoutSubscriberRpc<GetPaymentByOrderIdResponse> {
    }

    public record GetPaymentByOrderIdResponse(List<GetPaymentResponseDto> payments) {
    }
}
