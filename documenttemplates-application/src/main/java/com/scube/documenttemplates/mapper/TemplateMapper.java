package com.scube.documenttemplates.mapper;

import com.scube.documenttemplates.dto.TemplateDto;
import com.scube.documenttemplates.model.Template;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

@Mapper(componentModel = "spring")
public abstract class TemplateMapper {
    @Mapping(source = "uuid", target = "templateUuid")
    public abstract TemplateDto toDto(Template template);

    @Mapping(source = "templateUuid", target = "uuid")
    public abstract Template toEntity(TemplateDto documentDto);

    public abstract List<TemplateDto> toDto(List<Template> documents);

    public abstract List<Template> toEntity(List<TemplateDto> documentDtos);
}