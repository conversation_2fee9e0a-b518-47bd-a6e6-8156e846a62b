package com.scube.documenttemplates.dto;

import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;
import java.util.UUID;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class TemplateDto {
    private UUID templateUuid;
    private UUID documentUuid;
    private String nameKey;
    private String fileName;
    private String name;
    private String description;
    private String filetype;
    private Integer currentVersion;
    private Integer activeVersion;

    @JsonIgnore
    private Map<String, Object> properties;

    @JsonAnyGetter
    public Map<String, Object> getCustomFields() {
        return properties;
    }
}
