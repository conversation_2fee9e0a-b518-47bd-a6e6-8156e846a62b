package com.scube.documenttemplates.dto;

import com.fasterxml.jackson.databind.JsonNode;
import com.scube.documenttemplates.rabbit.GenerateDocumentCommandHandler;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class FillFileTemplateRequest {
    private String parentId;
    private String parentType;
    private String nameKey;
    private String format;
    private JsonNode data;

    public String getDataAsString() {
        return String.valueOf(this.getData());
    }

    public FillFileTemplateRequest(JsonNode data, String nameKey) {
        this.data = data;
        this.nameKey = nameKey;
    }

    public FillFileTemplateRequest(GenerateDocumentCommandHandler.GenerateDocumentCommand command) {
        this.parentId = command.getParentId();
        this.parentType = command.getParentType();
        this.nameKey = command.getNameKey();
        this.format = command.getFormat();
        this.data = command.getData();
    }
}