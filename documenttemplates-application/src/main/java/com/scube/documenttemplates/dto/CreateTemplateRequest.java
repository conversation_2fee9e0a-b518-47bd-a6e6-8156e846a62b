package com.scube.documenttemplates.dto;

import com.fasterxml.jackson.annotation.JsonAnySetter;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import java.util.Map;

@Data
@AllArgsConstructor
public class CreateTemplateRequest {
    @NotNull
    private MultipartFile file;
    private String name;
    private String fileName;
    @NotEmpty
    private String nameKey;
    private String description;
    private String filetype;

    /**
     * A map to store dynamic properties that are not explicitly defined as fields in this class.
     * The @JsonAnySetter annotation allows these properties to be populated during JSON deserialization.
     * This is useful for handling flexible or additional data that may vary between requests.
     */
    @JsonAnySetter
    private Map<String, Object> properties;
}