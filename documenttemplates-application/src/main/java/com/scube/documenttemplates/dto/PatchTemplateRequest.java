package com.scube.documenttemplates.dto;

import com.fasterxml.jackson.annotation.JsonAnySetter;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import java.util.Map;

@Data
@AllArgsConstructor
public class PatchTemplateRequest {
    private MultipartFile file;
    private String name;
    private String fileName;
    private String description;
    private String filetype;

    /**
     * Allows setting additional, unknown JSON properties during deserialization.
     * These properties will be stored in the 'properties' map.
     */
    @JsonAnySetter
    private Map<String, Object> properties;
}