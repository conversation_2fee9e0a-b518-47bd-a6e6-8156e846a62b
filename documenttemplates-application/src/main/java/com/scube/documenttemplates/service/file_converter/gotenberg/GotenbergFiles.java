package com.scube.documenttemplates.service.file_converter.gotenberg;

import com.scube.documenttemplates.CustomMultipartFile;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.web.multipart.MultipartFile;

public class GotenbergFiles extends LinkedMultiValueMap<String, Object> {
    public static GotenbergFiles of(CustomMultipartFile mpfile) {
        return new GotenbergFiles().file(mpfile);
    }

    public GotenbergFiles file(MultipartFile file) {
        this.add("files", file.getResource());
        return this;
    }
}
