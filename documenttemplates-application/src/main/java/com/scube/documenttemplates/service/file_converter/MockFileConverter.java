package com.scube.documenttemplates.service.file_converter;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

@Component
@ConditionalOnProperty(name = "file-converter.type", havingValue = "mock")
public class MockFileConverter implements IFileConverter {
    @Override
    public byte[] convertDocxToPdf(byte[] docBytes) {
        return new byte[0];
    }

    @Override
    public Mono<Void> convertDocxToPdfAsync(byte[] docBytes, String parentType, String parentId) {
        // do nothing
        return Mono.empty();
    }

    @Override
    public byte[] convert(String fromFormat, String toFormat, byte[] docBytes) {
        return new byte[0];
    }
}