package com.scube.documenttemplates.service;

import com.scube.auth.library.ITokenService;
import com.scube.client.document.generated.DocumentServiceConnection;
import com.scube.document.dto.gen_dto.FileUploadResponseDTO;
import com.scube.document.generated.FileUpdateRequest__QueryParams;
import com.scube.document.generated.FileUploadRequestDTO__QueryParams;
import com.scube.document.generated.GetFile__QueryParams;
import com.scube.document.generated.HandleFileUpload__QueryParams;
import com.scube.documenttemplates.CustomMultipartFile;
import com.scube.documenttemplates.model.Template;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.Resource;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.web.multipart.MultipartFile;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import java.util.UUID;

@Service
@Slf4j
@RequiredArgsConstructor
public class FileService {
    private final DocumentServiceConnection documentServiceConnection;
    private final ITokenService tokenService;

    private static final String DOCUMENT_TEMPLATE_PATH = "document_templates/";

    private CharSequence getToken() {
        var token = tokenService.getNewAccessTokenFromCurrentRealm();

        Assert.notNull(token, "Token cannot be null");
        return token;
    }

    private Mono<CharSequence> getTokenAsync() {
        var context = SecurityContextHolder.getContext();
        return Mono.fromCallable(() -> {
                    SecurityContextHolder.setContext(context);
                    return getToken();
                })
                .subscribeOn(Schedulers.boundedElastic());
    }

    @SneakyThrows
    public byte[] getTemplateFile(Template template) {
        return getTemplateFileResponse(template).getBody().getContentAsByteArray();
    }

    public ResponseEntity<Resource> getTemplateFileResponse(Template template) {
        GetFile__QueryParams queryParams = new GetFile__QueryParams();
        queryParams.version(template.getActiveVersion());
        queryParams.preview(false);
        return documentServiceConnection.document().getFileHttp(template.getDocumentUuid(), queryParams, getToken());
    }

    public FileUploadResponseDTO saveTemplateFile(MultipartFile file) {
        FileUploadRequestDTO__QueryParams requiredQueryParams = new FileUploadRequestDTO__QueryParams();
        HandleFileUpload__QueryParams optionalQueryParams = new HandleFileUpload__QueryParams();
        optionalQueryParams.path(DOCUMENT_TEMPLATE_PATH);
        optionalQueryParams.storedWithOriginalName(true);
        requiredQueryParams.file(file);
        return documentServiceConnection.document().handleFileUpload(requiredQueryParams, optionalQueryParams, getToken());
    }

    public Mono<FileUploadResponseDTO> saveFilledTemplateFileAsync(byte[] byteArray, String fileName, String contentType) {
        var multipartFile = new CustomMultipartFile(
                fileName, fileName,
                contentType,
                byteArray
        );

        FileUploadRequestDTO__QueryParams requiredQueryParams = new FileUploadRequestDTO__QueryParams();
        HandleFileUpload__QueryParams optionalQueryParams = new HandleFileUpload__QueryParams();
        requiredQueryParams.file(multipartFile);
        return getTokenAsync()
                .flatMap(token -> documentServiceConnection.document().handleFileUploadAsync(requiredQueryParams, optionalQueryParams, token));
    }

    public FileUploadResponseDTO saveFilledTemplateFile(byte[] byteArray, String fileName, String contentType) {
        return saveFilledTemplateFileAsync(byteArray, fileName, contentType)
                .block();
    }

    public Integer updateFile(UUID uuid, MultipartFile file) {
        FileUpdateRequest__QueryParams queryParams = new FileUpdateRequest__QueryParams();
        queryParams.file(file);
        return documentServiceConnection.document().handleFileUpdate(uuid, queryParams, getToken()).getVersion();
    }

    public String determineContentType(String templateType) {
        return switch (templateType) {
            case "docx" -> "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
            case "pdf" -> "application/pdf";
            case "text", "txt" -> "text/plain";
            case "xlsx" -> "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
            default -> String.valueOf(MediaType.APPLICATION_OCTET_STREAM);
        };
    }

    public void deleteFile(UUID uuid) {
        documentServiceConnection.document().delete(uuid, getToken());
    }
}
