package com.scube.documenttemplates.service.file_converter.gotenberg;

import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.service.annotation.PostExchange;
import reactor.core.publisher.Mono;

public interface GotenbergHttpExchange {
    @PostExchange(value = "/forms/libreoffice/convert", contentType = MediaType.MULTIPART_FORM_DATA_VALUE)
    byte[] convertDocument(@RequestBody GotenbergFiles files);

    @PostExchange(value = "/forms/libreoffice/convert", contentType = MediaType.MULTIPART_FORM_DATA_VALUE)
    Mono<Void> convertDocumentAsync(@RequestBody GotenbergFiles files,
                                    @RequestHeader("Gotenberg-Webhook-Url") String webhookUrl,
                                    @RequestHeader("Gotenberg-Webhook-Error-Url") String webhookErrorUrl,
                                    @RequestHeader("Gotenberg-Webhook-Extra-Http-Headers") String extraHttpHeaders);
}