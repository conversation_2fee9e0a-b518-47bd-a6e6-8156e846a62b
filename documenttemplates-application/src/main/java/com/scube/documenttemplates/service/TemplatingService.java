package com.scube.documenttemplates.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.scube.documenttemplates.service.helper.DocumentTemplateHelperHttpExchange;
import com.scube.documenttemplates.service.helper.ProcessWordTemplateRequest;
import com.scube.documenttemplates.utils.ByteArrayJxlsOutput;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jxls.transform.poi.JxlsPoiTemplateFillerBuilder;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ResponseStatusException;
import reactor.core.publisher.Mono;

import java.util.Iterator;
import java.util.Map;

@Component
@Slf4j
@RequiredArgsConstructor
public class TemplatingService {
    private final ObjectMapper objectMapper;
    private final DocumentTemplateHelperHttpExchange documentTemplateHelperHttpExchange;

    public byte[] processWordTemplate(String data, byte[] fileBytes) {
        return processWordTemplateAsync(data, fileBytes).block();
    }

    public Mono<byte[]> processWordTemplateAsync(String data, byte[] fileBytes) {
        var request = ProcessWordTemplateRequest.builder()
                .data(data)
                .fileBytes(fileBytes)
                .build();

        return documentTemplateHelperHttpExchange.processWordTemplate(request);
    }

    public String processTextTemplate(String data, String templateText) {
        try {
            JsonNode variables = objectMapper.readTree(data);
            Iterator<Map.Entry<String, JsonNode>> itr = variables.fields();
            while (itr.hasNext()) {
                Map.Entry<String, JsonNode> entry = itr.next();
                String value = entry.getValue().isTextual() ? entry.getValue().asText() : entry.getValue().toString();
                templateText = templateText.replace(String.format("{%s}", entry.getKey()), value);
            }
            return templateText;
        } catch (JsonProcessingException e) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Template data must be a valid JSON object");
        }
    }

    public byte[] processExcelTemplate(String data, Resource fileStream) {
        ObjectMapper mapper = new ObjectMapper();

        try {
            JsonNode jsonNode = mapper.readTree(data);
            Map<String, Object> dataMap = mapper.convertValue(jsonNode, new TypeReference<>() {
            });
            ByteArrayJxlsOutput byteArrayJxlsOutput = new ByteArrayJxlsOutput();
            log.debug("Processing template data {}", dataMap);
            JxlsPoiTemplateFillerBuilder.newInstance().withTemplate(fileStream.getInputStream()).build().fill(dataMap, byteArrayJxlsOutput);


            // Return the resulting Excel file as byte array
            return byteArrayJxlsOutput.toByteArray();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}