package com.scube.documenttemplates.service.file_converter.gotenberg;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.ObjectUtils;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.support.WebClientAdapter;
import org.springframework.web.service.invoker.HttpServiceProxyFactory;

@Configuration
@ConditionalOnProperty(name = "file-converter.type", havingValue = "gotenberg")
public class GotenBergHttpExchangeConfig {

    @Bean
    public GotenbergHttpExchange gotenBergHttpExchange(GotenbergFileConverterProperties properties) {
        if (ObjectUtils.isEmpty(properties.getUrl())) {
            throw new IllegalArgumentException("Gotenberg url is required");
        }
        if (ObjectUtils.isEmpty(properties.getMaxInMemorySize())) {
            throw new IllegalArgumentException("Gotenberg maxInMemorySize is required");
        }
        var bytes = (int) properties.getMaxInMemorySize().toBytes();
        var webClient = WebClient.builder()
                .baseUrl(properties.getUrl())
                .codecs(codecs -> codecs.defaultCodecs().maxInMemorySize(bytes))
                .build();
        return HttpServiceProxyFactory
                .builderFor(WebClientAdapter.create(webClient))
                .build()
                .createClient(GotenbergHttpExchange.class);
    }
}