package com.scube.documenttemplates.service;

import com.scube.documenttemplates.dto.CreateTemplateRequest;
import com.scube.documenttemplates.dto.PatchTemplateRequest;
import com.scube.documenttemplates.dto.TemplateDto;
import com.scube.documenttemplates.mapper.TemplateMapper;
import com.scube.documenttemplates.model.Template;
import com.scube.documenttemplates.repository.TemplateRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.web.server.ResponseStatusException;

import java.util.List;
import java.util.UUID;

@Service
@Slf4j
@RequiredArgsConstructor
public class DocumentTemplateService {
    private final FileService fileService;
    private final TemplatingService templatingService;
    private final TemplateRepository templateRepository;
    private final TemplateMapper templateMapper;

    public String fillTextTemplate(String data, String nameKey) {
        Template template = getTemplate(nameKey);

        byte[] fileBytes = fileService.getTemplateFile(template);

        return templatingService.processTextTemplate(data, new String(fileBytes));
    }

    public TemplateDto createTemplate(CreateTemplateRequest request) {
        if (templateRepository.existsByNameKey(request.getNameKey())) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Template with nameKey: " + request.getNameKey() + " already exists");
        }
        if (ObjectUtils.isEmpty(request.getFile())) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "File is required");
        }
        Template template = new Template(request);
        var uploadResponse = fileService.saveTemplateFile(request.getFile());
        template.setDocumentUuid(uploadResponse.getDocumentUUID());
        template.setCurrentVersion(1);
        template.setActiveVersion(1);
        return templateMapper.toDto(templateRepository.save(template));
    }

    public List<TemplateDto> getTemplatesByCategory(String category) {
        return templateMapper.toDto(templateRepository.findByCategory(category));
    }

    public List<TemplateDto> getAllTemplates() {
        return templateMapper.toDto(templateRepository.findAll());
    }

    @Transactional
    public ResponseEntity<Resource> downloadTemplate(String templateNameKey) {
        Template template = getTemplate(templateNameKey);

        return downloadTemplate(template);
    }

    @Transactional
    public ResponseEntity<Resource> downloadTemplate(Template template) {
        return fileService.getTemplateFileResponse(template);
    }

    @Transactional
    public TemplateDto patchTemplate(String templateNameKey, PatchTemplateRequest request) {
        Template template = getTemplate(templateNameKey);

        return patchTemplate(template, request);
    }

    @Transactional
    public TemplateDto patchTemplate(Template template, PatchTemplateRequest request) {
        template.patch(request);

        if (!ObjectUtils.isEmpty(request.getFile())) {
            var version = fileService.updateFile(template.getDocumentUuid(), request.getFile());
            template.setCurrentVersion(version);
            template.setActiveVersion(version);
        }

        return templateMapper.toDto(templateRepository.save(template));
    }

    @Transactional
    public Template getTemplate(String templateNameKey) {
        return templateRepository.findByNameKey(templateNameKey)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Template with nameKey: " + templateNameKey + " not found"));
    }

    public TemplateDto getTemplateDto(UUID templateUUID) {
        return templateMapper.toDto(templateRepository.findByUuidOrThrow(templateUUID));
    }

    public TemplateDto getTemplateDto(String nameKey) {
        return templateMapper.toDto(getTemplate(nameKey));
    }

    @Transactional
    public void setActiveVersion(UUID templateUuid, Integer version) {
        Template template = templateRepository.findByUuidOrThrow(templateUuid);
        template.setActiveVersion(version);
        templateRepository.save(template);
    }
}