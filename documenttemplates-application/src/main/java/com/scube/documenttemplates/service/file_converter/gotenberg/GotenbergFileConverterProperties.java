package com.scube.documenttemplates.service.file_converter.gotenberg;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.unit.DataSize;

@Data
@Configuration
@ConfigurationProperties(prefix = "file-converter.gotenberg")
public class GotenbergFileConverterProperties {
    private String url;
    private DataSize maxInMemorySize = DataSize.ofMegabytes(10);

    private String webhookUrl;
    private String webhookErrorUrl;
}