package com.scube.documenttemplates.service.helper;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.Assert;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.support.WebClientAdapter;
import org.springframework.web.service.invoker.HttpServiceProxyFactory;

@Configuration
public class DocumentTemplateHelperConfig {
    private final WebClient webClient;

    public DocumentTemplateHelperConfig(DocumentTemplateHelperProperties properties) {
        Assert.notNull(properties.getUrl(), "docHelperUrl must not be null");
        var bytes = (int) properties.getMaxInMemorySize().toBytes();
        webClient = WebClient.builder()
                .codecs(codecs -> codecs.defaultCodecs().maxInMemorySize(bytes))
                .baseUrl(properties.getUrl())
                .build();
    }

    @Bean
    public DocumentTemplateHelperHttpExchange documentTemplateHelperHttpExchange() {
        return HttpServiceProxyFactory
                .builderFor(WebClientAdapter.create(webClient))
                .build()
                .createClient(DocumentTemplateHelperHttpExchange.class);
    }
}
