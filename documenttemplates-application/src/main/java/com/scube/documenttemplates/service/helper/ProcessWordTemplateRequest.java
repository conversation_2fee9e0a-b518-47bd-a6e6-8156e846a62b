package com.scube.documenttemplates.service.helper;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ProcessWordTemplateRequest {
    @JsonProperty("data")
    private String data;

    @JsonProperty("file")
    private byte[] fileBytes;
}