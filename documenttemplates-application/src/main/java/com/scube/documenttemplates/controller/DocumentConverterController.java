package com.scube.documenttemplates.controller;

import com.scube.client.ServiceUrlConstant;
import com.scube.client.annotation.GenerateHttpExchange;
import com.scube.documenttemplates.permission.Permissions;
import com.scube.documenttemplates.service.file_converter.IFileConverter;
import jakarta.annotation.security.RolesAllowed;
import jakarta.validation.constraints.Max;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping("document-converter")
@AllArgsConstructor
@GenerateHttpExchange(value = ServiceUrlConstant.DOCUMENT_TEMPLATE_SERVICE)
@Validated
public class DocumentConverterController {
    private final IFileConverter fileConverter;

    @PostMapping(value = "{fromFormat}/{toFormat}", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @RolesAllowed(Permissions.DocumentConverter.CONVERT_DOCUMENT)
    public ResponseEntity<Resource> convertDocument(@PathVariable @Max(255) String fromFormat, @PathVariable @Max(255) String toFormat, @RequestPart("file") byte[] docBytes) {
        var startTime = System.currentTimeMillis();
        var convertedBytes = fileConverter.convert(fromFormat, toFormat, docBytes);
        log.info("Finished converting {} to {} in {} ms", fromFormat, toFormat, System.currentTimeMillis() - startTime);
        return ResponseEntity.ok().body(new ByteArrayResource(convertedBytes));
    }
}
