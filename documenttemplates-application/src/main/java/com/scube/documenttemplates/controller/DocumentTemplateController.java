package com.scube.documenttemplates.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.scube.client.ServiceUrlConstant;
import com.scube.client.annotation.GenerateHttpExchange;
import com.scube.documenttemplates.dto.*;
import com.scube.documenttemplates.permission.Permissions;
import com.scube.documenttemplates.service.DocumentTemplateService;
import com.scube.documenttemplates.strategy.template_builder.PdfTemplateBuilder;
import jakarta.annotation.security.RolesAllowed;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.ObjectUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;
import java.util.UUID;

@RestController
@RequestMapping("templates")
@AllArgsConstructor
@GenerateHttpExchange(value = ServiceUrlConstant.DOCUMENT_TEMPLATE_SERVICE)
@Validated
public class DocumentTemplateController {
    private DocumentTemplateService templateService;
    private final PdfTemplateBuilder pdfTemplateBuilder;
    private final ObjectMapper objectMapper;

    @GetMapping(value = "/{templateNameKey}/download")
    @ResponseStatus(HttpStatus.OK)
    @RolesAllowed(Permissions.DocumentTemplate.DOWNLOAD)
    public ResponseEntity<Resource> download(@PathVariable @Size(max = 255) String templateNameKey) {
        return templateService.downloadTemplate(templateNameKey);
    }

    @PostMapping(value = "/{templateNameKey}/fill")
    @RolesAllowed(Permissions.DocumentTemplate.FILL_TEMPLATE)
    public FillTemplateResponse fillTemplate(@PathVariable @Size(max = 255) String templateNameKey, @RequestBody JsonNode data) {
        return new FillTemplateResponse(pdfTemplateBuilder.fillFileTemplate(String.valueOf(data), templateNameKey));
    }

    @PostMapping(value = "/{templateNameKey}/fill-text")
    @RolesAllowed(Permissions.DocumentTemplate.FILL_TEMPLATE)
    public FillTextTemplateResponse fillTextTemplate(@PathVariable @Size(max = 255) String templateNameKey, @RequestBody JsonNode data) {
        return new FillTextTemplateResponse(templateService.fillTextTemplate(String.valueOf(data), templateNameKey));
    }

    @PostMapping(value = "/fill", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @RolesAllowed(Permissions.DocumentTemplate.FILL)
    public FillTemplateResponse fill(@NotNull @RequestPart MultipartFile template, @NotEmpty @RequestPart String data) {
        return new FillTemplateResponse(pdfTemplateBuilder.fillFileTemplate(data, template));
    }

    @PostMapping(value = "/{templateNameKey}/fillAndDownload")
    @RolesAllowed(Permissions.DocumentTemplate.TEST_FILL_TEMPLATE)
    public ResponseEntity<Resource> testFillTemplate(@PathVariable @Size(max = 255) String templateNameKey, @RequestBody JsonNode data) {
        return pdfTemplateBuilder.testFillTemplate(String.valueOf(data), templateNameKey);
    }

    @PatchMapping(value = "/{templateNameKey}", consumes = "multipart/form-data")
    @RolesAllowed(Permissions.DocumentTemplate.UPDATE_TEMPLATE)
    public TemplateDto updateTemplate(@PathVariable @Size(max = 255) String templateNameKey,
                                      @RequestParam Map<String, String> params,
                                      @RequestParam Map<String, MultipartFile> files) throws JsonProcessingException {
        var paramJson = objectMapper.writeValueAsString(params);
        PatchTemplateRequest request = objectMapper.readValue(paramJson, PatchTemplateRequest.class);
        if (!ObjectUtils.isEmpty(files)) {
            var file = files.values().stream()
                    .findFirst()
                    .orElse(null);
            request.setFile(file);
        }
        return templateService.patchTemplate(templateNameKey, request);
    }

    @PostMapping(consumes = "multipart/form-data")
    @ResponseStatus(HttpStatus.CREATED)
    @RolesAllowed(Permissions.DocumentTemplate.CREATE_TEMPLATE)
    public TemplateDto createTemplate(@RequestParam Map<String, String> params,
                                      @RequestParam Map<String, MultipartFile> files) throws JsonProcessingException {
        var paramJson = objectMapper.writeValueAsString(params);
        CreateTemplateRequest request = objectMapper.readValue(paramJson, CreateTemplateRequest.class);
        if (!ObjectUtils.isEmpty(files)) {
            var file = files.values().stream()
                    .findFirst()
                    .orElse(null);
            request.setFile(file);
        }
        return templateService.createTemplate(request);
    }

    @PatchMapping(value = "/{templateNameKey}/patch-form-model-attribute", consumes = "multipart/form-data")
    @RolesAllowed(Permissions.DocumentTemplate.UPDATE_TEMPLATE)
    public TemplateDto updateTemplate(@PathVariable @Size(max = 255) String templateNameKey, @RequestBody @ModelAttribute PatchTemplateRequest request) {
        return templateService.patchTemplate(templateNameKey, request);
    }

    @PostMapping(value = "create-from-model-attribute", consumes = "multipart/form-data")
    @ResponseStatus(HttpStatus.CREATED)
    @RolesAllowed(Permissions.DocumentTemplate.CREATE_TEMPLATE)
    public TemplateDto createTemplateFromModelAttribute(@RequestBody @ModelAttribute CreateTemplateRequest request) {
        return templateService.createTemplate(request);
    }

    @GetMapping("/{templateUuid}")
    @RolesAllowed(Permissions.DocumentTemplate.GET_TEMPLATE)
    public TemplateDto getTemplate(@PathVariable UUID templateUuid) {
        return templateService.getTemplateDto(templateUuid);
    }

    @GetMapping("/nameKey/{templateNameKey}")
    @RolesAllowed(Permissions.DocumentTemplate.GET_TEMPLATE_BY_NAME_KEY)
    public TemplateDto getTemplateByNameKey(@PathVariable @Size(max = 255) String templateNameKey) {
        return templateService.getTemplateDto(templateNameKey);
    }

    @GetMapping("/categories/{category}")
    @RolesAllowed(Permissions.DocumentTemplate.GET_TEMPLATES_BY_CATEGORY)
    public List<TemplateDto> getTemplatesByCategory(@PathVariable @Size(max = 255) String category) {
        return templateService.getTemplatesByCategory(category);
    }

    @GetMapping
    @RolesAllowed(Permissions.DocumentTemplate.GET_ALL_TEMPLATES)
    public List<TemplateDto> getAllTemplates() {
        return templateService.getAllTemplates();
    }

    @PostMapping("/{templateUuid}/set-active-version/{version}")
    @ResponseStatus(HttpStatus.OK)
    @RolesAllowed(Permissions.DocumentTemplate.SET_ACTIVE_VERSION)
    public void setActiveVersion(@PathVariable UUID templateUuid, @PathVariable @Min(1) Integer version) {
        templateService.setActiveVersion(templateUuid, version);
    }
}