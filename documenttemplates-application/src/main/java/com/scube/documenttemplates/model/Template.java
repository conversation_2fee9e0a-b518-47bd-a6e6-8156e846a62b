package com.scube.documenttemplates.model;

import com.scube.audit.auditable.entity.AuditableEntity;
import com.scube.documenttemplates.dto.CreateTemplateRequest;
import com.scube.documenttemplates.dto.PatchTemplateRequest;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.envers.Audited;
import org.springframework.lang.Nullable;
import org.springframework.web.multipart.MultipartFile;

import java.util.Objects;
import java.util.Optional;
import java.util.UUID;

import static org.apache.commons.lang3.StringUtils.isNotBlank;

@Getter
@Setter
@Entity
@NoArgsConstructor
@Table(name = Template.TABLE_NAME)
@Audited
public class Template extends AuditableEntity {
    public static final String TABLE_NAME = "template";

    @Size(max = 255)
    private String category;

    @Size(max = 255)
    private String subCategory;

    private UUID documentUuid;

    @Size(max = 255)
    @Column(unique = true, nullable = false, name = "name_key")
    private String nameKey;

    @Size(max = 255)
    @Column(nullable = false, name = "file_name")
    private String fileName;

    @Size(max = 255)
    @Column(nullable = false)
    private String name;

    @Size(max = 255)
    private String description;

    @Size(max = 255)
    private String filetype;

    private Integer currentVersion;
    private Integer activeVersion;

    public Template(CreateTemplateRequest request) {
        nameKey = request.getNameKey();
        fileName = determineFileName(request.getFileName(), request.getFile());
        name = request.getName();
        description = request.getDescription();
        filetype = determineFileType(request.getFiletype());
        if (Objects.nonNull(request.getProperties())) {
            this.setProperties(request.getProperties());
        }
    }

    public void patch(PatchTemplateRequest request) {
        var fn = determineFileName(request.getFileName(), request.getFile());
        if (isNotBlank(fn))
            this.fileName = fn;
        if (isNotBlank(request.getName()))
            this.name = request.getName();
        if (isNotBlank(request.getDescription()))
            this.description = request.getDescription();
        if (isNotBlank(request.getFiletype()))
            this.filetype = determineFileType(request.getFiletype());
        if (Objects.nonNull(request.getProperties())) {
            this.setProperties(request.getProperties());
        }
    }

    private static String determineFileName(String fileName, @Nullable MultipartFile file) {
        return Optional.ofNullable(fileName)
                .or(() -> Optional.ofNullable(file)
                        .map(MultipartFile::getOriginalFilename))
                .orElse(null);
    }

    private String determineFileType(String filetype) {
        return Optional.ofNullable(filetype).orElseGet(() -> {
            if (fileName != null && fileName.contains(".")) {
                return fileName.substring(fileName.lastIndexOf('.') + 1);
            }
            return null;
        });
    }
}
