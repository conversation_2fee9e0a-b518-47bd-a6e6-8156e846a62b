package com.scube.documenttemplates.utils;

import org.jxls.builder.JxlsOutput;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStream;

public class ByteArrayJxlsOutput implements JxlsOutput {
    private final ByteArrayOutputStream byteArrayOutputStream;

    public ByteArrayJxlsOutput() {
        this.byteArrayOutputStream = new ByteArrayOutputStream();
    }

    @Override
    public OutputStream getOutputStream() throws IOException {
        return byteArrayOutputStream;
    }

    public byte[] toByteArray() {
        return byteArrayOutputStream.toByteArray();
    }
}