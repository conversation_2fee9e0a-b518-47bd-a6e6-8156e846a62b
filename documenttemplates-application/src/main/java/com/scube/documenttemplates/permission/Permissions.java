package com.scube.documenttemplates.permission;

/**
 * Generated DO NOT MODIFY!
 * date: 2025-03-21T17:17:08.840661500Z
 */
public class Permissions {
    private Permissions() {
    }

    /**
     * Generated DO NOT MODIFY!
     * date: 2025-03-21T17:17:08.842218Z
     */
    public static class Permission {
        public static final String SEED_ROLES_TO_ALL_REALMS = "document-templates-service-permissions-seed-roles-to-all-realms";

        public static final String SEED_ROLES_BY_REALM = "document-templates-service-permissions-seed-roles-by-realm";

        private Permission() {
        }
    }

    /**
     * Generated DO NOT MODIFY!
     * date: 2025-03-21T17:17:08.855493700Z
     */
    public static class DocumentTemplate {
        public static final String DOWNLOAD = "document-templates-service-templates-download";

        public static final String FILL_TEMPLATE = "document-templates-service-templates-fill-template";

        public static final String FILL_TEXT_TEMPLATE = "document-templates-service-templates-fill-text-template";

        public static final String FILL = "document-templates-service-templates-fill";

        public static final String TEST_FILL_TEMPLATE = "document-templates-service-templates-test-fill-template";

        public static final String UPDATE_TEMPLATE = "document-templates-service-templates-update-template";

        public static final String CREATE_TEMPLATE = "document-templates-service-templates-create-template";

        public static final String GET_TEMPLATE = "document-templates-service-templates-get-template";

        public static final String GET_TEMPLATE_BY_NAME_KEY = "document-templates-service-templates-get-template-by-name-key";

        public static final String GET_TEMPLATES_BY_CATEGORY = "document-templates-service-templates-get-templates-by-category";

        public static final String GET_ALL_TEMPLATES = "document-templates-service-templates-get-all-templates";

        public static final String SET_ACTIVE_VERSION = "document-templates-service-templates-set-active-version";

        private DocumentTemplate() {
        }
    }

    /**
     * Generated DO NOT MODIFY!
     * date: 2025-03-21T17:17:08.856525Z
     */
    public static class DocumentConverter {
        public static final String CONVERT_DOCUMENT = "document-templates-service-document-converter-convert-document";

        private DocumentConverter() {
        }
    }

    /**
     * Generated DO NOT MODIFY!
     * date: 2025-03-21T17:17:08.856525Z
     */
    public static class GotenbergWebhook {
        public static final String HANDLE_WEBHOOK = "document-templates-service-webhook-gotenberg-handle-webhook";

        public static final String HANDLE_WEBHOOK_ERROR = "document-templates-service-webhook-gotenberg-handle-webhook-error";

        private GotenbergWebhook() {
        }
    }
}
