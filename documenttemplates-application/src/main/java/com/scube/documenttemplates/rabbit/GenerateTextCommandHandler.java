package com.scube.documenttemplates.rabbit;

import com.fasterxml.jackson.databind.JsonNode;
import com.scube.documenttemplates.service.DocumentTemplateService;
import com.scube.rabbit.core.RabbitResult;
import com.scube.rabbit.core.fanout.IRabbitFanoutPubSubRpc;
import com.scube.rabbit.core.fanout.subscriber.FanoutListenerRpc;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class GenerateTextCommandHandler extends FanoutListenerRpc<GenerateTextCommandHandler.GenerateTextCommand, String> {
    private final DocumentTemplateService templateService;

    public RabbitResult<String> consume(GenerateTextCommand generateTextCommand) {
        return RabbitResult.of(() ->
                templateService.fillTextTemplate(String.valueOf(generateTextCommand.getData()), generateTextCommand.getNameKey())
        );
    }

    @Data
    public static class GenerateTextCommand implements IRabbitFanoutPubSubRpc<String> {
        private JsonNode data;
        private String nameKey;
    }
}