<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="Ben" id="1711558178620-40">
        <addColumn tableName="audit_log_template">
            <column name="current_version" type="INTEGER"/>
            <column name="active_version" type="INTEGER"/>
        </addColumn>
    </changeSet>
    <changeSet author="Ben" id="1711558178620-41">
        <addColumn tableName="template">
            <column name="current_version" type="INTEGER"/>
            <column name="active_version" type="INTEGER"/>
        </addColumn>
    </changeSet>
    <changeSet author="Ben" id="1711558178620-42">
        <sql>
            update template set current_version = 1, active_version = 1;
            update audit_log_template set current_version = 1, active_version = 1;
        </sql>
    </changeSet>
</databaseChangeLog>
