<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="postgres (generated)" id="1711558178620-40">
        <addColumn tableName="audit_log_template">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711558178620-41">
        <addColumn tableName="template">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711558178620-42">
        <addColumn tableName="audit_log_template">
            <column defaultValueComputed="gen_random_uuid()" name="template_uuid" type="uuid"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711558178620-43">
        <addColumn tableName="template">
            <column defaultValueComputed="gen_random_uuid()" name="template_uuid" type="uuid">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711558178620-44">
        <addForeignKeyConstraint baseColumnNames="revision_id" baseTableName="audit_log_scheduler" constraintName="fkggi0jubkjahhqj63qb2hyn8kb" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="audit_log_revision_id" referencedTableName="audit_log_revision" validate="true"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711558178620-45">
        <addForeignKeyConstraint baseColumnNames="revision_id" baseTableName="audit_log_property_type" constraintName="fkph3vvdjiaedcco923ivk6qkxc" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="audit_log_revision_id" referencedTableName="audit_log_revision" validate="true"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711558178620-46">
        <dropUniqueConstraint constraintName="audit_log_property_type_property_type_uuid_key" tableName="audit_log_property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711558178620-47">
        <dropColumn columnName="created_by" tableName="audit_log_scheduler"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711558178620-48">
        <dropColumn columnName="created_date" tableName="audit_log_scheduler"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711558178620-1">
        <modifyDataType columnName="created_by" newDataType="varchar(255)" tableName="property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711558178620-2">
        <modifyDataType columnName="created_by" newDataType="varchar(255)" tableName="scheduler"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711558178620-3">
        <modifyDataType columnName="created_by" newDataType="varchar(255)" tableName="template"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711558178620-4">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711558178620-5">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="scheduler"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711558178620-6">
        <addNotNullConstraint columnDataType="timestamp" columnName="created_date" tableName="scheduler" validate="true"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711558178620-7">
        <dropNotNullConstraint columnDataType="varchar(255)" columnName="file_name" tableName="audit_log_template"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711558178620-8">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(255)" tableName="audit_log_property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711558178620-9">
        <dropNotNullConstraint columnDataType="varchar(255)" columnName="last_modified_by" tableName="audit_log_property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711558178620-10">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(255)" tableName="audit_log_scheduler"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711558178620-11">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(255)" tableName="property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711558178620-12">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(255)" tableName="scheduler"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711558178620-13">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(255)" tableName="template"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711558178620-14">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="audit_log_property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711558178620-15">
        <dropNotNullConstraint columnDataType="timestamp" columnName="last_modified_date" tableName="audit_log_property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711558178620-16">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="audit_log_scheduler"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711558178620-17">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711558178620-18">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="scheduler"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711558178620-19">
        <addNotNullConstraint columnDataType="timestamp" columnName="last_modified_date" tableName="scheduler" validate="true"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711558178620-20">
        <dropNotNullConstraint columnDataType="varchar(255)" columnName="name" tableName="audit_log_template"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711558178620-21">
        <modifyDataType columnName="property_name" newDataType="varchar(250)" tableName="audit_log_property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711558178620-22">
        <dropNotNullConstraint columnDataType="varchar(250)" columnName="property_name" tableName="audit_log_property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711558178620-23">
        <dropDefaultValue columnDataType="varchar(250)" columnName="property_name" tableName="audit_log_property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711558178620-24">
        <modifyDataType columnName="property_name" newDataType="varchar(250)" tableName="property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711558178620-25">
        <dropDefaultValue columnDataType="varchar(250)" columnName="property_name" tableName="property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711558178620-26">
        <dropNotNullConstraint columnDataType="varchar(255)" columnName="property_type" tableName="audit_log_property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711558178620-27">
        <dropDefaultValue columnDataType="varchar(255)" columnName="property_type" tableName="audit_log_property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711558178620-28">
        <dropDefaultValue columnDataType="varchar(255)" columnName="property_type" tableName="property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711558178620-29">
        <dropNotNullConstraint columnDataType="uuid" columnName="property_type_uuid" tableName="audit_log_property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711558178620-30">
        <addDefaultValue columnDataType="uuid" columnName="property_type_uuid" defaultValueComputed="gen_random_uuid()" tableName="audit_log_property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711558178620-31">
        <addDefaultValue columnDataType="uuid" columnName="property_type_uuid" defaultValueComputed="gen_random_uuid()" tableName="property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711558178620-32">
        <modifyDataType columnName="revision_id" newDataType="int" tableName="audit_log_property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711558178620-33">
        <dropNotNullConstraint columnDataType="smallint(5)" columnName="revision_type" tableName="audit_log_property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711558178620-34">
        <dropNotNullConstraint columnDataType="uuid" columnName="scheduler_uuid" tableName="scheduler"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711558178620-35">
        <dropNotNullConstraint columnDataType="varchar(100)" columnName="table_name" tableName="audit_log_property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711558178620-36">
        <dropDefaultValue columnDataType="varchar(100)" columnName="table_name" tableName="audit_log_property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711558178620-37">
        <dropDefaultValue columnDataType="varchar(100)" columnName="table_name" tableName="property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711558178620-38">
        <addUniqueConstraint columnNames="table_name, property_name" constraintName="property_type_unique" tableName="property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711558178620-39">
        <addUniqueConstraint columnNames="template_uuid" constraintName="uk_es7x87hh0ctme7uuynscugam0" tableName="template"/>
    </changeSet>
</databaseChangeLog>
