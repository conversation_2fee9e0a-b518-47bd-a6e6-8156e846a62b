<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.4.xsd">
    <!--@formatter:off-->
    <include file="classpath:db/changelog/scripts/00000000000000_initial_schema.xml" relativeToChangelogFile="false"/>
    <include file="classpath:db/changelog/scripts/add_nameKey_and_fileType.xml" relativeToChangelogFile="false"/>
    <include file="classpath:db/changelog/scripts/00000000000001_audit_logging.xml" relativeToChangelogFile="false"/>
    <include file="classpath:db/changelog/scripts/00000000000002_audit_logging.xml" relativeToChangelogFile="false"/>
    <include file="classpath:db/changelog/scripts/00000000000003_audit_tables.xml" relativeToChangelogFile="false"/>
    <include file="classpath:db/changelog/scripts/00000000000004_history_tables.xml" relativeToChangelogFile="false"/>
    <include file="classpath:db/changelog/scripts/00000000000005_remove_sequence_for_identity.xml" relativeToChangelogFile="false"/>
    <include file="classpath:db/changelog/scripts/00000000000006_make_name_key_not_nullable.xml" relativeToChangelogFile="false"/>
    <include file="classpath:db/changelog/scripts/00000000000007_add_scheduler_table_tbl.xml" relativeToChangelogFile="false"/>
    <include file="classpath:db/changelog/scripts/00000000000008_update_audit_columns_to_clerk.xml" relativeToChangelogFile="false"/>
    <include file="classpath:db/changelog/scripts/00000000000009_add_template_columns.xml" relativeToChangelogFile="false"/>
    <include file="classpath:db/changelog/scripts/property_type.xml" relativeToChangelogFile="false"/>
    <include file="classpath:db/changelog/scripts/000000000000010_custom_fields_as_json.xml" relativeToChangelogFile="false"/>
    <include file="classpath:db/changelog/scripts/000000000000011_add_document_version.xml" relativeToChangelogFile="false"/>
    <include file="classpath:db/changelog/scripts/000000000000012_shedlock.xml" relativeToChangelogFile="false"/>
    <include file="classpath:db/changelog/scripts/revision_entity.xml" relativeToChangelogFile="false"/>
</databaseChangeLog>