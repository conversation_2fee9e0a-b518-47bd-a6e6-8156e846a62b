package com.scube.coordinator.features.fees.strategies;

import com.scube.calculation.dto.gen_dto.CartInvoiceItem;
import com.scube.client.calculation.generated.CalculationServiceConnection;
import com.scube.client.license.generated.LicenseServiceConnection;
import com.scube.coordinator.features.cart.CartService;
import com.scube.coordinator.features.cart.dto.AddItemToCartRequest;
import com.scube.coordinator.features.fees.CartItemFeeUtils;
import com.scube.coordinator.features.fees.dto.AddFeeOnCartItemRequest;
import com.scube.coordinator.features.fees.dto.RemoveFeeOnCartItemRequest;
import com.scube.coordinator.features.fees.dto.UpdateFeeOnCartItemRequest;
import com.scube.coordinator.features.payment.dto.CalculationObserverEvent;
import com.scube.licensing.features.license.dto.gen_dto.LicenseActivityAddFeeRequest;
import com.scube.licensing.features.license.dto.gen_dto.LicenseActivityFeeRemoveRequest;
import com.scube.licensing.features.license.dto.gen_dto.LicenseActivityFeeUpdateRequest;
import com.scube.rabbit.core.AmqpGateway;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.List;
import java.util.Map;
import java.util.UUID;

@Slf4j
@Service("licenseCartItemStrategy")
@RequiredArgsConstructor
public class LicenseCartItemStrategy implements ICartItemStrategy {
    private final LicenseServiceConnection licenseServiceConnection;
    private final CalculationServiceConnection calculationServiceConnection;
    private final CartService cartService;
    private final AmqpGateway amqpGateway;

    @Override
    public Object updateFee(CartInvoiceItem cartItem, UpdateFeeOnCartItemRequest request) {
        var licenseActivityFeeUpdateRequest = new LicenseActivityFeeUpdateRequest(request.getFeeAmount(), request.getReason());
        var licenseActivityFeeId = CartItemFeeUtils.getLicenseActivityFeeId(request.getFeeId(), cartItem);
        licenseServiceConnection.fees().updateLicenseFee(licenseActivityFeeId, licenseActivityFeeUpdateRequest);

        refreshCartItem(cartItem);
        return null;
    }

    @Override
    public Object addFee(CartInvoiceItem cartItem, AddFeeOnCartItemRequest request) {
        var licenseActivityAddFeeRequest = new LicenseActivityAddFeeRequest(request.getFeeCode(), request.getFeeAmount(), request.getReason());
        var activityId = getActivityId(request.getAdditionalFields());
        licenseServiceConnection.fees().addLicenseFee(activityId, licenseActivityAddFeeRequest);

        refreshCartItem(cartItem);
        return null;
    }

    @Override
    public Object removeFee(CartInvoiceItem cartItem, RemoveFeeOnCartItemRequest request) {
        var licenseActivityFeeRemoveRequest = new LicenseActivityFeeRemoveRequest(request.getReason());
        var licenseActivityFeeId = CartItemFeeUtils.getLicenseActivityFeeId(request.getFeeId(), cartItem);
        licenseServiceConnection.fees().removeLicenseFee(licenseActivityFeeId, licenseActivityFeeRemoveRequest);

        refreshCartItem(cartItem);
        return null;
    }

    private UUID getActivityId(Map<String, Object> additionalFields) {
        var activityId = additionalFields.get("activityId");
        if (ObjectUtils.isEmpty(activityId)) return null;
        return UUID.fromString(activityId.toString());
    }

    private void refreshCartItem(CartInvoiceItem cartItem) {
        calculationServiceConnection.cart().removeItem(cartItem.getCartId(), cartItem.getCartItemId());
        cartService.addToCart(cartItem.getCartId(), new AddItemToCartRequest(cartItem.getItemType(), cartItem.getItemId()), false);
        amqpGateway.publish(new CalculationObserverEvent("refresh", new CalculationRefreshObserverPayload(List.of(cartItem.getItemId()), cartItem.getCartId())));
    }
    private record CalculationRefreshObserverPayload(List<UUID> entityIds, UUID currentCartId){}
}