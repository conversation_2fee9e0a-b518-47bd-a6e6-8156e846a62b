package com.scube.calculation.repository;

import com.scube.audit.auditable.repositories.AuditableEntityRepository;
import com.scube.calculation.enums.FeeType;
import com.scube.calculation.model.Fee;
import jakarta.validation.constraints.Size;

import java.util.List;
import java.util.Optional;

public interface FeeRepository extends AuditableEntityRepository<Fee, Long> {
    public Optional<Fee> findByKey(@Size(max = 255) String key);

    List<Fee> findAllByKeyIn(List<String> keys);

    List<Fee> findAllByOperation(FeeType operation);
}
