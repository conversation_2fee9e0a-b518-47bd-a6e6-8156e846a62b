package com.scube.calculation.controller;

import com.c4_soft.springaddons.security.oidc.OpenidClaimSet;
import com.scube.calculation.dto.PageDTO;
import com.scube.calculation.dto.order.OrderInvoiceResponse;
import com.scube.calculation.dto.order.OrderSummaryResponse;
import com.scube.calculation.permission.Permissions;
import com.scube.calculation.service.OrderService;
import com.scube.client.ServiceUrlConstant;
import com.scube.client.annotation.GenerateHttpExchange;
import jakarta.annotation.security.RolesAllowed;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.prepost.PostAuthorize;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.UUID;

@RestController
@RequestMapping("me/order")
@AllArgsConstructor
@GenerateHttpExchange(value = ServiceUrlConstant.CALCULATION_SERVICE)
@Validated
public class LoggedInUserOrderController {
    private final OrderService orderService;

    @PostMapping("cart/{cartId}")
    @RolesAllowed(Permissions.LoggedInUserOrder.CREATE_ORDER_FROM_CART)
    @PreAuthorize("isOwnerOfCart(#cartId)")
    public OrderInvoiceResponse createOrderFromCart(@PathVariable UUID cartId) {
        return orderService.makeOrderFromCartWithResponse(cartId);
    }

    @PostMapping("{orderId}/cancel")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @RolesAllowed(Permissions.LoggedInUserOrder.CANCEL_ORDER)
    @PreAuthorize("isOwnerOfOrder(#orderId)")
    public void cancelOrder(@PathVariable UUID orderId) {
        orderService.cancelOrder(orderId);
    }

    @GetMapping("{orderId}")
    @RolesAllowed(Permissions.LoggedInUserOrder.GET_ORDER)
    @PreAuthorize("isOwnerOfOrder(#orderId)")
    public OrderInvoiceResponse getOrder(@PathVariable UUID orderId) {
        return orderService.getOrderInvoice(orderId);
    }

    @GetMapping
    @RolesAllowed(Permissions.LoggedInUserOrder.LIST_ORDERS)
    @PostAuthorize("returnObject.items.![userId].contains(#jwt.getSubject())")
    public PageDTO<OrderSummaryResponse> listOrders(@RequestParam(required = false) @Size(max = 255) String status,
                                                    @RequestParam(required = false, defaultValue = "1") int pageNumber,
                                                    @RequestParam(required = false, defaultValue = "10") int pageSize,
                                                    @AuthenticationPrincipal OpenidClaimSet jwt) {
        return orderService.listOrders(jwt.getSubject(), status, pageNumber, pageSize);
    }

    @PostMapping("/{orderId}/rollBackToCart/{cartId}")
    @ResponseStatus(HttpStatus.OK)
    @RolesAllowed(Permissions.LoggedInUserOrder.ROLL_BACK_TO_CART)
    public void rollBackToCart(@PathVariable UUID cartId, @PathVariable UUID orderId) {
        orderService.rollbackOrderToCart(cartId, orderId);
    }
}