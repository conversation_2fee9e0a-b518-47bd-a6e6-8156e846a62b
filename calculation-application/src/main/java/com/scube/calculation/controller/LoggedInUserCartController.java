package com.scube.calculation.controller;

import com.c4_soft.springaddons.security.oidc.OpenidClaimSet;
import com.scube.calculation.dto.*;
import com.scube.calculation.permission.Permissions;
import com.scube.calculation.service.CartService;
import com.scube.client.ServiceUrlConstant;
import com.scube.client.annotation.GenerateHttpExchange;
import jakarta.annotation.security.RolesAllowed;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.prepost.PostAuthorize;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.util.ObjectUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.server.ResponseStatusException;

import java.util.List;
import java.util.UUID;

@RestController
@RequestMapping("me/cart")
@AllArgsConstructor
@GenerateHttpExchange(value = ServiceUrlConstant.CALCULATION_SERVICE)
@Validated
public class LoggedInUserCartController {
    private final CartService cartService;

    @PostMapping("new")
    @ResponseStatus(HttpStatus.CREATED)
    @RolesAllowed(Permissions.LoggedInUserCart.CREATE_CART)
    @PostAuthorize("returnObject == null || returnObject.userId == #jwt.getSubject()")
    public CartInvoiceResponse createCart(@AuthenticationPrincipal OpenidClaimSet jwt) {
        return cartService.createCart(jwt.getSubject());
    }

    @GetMapping("active")
    @RolesAllowed(Permissions.LoggedInUserCart.GET_ACTIVE_CART)
    @PostAuthorize("returnObject == null || returnObject.userId == #jwt.getSubject()")
    public CartInvoiceResponse getActiveCart(@AuthenticationPrincipal OpenidClaimSet jwt,
                                             @RequestParam(required = false, defaultValue = "cardOnline") @Size(max = 255) String paymentMethod) {
        // since they are resident just make sure that the payment method is cardOnline
        // and they can't override it to avoid stripe fees
        if (!paymentMethod.equalsIgnoreCase("cardOnline"))
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Invalid payment method");
        return cartService.getActiveCartInvoice(jwt.getSubject(), paymentMethod);
    }

    @GetMapping
    @RolesAllowed(Permissions.LoggedInUserCart.LIST_CART)
    @PostAuthorize("returnObject == null || returnObject.items.![userId].contains(#jwt.getSubject())")
    public PageDTO<CartSummaryResponse> listCart(@RequestParam(required = false) List<String> statuses,
                                                 @RequestParam(required = false, defaultValue = "1") int pageNumber,
                                                 @RequestParam(required = false, defaultValue = "10") int pageSize,
                                                 @AuthenticationPrincipal OpenidClaimSet jwt) {
        return cartService.listCarts(jwt.getSubject(), statuses, pageNumber, pageSize);
    }

    @GetMapping("{cartId}")
    @RolesAllowed(Permissions.LoggedInUserCart.GET_CART_INVOICE)
    @PreAuthorize("isOwnerOfCart(#cartId)")
    public CartInvoiceResponse getCartInvoice(@PathVariable UUID cartId) {
        return cartService.getCartInvoice(cartId);
    }

    @PostMapping("{cartId}/items")
    @RolesAllowed(Permissions.LoggedInUserCart.ADD_ITEM)
    @PreAuthorize("isOwnerOfCart(#cartId)")
    public CartItemSummary addItem(@PathVariable UUID cartId, @RequestBody AddItemRequest request) {
        return cartService.addItem(cartId, request);
    }

    @DeleteMapping("{cartId}/item/{cartItemId}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @RolesAllowed(Permissions.LoggedInUserCart.REMOVE_ITEM)
    @PreAuthorize("isOwnerOfCart(#cartId)")
    public void removeItem(@PathVariable UUID cartId, @PathVariable long cartItemId) {
        cartService.removeItem(cartId, cartItemId);
    }

    @DeleteMapping("{cartId}/cartItem")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @RolesAllowed(Permissions.LoggedInUserCart.REMOVE_CART_ITEM_BY)
    @PreAuthorize("isOwnerOfCart(#cartId)")
    public void removeCartItemBy(@PathVariable UUID cartId,
                                 @RequestParam(value = "cartItemId", required = false) List<Long> cartItemIds,
                                 @RequestParam(value = "itemId", required = false) List<UUID> itemIds) {
        if (!ObjectUtils.isEmpty(cartItemIds))
            cartService.removeItemByCartItemIds(cartId, cartItemIds);
        else if (!ObjectUtils.isEmpty(itemIds))
            cartService.removeItemByItemIds(cartId, itemIds);
        else
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Either cartItemId or itemId is required");
    }

    @PatchMapping("{cartId}/clear")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @RolesAllowed(Permissions.LoggedInUserCart.CLEAR_CART)
    @PreAuthorize("isOwnerOfCart(#cartId)")
    public void clearCart(@PathVariable UUID cartId) {
        cartService.clearCart(cartId);
    }

    @PatchMapping("clear")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public void clearCart(@AuthenticationPrincipal OpenidClaimSet jwt) {
        var cart = cartService.getActiveCartInvoice(jwt.getSubject(), null);
        if (cart == null) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "Cart not found");
        }
        cartService.clearCart(cart.getCartId());
    }
}