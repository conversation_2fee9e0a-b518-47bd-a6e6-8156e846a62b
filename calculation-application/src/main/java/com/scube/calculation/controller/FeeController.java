package com.scube.calculation.controller;

import com.scube.calculation.dto.UpsertFeeRequest;
import com.scube.calculation.dto.fee.FeeDto;
import com.scube.calculation.mapper.FeeMapper;
import com.scube.calculation.model.Fee;
import com.scube.calculation.permission.Permissions;
import com.scube.calculation.service.FeeService;
import com.scube.client.ServiceUrlConstant;
import com.scube.client.annotation.GenerateHttpExchange;
import jakarta.annotation.security.RolesAllowed;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/fees")
@RequiredArgsConstructor
@GenerateHttpExchange(value = ServiceUrlConstant.CALCULATION_SERVICE)
public class FeeController {
    private final FeeService feeService;
    private final FeeMapper feeMapper;

    @PostMapping
    @RolesAllowed(Permissions.Fee.CREATE_FEE)
    public FeeDto createFee(@RequestBody Fee feeRequest) {
        var fee = feeService.createFee(feeRequest);
        return feeMapper.toDto(fee);
    }

    @PutMapping
    @RolesAllowed(Permissions.Fee.UPSERT_FEES)
    public void upsertFees(@RequestBody List<UpsertFeeRequest> feeRequests) {
        feeService.upsertFees(feeRequests);
    }

    @GetMapping
    @RolesAllowed(Permissions.Fee.GET_ALL_FEES)
    public List<FeeDto> getAllFees() {
        var fees = feeService.getFees();
        return feeMapper.toDto(fees);
    }

    @GetMapping("/query")
    @RolesAllowed(Permissions.Fee.GET_FEES_BY_KEYS)
    public List<FeeDto> getFeesByKeys(@RequestParam List<String> keys) {
        return feeService.getFeesByKey(keys);
    }

    @GetMapping("/manual")
    @RolesAllowed(Permissions.Fee.GET_MANUAL_FEES)
    public List<FeeDto> getManualFees() {
        var fees = feeService.getManualFees();
        return feeMapper.toDto(fees);
    }
}
