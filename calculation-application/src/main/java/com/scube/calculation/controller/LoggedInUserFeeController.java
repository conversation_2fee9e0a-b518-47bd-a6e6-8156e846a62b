package com.scube.calculation.controller;

import com.scube.calculation.dto.fee.FeeDto;
import com.scube.calculation.model.Fee;
import com.scube.calculation.permission.Permissions;
import com.scube.calculation.service.FeeService;
import com.scube.client.ServiceUrlConstant;
import com.scube.client.annotation.GenerateHttpExchange;
import jakarta.annotation.security.RolesAllowed;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/me/fees")
@RequiredArgsConstructor
@GenerateHttpExchange(value = ServiceUrlConstant.CALCULATION_SERVICE)
public class LoggedInUserFeeController {
    private final FeeService feeService;

    @GetMapping
    @RolesAllowed(Permissions.LoggedInUserFee.GET_ALL_FEES)
    public List<Fee> getAllFees() {
        return feeService.getFees();
    }

    @GetMapping("/query")
    @RolesAllowed(Permissions.LoggedInUserFee.GET_FEES_BY_KEYS)
    public List<FeeDto> getFeesByKeys(@RequestParam List<String> keys) {
        return feeService.getFeesByKey(keys);
    }
}