package com.scube.calculation.controller;

import com.c4_soft.springaddons.security.oidc.OpenidClaimSet;
import com.scube.calculation.dto.*;
import com.scube.calculation.permission.Permissions;
import com.scube.calculation.service.CartService;
import com.scube.client.ServiceUrlConstant;
import com.scube.client.annotation.GenerateHttpExchange;
import jakarta.annotation.security.RolesAllowed;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.util.ObjectUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.server.ResponseStatusException;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.UUID;

@RestController
@RequestMapping("cart")
@AllArgsConstructor
@GenerateHttpExchange(value = ServiceUrlConstant.CALCULATION_SERVICE)
@Validated
public class CartController {
    private final CartService cartService;

    @PostMapping("new")
    @ResponseStatus(HttpStatus.CREATED)
    @RolesAllowed(Permissions.Cart.CREATE_CART)
    public CartInvoiceResponse createCart(@AuthenticationPrincipal OpenidClaimSet jwt, @RequestParam(required = false) @Size(max = 255) String userId) {
        if (ObjectUtils.isEmpty(userId))
            userId = jwt.getSubject();
        return cartService.createCart(userId);
    }

    @GetMapping("active")
    @RolesAllowed(Permissions.Cart.GET_ACTIVE_CART)
    public CartInvoiceResponse getActiveCart(@AuthenticationPrincipal OpenidClaimSet jwt, @RequestParam(required = false) @Size(max = 255) String userId, @RequestParam(required = false) @Size(max = 255) String paymentMethod) {
        if (ObjectUtils.isEmpty(userId))
            userId = jwt.getSubject();
        return cartService.getActiveCartInvoice(userId, paymentMethod);
    }

    @GetMapping
    @RolesAllowed(Permissions.Cart.LIST_CARTS)
    public PageDTO<CartSummaryResponse> listCarts(@RequestParam(required = false) @Size(max = 255) String userId,
                                                  @RequestParam(required = false) List<String> statuses,
                                                  @RequestParam(required = false, defaultValue = "1") int pageNumber,
                                                  @RequestParam(required = false, defaultValue = "10") int pageSize,
                                                  @AuthenticationPrincipal OpenidClaimSet jwt) {
        if (ObjectUtils.isEmpty(userId))
            userId = jwt.getSubject();
        return cartService.listCarts(userId, statuses, pageNumber, pageSize);
    }

    @GetMapping("{cartId}")
    @RolesAllowed(Permissions.Cart.GET_CART_INVOICE)
    public CartInvoiceResponse getCartInvoice(@PathVariable UUID cartId) {
        return cartService.getCartInvoice(cartId);
    }

    @PostMapping("{cartId}/items")
    @RolesAllowed(Permissions.Cart.ADD_ITEM)
    public CartItemSummary addItem(@PathVariable UUID cartId, @RequestBody AddItemRequest request) {
        return cartService.addItem(cartId, request);
    }

    @DeleteMapping("{cartId}/item/{cartItemId}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @RolesAllowed(Permissions.Cart.REMOVE_ITEM)
    public void removeItem(@PathVariable UUID cartId, @PathVariable long cartItemId) {
        cartService.removeItem(cartId, cartItemId);
    }

    @GetMapping("/cartItem/{cartItemId}")
    @RolesAllowed(Permissions.Cart.GET_CART_ITEM)
    public CartInvoiceResponse.CartInvoiceItem getCartItem(@PathVariable long cartItemId) {
        return cartService.getCartItem(cartItemId);
    }

    @PatchMapping("/cartItemFee/{cartItemFeeId}/changePrice")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @RolesAllowed(Permissions.Cart.CHANGE_PRICE_ON_CART_ITEM_FEE)
    public void changePriceOnCartItemFee(@PathVariable UUID cartItemFeeId, @RequestBody UpdateCartItemFeeRequest request) {
        cartService.changePriceOnCartItemFee(cartItemFeeId, request);
    }

    @DeleteMapping("/cartItemFee/{cartItemFeeId}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @RolesAllowed(Permissions.Cart.REMOVE_CART_ITEM_FEE)
    public void removeCartItemFee(@PathVariable UUID cartItemFeeId) {
        cartService.removeCartItemFee(cartItemFeeId);
    }

    @PostMapping("cartItem/{cartItemId}/addFee")
    @RolesAllowed(Permissions.Cart.ADD_FEE_ON_CART_ITEM)
    public void addFeeOnCartItem(@PathVariable Long cartItemId, @RequestBody AddFeeOnCartItemRequest request) {
        cartService.addFeeOnCartItem(cartItemId, request);
    }

    @DeleteMapping("{cartId}/cartItem")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @RolesAllowed(Permissions.Cart.REMOVE_CART_ITEM_BY)
    public void removeCartItemBy(@PathVariable UUID cartId,
                                 @RequestParam(value = "cartItemId", required = false) List<Long> cartItemIds,
                                 @RequestParam(value = "itemId", required = false) List<UUID> itemIds) {
        if (!ObjectUtils.isEmpty(cartItemIds))
            cartService.removeItemByCartItemIds(cartId, cartItemIds);
        else if (!ObjectUtils.isEmpty(itemIds))
            cartService.removeItemByItemIds(cartId, itemIds);
        else
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Either cartItemId or itemId is required");
    }

    @PostMapping("invoice")
    @RolesAllowed(Permissions.Cart.GET_PREVIEW_INVOICE)
    public CartInvoiceResponse getPreviewInvoice(@RequestBody List<AddItemRequest> items) {
        return cartService.getPreviewInvoice(items);
    }

    @GetMapping("{cartId}/summary")
    @RolesAllowed(Permissions.Cart.GET_CART_SUMMARY)
    public PayableSummaryResponse getCartSummary(@PathVariable UUID cartId) {
        return cartService.getCartSummary(cartId);
    }

    @PatchMapping("{cartId}/switch")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @RolesAllowed(Permissions.Cart.SWITCH_ACTIVE_CART)
    public void switchActiveCart(@PathVariable UUID cartId, @AuthenticationPrincipal OpenidClaimSet jwt) {
        String userId = jwt.getSubject();
        cartService.switchCart(cartId, userId);
    }

    @PatchMapping("{cartId}/clear")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @RolesAllowed(Permissions.Cart.CLEAR_CART)
    public void clearCart(@PathVariable UUID cartId) {
        cartService.clearCart(cartId);
    }

    @DeleteMapping("{cartId}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @RolesAllowed(Permissions.Cart.DELETE_CART)
    public void deleteCart(@PathVariable UUID cartId) {
        cartService.deleteCart(cartId);
    }

    @GetMapping("filters")
    @ResponseStatus(HttpStatus.OK)
    @RolesAllowed(Permissions.Cart.FIND_CARTS_BY_FILTERS)
    public List<CartInvoiceResponse> findCartsByFilters(@RequestParam(value = "startDate", required = false) LocalDate startDate,
                                                        @RequestParam(value = "endDate", required = false) LocalDate endDate,
                                                        @RequestParam Map<String, String> queryParams) {
        queryParams.put("startDate", startDate.toString());
        queryParams.put("endDate", endDate.toString());
        return cartService.findCartsByFilters(queryParams);
    }

    @DeleteMapping("/cartItemFee/byEntityFee/{entityFeeId}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @RolesAllowed(Permissions.Cart.REMOVE_CART_ITEM_FEE)
    public void removeCartItemFeeByEntityFeeId(@PathVariable @NotNull UUID entityFeeId) {
        cartService.removeCartItemFeeByEntityFeeId(entityFeeId);
    }


    @DeleteMapping("/cartItem/byItemId/{itemId}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @RolesAllowed(Permissions.Cart.REMOVE_CART_ITEM_FEE)
    public void removeCartItemByItemId(@PathVariable @NotNull UUID itemId) {
        cartService.removeCartItemByItemId(itemId);
    }

    @PostMapping("cartItem/byItemId/{itemId}/addFee")
    @RolesAllowed(Permissions.Cart.ADD_FEE_ON_CART_ITEM)
    public void updateFeeOnCartItemByItemId(@PathVariable @NotNull UUID itemId, @RequestBody AddFeeOnCartItemRequest request) {
        cartService.updateFeeOnCartItemByItemId(itemId, request);
    }
}
