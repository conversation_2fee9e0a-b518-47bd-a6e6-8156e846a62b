package com.scube.calculation.service;

import com.scube.calculation.dto.UpsertFeeRequest;
import com.scube.calculation.dto.fee.FeeDto;
import com.scube.calculation.enums.FeeType;
import com.scube.calculation.mapper.FeeMapper;
import com.scube.calculation.model.Fee;
import com.scube.calculation.repository.FeeRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.server.ResponseStatusException;

import java.util.List;
import java.util.Map;
import java.util.Optional;

@Service
@RequiredArgsConstructor
public class FeeService {
    private final FeeRepository feeRepository;
    private final FeeMapper feeMapper;

    public List<Fee> getFees() {
        return feeRepository.findAll();
    }

    public List<FeeDto> getFeesByKey(List<String> keys) {
        return feeRepository.findAllByKeyIn(keys).stream()
                .map(feeMapper::toDto)
                .toList();
    }

    public Fee createFee(Fee fee) {
        if (fee.getAmount() == null)
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Fee amount is required");
        if (fee.getOperation() == null)
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Fee operation (flat, percentage, etc) is required");
        return feeRepository.save(fee);
    }

    @Transactional
    public void upsertFees(List<UpsertFeeRequest> feeRequests) {
        for (UpsertFeeRequest feeRequest : feeRequests) {
            Optional<Fee> existingFee = feeRepository.findByKey(feeRequest.getKey());
            if (existingFee.isPresent()) {
                BeanUtils.copyProperties(feeRequest, existingFee.get(), "id", "uuid");
                setAdditionalProperties(feeRequest, existingFee.get());
                feeRepository.save(existingFee.get());
            } else {
                var fee = feeMapper.toEntityFromRequest(feeRequest);
                setAdditionalProperties(feeRequest, fee);
                feeRepository.save(fee);
            }
        }
    }

    private static void setAdditionalProperties(UpsertFeeRequest feeRequest, Fee existingFee) {
        Optional.ofNullable(feeRequest.getAdditionalProperties()).orElse(Map.of())
                .forEach(existingFee::setProperty);
    }

    public List<Fee> getManualFees() {
        return feeRepository.findAllByOperation(FeeType.MANUAL);
    }
}