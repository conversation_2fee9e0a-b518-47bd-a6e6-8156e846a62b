package com.scube.calculation.rabbit;

import com.scube.calculation.dto.order.OrderInvoiceResponse;
import com.scube.calculation.mapper.OrderMapper;
import com.scube.calculation.repository.OrderRepository;
import com.scube.rabbit.core.AmqpGateway;
import com.scube.rabbit.core.fanout.publisher.IRabbitFanoutPublisher;
import com.scube.rabbit.core.fanout.subscriber.FanoutListener;
import com.scube.rabbit.core.fanout.subscriber.IRabbitFanoutSubscriber;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.server.ResponseStatusException;

import java.util.UUID;

@Component
@RequiredArgsConstructor
public class OrderSubmitPaymentEventHandler extends FanoutListener<OrderSubmitPaymentEventHandler.OrderSubmitPaymentEvent> {
    private final OrderRepository orderRepository;
    private final AmqpGateway amqpGateway;
    private final OrderMapper orderMapper;

    @Override
    @Transactional
    public void consume(OrderSubmitPaymentEvent event) {
        var order = orderRepository.findById(UUID.fromString(event.orderId))
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "No such order with id " + event.orderId()));

       order.lockOrder();
       orderRepository.save(order);
       amqpGateway.publish(new OrderLockedEvent(orderMapper.toOrderInvoiceResponse(order)));
    }

    public record OrderSubmitPaymentEvent(String orderId) implements IRabbitFanoutSubscriber {}

    public record OrderLockedEvent(OrderInvoiceResponse order) implements IRabbitFanoutPublisher {}
}
