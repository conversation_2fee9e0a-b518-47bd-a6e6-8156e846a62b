package com.scube.calculation.rabbit;

import com.scube.calculation.dto.observer.CalculationObserverEvent;
import com.scube.calculation.observer.ICalculationObserver;
import com.scube.rabbit.core.fanout.subscriber.FanoutListener;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Map;


@Component
@RequiredArgsConstructor
@Slf4j
public class CalculationObserverEventHandler extends FanoutListener<CalculationObserverEvent> {
    private final Map<String, ICalculationObserver> observers;

    @Override
    @Transactional
    public void consume(CalculationObserverEvent event) {
        ICalculationObserver calculationObserver = observers.get(event.getObserverType());
        if (calculationObserver == null) throw new IllegalStateException("Unexpected value: " + event.getObserverType());
        calculationObserver.setInput(event.getInput());
        calculationObserver.update();

    }
}
