package com.scube.calculation.observer;


import com.scube.calculation.service.CartService;
import com.scube.rabbit.core.fanout.subscriber.IRabbitFanoutSubscriber;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;


@Component("remove")
@RequiredArgsConstructor
public class RemoveCartItemObserver implements ICalculationObserver {
    private final CartService cartService;
    private List<UUID> input;

    @Override
    public void update() {
        cartService.removeEntityFromAllCarts(input);
    }

    @Override
    public <T> void setInput(T input) {
        this.input = (List<UUID>) input;
    }
}
