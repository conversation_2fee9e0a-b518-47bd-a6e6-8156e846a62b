package com.scube.calculation.permissions;

import com.c4_soft.springaddons.security.oauth2.test.annotations.WithJwt;
import com.scube.calculation.SharedTestConfig;
import com.scube.calculation.permission.Permissions;
import org.junit.jupiter.api.Test;

import static com.scube.calculation.permissions.MockMvcHelper.*;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotEquals;

class CartControllerTest extends SharedTestConfig {
    @Test
    @WithJwt(json = START_JSON + Permissions.Cart.CREATE_CART + END_JSON)
    void testCreateCart_Success() throws Exception {
        var result = performJsonPost(mockMvc, "/cart/new");
        assertNotEquals(403, result.getResponse().getStatus());
    }

    @Test
    void testCreateCart_Failure() throws Exception {
        var result = performJsonPost(mockMvc, "/cart/new");
        assertEquals(403, result.getResponse().getStatus());
    }
}