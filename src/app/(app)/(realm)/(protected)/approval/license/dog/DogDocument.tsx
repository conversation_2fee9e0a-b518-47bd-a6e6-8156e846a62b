import { DialogContent } from "@/components/ui/dialog";
import { useGetDocumentBlob } from "@/hooks/api/useServices";
import { cn } from "@/lib/utils";
import Link from "next/link";
import { useCallback, useState } from "react";
import { Dialog, DialogTrigger } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Slider } from "@/components/ui/slider";
import { motion } from "framer-motion";
import Image from "next/image";
import { FaArrowRotateLeft, FaArrowRotateRight } from "react-icons/fa6";
import { Dog } from "@/types/DogType";
import { Document } from "@/types/DocumentType";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";

export default function DogDocument({ dog }: { dog: Dog }) {
  return (
    <div className="flex h-full w-full max-w-lg flex-col overflow-auto p-4">
      <div className="mb-4 text-xl font-semibold text-neutral-600">
        Documents
      </div>
      
      {dog?.documents && dog.documents.length > 0 ? (
        <Accordion 
          type="multiple" 
          className="w-full space-y-2"
          defaultValue={dog.documents.map((doc: Document) => doc.entityId)}
        >
          {dog.documents.map((doc: Document) => (
            <AccordionItem 
              key={doc.entityId} 
              value={doc.entityId}
              className="border rounded-lg"
            >
              <AccordionTrigger className="px-4 py-2 hover:no-underline">
                <span className="text-left font-medium">{doc.name}</span>
              </AccordionTrigger>
              <AccordionContent className="p-0">
                <FileDisplay file={doc} />
              </AccordionContent>
            </AccordionItem>
          ))}
        </Accordion>
      ) : (
        <div className="text-neutral-500">No documents found</div>
      )}
    </div>
  );
}

const FileDisplay = ({
  file,
}: {
  file: {
    documentUuid: string;
    name: string;
  };
}) => {
  const { data, isLoading, isError } = useGetDocumentBlob(file.documentUuid);

  const [zoomLevel, setZoomLevel] = useState<number>(100);
  const [rotate, setRotate] = useState<number>(0);

  const maxZoom = 200;
  const minZoom = 50;

  const zoomOut = () => setZoomLevel((prev) => Math.max(prev - 5, minZoom));
  const zoomIn = () => setZoomLevel((prev) => Math.min(prev + 5, maxZoom));
  const rotateRight = () => setRotate((prev) => prev + 90);
  const rotateLeft = () => setRotate((prev) => prev - 90);

  const handleZoomChange = useCallback((value: number[]) => {
    setZoomLevel(value[0]);
  }, []);

  const scale = zoomLevel / 100;

  if (isLoading) {
    return (
      <div className="w-full">
        <div className="flex items-center justify-center py-8 bg-neutral-50 rounded border">
          <div className="flex flex-col items-center gap-3">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <div className="text-neutral-500 text-sm">Loading document...</div>
          </div>
        </div>
      </div>
    );
  }

  if (isError) {
    return (
      <div className="w-full">
        <div className="flex items-center justify-center py-8 bg-red-50 rounded border border-red-200">
          <div className="flex flex-col items-center gap-2">
            <div className="text-red-500 text-sm font-medium">Error loading document</div>
            <div className="text-red-400 text-xs">Please try again later</div>
          </div>
        </div>
      </div>
    );
  }

  if (data.type === "application/pdf") {
    const fileUrl = URL.createObjectURL(data);

    return (
      <div className="w-full">
        <Link
          target="_blank"
          href={fileUrl}
          download={file.name}
          className="block w-full"
        >
          <div className="w-full rounded bg-neutral-100 shadow hover:bg-neutral-200 transition-colors">
            <object
              data={fileUrl}
              type="application/pdf"
              className="w-full h-96 object-contain"
            >
              <div className="flex items-center justify-center h-96 bg-neutral-50">
                <p className="text-neutral-600">
                  Your browser does not support PDFs.{" "}
                  <span className="text-blue-600 underline">Download the PDF</span>.
                </p>
              </div>
            </object>
          </div>
        </Link>
      </div>
    );
  }

  return (
    <div className="w-full">
      <Dialog>
        <DialogTrigger asChild>
          <button className="w-full group">
            <div className="w-full rounded border bg-neutral-50 overflow-hidden group-hover:bg-neutral-100 transition-colors">
              <Image
                src={URL.createObjectURL(data)}
                alt="Document image"
                width={0}
                height={0}
                sizes="100vw"
                className="w-full h-auto"
              />
            </div>
          </button>
        </DialogTrigger>
        <DialogContent className="h-full max-w-6xl p-0">
          <div className="flex h-full flex-col">
            {/* Image container */}
            <div className="flex-1 overflow-hidden bg-neutral-100 relative">
              <motion.div
                drag
                dragConstraints={{
                  left: -(scale - 1) * 400,
                  right: (scale - 1) * 400,
                  top: -(scale - 1) * 300,
                  bottom: (scale - 1) * 300,
                }}
                dragElastic={0.1}
                className="relative h-full w-full cursor-grab active:cursor-grabbing"
                style={{ 
                  touchAction: "none",
                  scale: scale,
                }}
                whileDrag={{ cursor: "grabbing" }}
              >
                <Image
                  src={URL.createObjectURL(data)}
                  alt="Document image"
                  fill
                  className="object-contain object-center select-none"
                  style={{ 
                    transform: `rotate(${rotate}deg)`,
                    pointerEvents: "none"
                  }}
                  draggable={false}
                />
              </motion.div>
              
              {/* Zoom level indicator */}
              {zoomLevel !== 100 && (
                <div className="absolute top-4 left-4 bg-black/70 text-white px-3 py-1 rounded-full text-sm">
                  {zoomLevel}% {zoomLevel > 100 ? '- Drag to pan' : ''}
                </div>
              )}
            </div>

            {/* Bottom controls bar */}
            <div className="flex items-center justify-between gap-4 p-4 border-t bg-white">
              <div className="flex items-center gap-3">
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={rotateLeft}
                  className="h-9 w-9 p-0"
                >
                  <FaArrowRotateLeft className="h-4 w-4" />
                </Button>
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={rotateRight}
                  className="h-9 w-9 p-0"
                >
                  <FaArrowRotateRight className="h-4 w-4" />
                </Button>
              </div>
              
              <div className="flex items-center gap-3">
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={zoomOut}
                  disabled={zoomLevel <= minZoom}
                  className="h-9 w-9 p-0"
                >
                  -
                </Button>
                <div className="flex items-center gap-2 min-w-32">
                  <Slider
                    className="flex-1"
                    min={minZoom}
                    max={maxZoom}
                    step={5}
                    value={[zoomLevel]}
                    onValueChange={handleZoomChange}
                  />
                  <span className="text-sm text-neutral-600 w-12 text-right">
                    {zoomLevel}%
                  </span>
                </div>
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={zoomIn}
                  disabled={zoomLevel >= maxZoom}
                  className="h-9 w-9 p-0"
                >
                  +
                </Button>
              </div>
              
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => {
                  setZoomLevel(100);
                  setRotate(0);
                }}
              >
                Reset
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};