import { Dog } from "@/types/DogType";
import { cn } from "@/lib/utils";
import { useEffect, useState } from "react";
import { AnimatePresence, motion } from "framer-motion";
import { License } from "@/types/LicenseType";
import AvatarImage, { getAvatarBlob } from "@/components/AvatarImage";
import ApprovalEntityHeader from "./ApprovalEntityHeader";
import { useCurrentLicenseContext } from "@/components/approval/hooks/useDogLicenseApproval";
import ProfileBuilderFactory from "@/components/builders/profileBuilder/ProfileBuilderFactory";
import DogDocument from "./DogDocument";

export const DogsContentApproval = ({
  dogs,
  license,
}: {
  dogs: Dog[];
  license: License;
}) => {
  const [currentDog, setCurrentDog] = useState<Dog | null>(dogs[0]);

  useEffect(() => {
    setCurrentDog(dogs[0]);
  }, [dogs]);

  const containerVariants = {
    hidden: { opacity: 0, y: -50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { type: "spring", stiffness: 100 },
    },
    exit: { opacity: 0, y: 50, transition: { duration: 0.2 } },
  };

  if (!dogs) return null;

  return (
    <>
      {(dogs.length > 1 ||
        license?.licenseType?.code === "purebredDogLicense") && (
        <div className="flex w-[300px] flex-col gap-2 rounded bg-white p-2 shadow">
          {dogs.map((dog: Dog) => (
            <DogTab
              dog={dog}
              key={dog.entityId}
              currentDogEntityId={currentDog?.entityId || ""}
              setCurrentDog={setCurrentDog}
            />
          ))}
        </div>
      )}
      <AnimatePresence mode="wait">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          exit="exit"
          className="w-full overflow-hidden rounded bg-white shadow"
        >
          {currentDog && <DogContent dog={currentDog} />}
        </motion.div>
      </AnimatePresence>
    </>
  );
};

export const DogTab = ({
  dog,
  currentDogEntityId,
  setCurrentDog,
}: {
  dog: Dog;
  currentDogEntityId: string;
  setCurrentDog: (dog: Dog) => void;
}) => {
  const active = currentDogEntityId === dog?.entityId;

  return (
    <button
      className={cn(
        "flex w-full gap-2 rounded p-2 transition-all hover:bg-blue-100",
        active && "bg-blue-50",
      )}
      key={dog?.entityId}
      onClick={() => setCurrentDog(dog)}
    >
      <AvatarImage
        entityType="dog"
        src={getAvatarBlob(dog?.documents) as string}
        alt="dog"
        width={active ? 40 : 40}
        height={active ? 40 : 40}
        className={cn(
          "h-fit rounded object-cover object-center shadow",
          active && "rounded-lg",
        )}
      />
      <div className={cn("flex flex-col text-left", active && "text-left")}>
        <div className={cn("", active && "line-clamp-1 break-all font-medium")}>
          {dog.dogName}
        </div>
        <small className="line-clamp-1 break-all text-neutral-600">
          {dog.dogBreed}
        </small>
      </div>
    </button>
  );
};

const DogContent = ({ dog }: { dog: Dog }) => {
  const [isThumbnail, setIsThumbnail] = useState<boolean>(true);
  const { refetchCurrentLicense, isRefetchingCurrentLicense } =
    useCurrentLicenseContext();

  return (
    <div className="flex h-full w-full gap-2 overflow-hidden ">
      {/* Dog Profile Information */}
      <div className="flex h-full w-full flex-col gap-4 overflow-auto p-4">
        <ApprovalEntityHeader
          entity={dog}
          entityId={dog.entityId}
          entityType="dog"
        />
        <ProfileBuilderFactory
          entity={dog}
          profileType="dog"
          entityIsFetching={isRefetchingCurrentLicense}
          entityRefetch={refetchCurrentLicense}
        />
      </div>
      {/* Documents */}
      <DogDocument dog={dog} />
    </div>
  );
};
