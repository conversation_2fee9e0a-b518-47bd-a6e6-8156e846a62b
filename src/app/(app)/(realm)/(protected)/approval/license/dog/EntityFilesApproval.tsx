import { Document } from "@/types/DocumentType";
import { FetchFile, FileItem } from "@/components/files/FilesComponent";
import { cn } from "@/lib/utils";

type EntityFilesProps = {
  documents: {
    [key: string]: Document[];
  };
  sheet?: boolean;
};

// Check if a document array contain null values
const containsNull = (arr: Document[]) => {
  return arr.some((document) => document === null || document === undefined);
};

export const EntityFilesApproval = ({
  documents,
  sheet,
}: EntityFilesProps) => {
  console.log(documents);
  return (
    <div className="flex w-full flex-col overflow-y-auto overflow-x-hidden">
      {Object.keys(documents).map((group: string) => {
        if (containsNull(documents[group]) || documents[group].length === 0) {
          return (
            <div key={group}>
              <h3 className="mb-4 text-xl font-semibold capitalize">
                {group} Documents
              </h3>
              <h1>Documents not found</h1>
            </div>
          );
        } else
          return (
            <div className="mb-10"
              key={group}
            >
              <h3 className="mb-4 text-xl font-semibold capitalize">
                {group} Documents
              </h3>

              <div
                className={cn(
                  "max-w-2xl px-4 py-4 sm:px-6 lg:max-w-7xl lg:px-8",
                  sheet ? "w-full" : "",
                )}
              >
                <div
                  className={cn(
                    "grid grid-cols-1 gap-x-6 gap-y-10 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 xl:gap-x-8",
                    sheet ? "flex flex-col" : "",
                  )}
                >
                  {documents[group]?.map((document: Document) => {
                    return (
                      <FetchFile
                        file={document}
                        key={document.documentUuid}
                        sheet={sheet}
                      />
                    );
                  })}
                </div>
              </div>
            </div>
          );
      })}
    </div>
  );
};
