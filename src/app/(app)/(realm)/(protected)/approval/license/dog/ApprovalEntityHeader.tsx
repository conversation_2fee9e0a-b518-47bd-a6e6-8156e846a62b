import { entityDisplay } from "@/app/(app)/(realm)/(protected)/(app)/entity/[entitytype]/[entityId]/profile/entityHelper";
import { Badge } from "@/components/ui/badge";
import { Individual } from "@/types/IndividualType";
import { BiEnvelope, BiPhone } from "react-icons/bi";
import ProfileAvatar from "@/components/profile/ProfileAvatar";
import { isSenior } from "@/components/license/licenseHelper";
import { useGetDocumentBlob } from "@/hooks/api/useServices";
import Link from "next/link";

const ApprovalEntityHeader = ({
  entity,
  entityType,
  entityId,
}: {
  entity: any;
  entityType: string;
  entityId: string;
}) => {
  const ent = entityDisplay(entity, entityType);
  console.log(ent);
  const isActive = ent?.active;
  const individual = entity?.individual as Individual;

  const senior = isSenior(individual?.dateOfBirth);

  const avatarUrl: string =
    individual?.documents?.find((doc) => doc.key === "avatar")?.documentUuid ??
    "";

  const enabled = avatarUrl !== "" || avatarUrl.length > 0;

  const {
    data: avatarBlob,
    isLoading,
    isError,
  } = useGetDocumentBlob((avatarUrl as string) ?? "", enabled);

  return (
    <div className="bg-white  border-gray-200 mb-4">
      <div className="flex items-center gap-4">
        {/* Compact Avatar */}
        <div className="shrink-0">
          {isLoading || isError ? (
            <ProfileAvatar
              entityId={entityId}
              entityType={entityType}
              isActive={isActive ?? false}
              avatarUrl={null}
              canEdit={true}
              type="normal"
            />
          ) : (
            <ProfileAvatar
              entityId={entityId}
              entityType={entityType}
              isActive={isActive ?? false}
              avatarUrl={URL?.createObjectURL(avatarBlob)}
              canEdit={true}
              blob={avatarBlob}
              type="normal"
            />
          )}
        </div>

        {/* Compact Info Section */}
        <div className="flex-1 min-w-0">
          {/* Name and Status Row */}
          <div className="flex items-center gap-1 mb-1">
            <h1 className="text-2xl font-semibold text-gray-900 truncate">
              <Link
                href={`/profile/${entityType}/${entityId}?tab=profile`}
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-600 hover:text-blue-500 capitalize"
              >
                {ent?.primaryDisplay || "No Name"}
              </Link>
              {senior && (
                <span className="text-sm text-gray-500 font-normal ml-1">(Senior)</span>
              )}
            </h1>
            {!isActive && <Badge variant="destructive" className="shrink-0">Inactive</Badge>}
          </div>

          {/* Address */}
          {ent?.secondaryDisplay && (
            <div className="text-sm text-gray-600 text-wrap">
              {ent.secondaryDisplay}
            </div>
          )}

          {/* Contact Info */}
          {ent?.contacts && (
            <div className="flex  text-sm text-gray-600 w-full flex-wrap">
              {ent.contacts.phone && (
                <div className="flex items-center gap-1 shrink-0 mr-4">
                  <BiPhone className="w-4 h-4" />
                  <span>{ent.contacts.phone}</span>
                </div>
              )}
              {ent.contacts.email && (
                <div className="flex items-center gap-1">
                  <BiEnvelope className="w-4 h-4" />
                  <span className="">{ent.contacts.email}</span>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ApprovalEntityHeader;