"use client";
import {
  ApprovalB<PERSON>,
  App<PERSON>al<PERSON>ontainer,
  ContentContainer,
} from "@/components/approval/components/Approval";

import {
  ApprovalSidebarContainer,
  SidebarTitle,
} from "@/components/approval/components/ApprovalSidebar";
import {
  LicenseListProvider,
  CurrentLicenseProvider,
  useLicenseListContext,
} from "@/components/approval/hooks/useDogLicenseApproval";
import LicenseApprovalContent from "./LicenseApprovalContent";
import { DogLicenseApprovalSidebar } from "./DogLicenseApprovalSidebar";
import { Button } from "@/components/ui/button";
import { RefreshCw } from "lucide-react";

export default function LicenseApprovalPage() {
  return (
    <ApprovalContainer>
      <LicenseListProvider>
        <SidebarContent />
        <ContentContainer>
          <CurrentLicenseProvider>
            <LicenseApprovalContent />
            <ApprovalBar type="license" />
          </CurrentLicenseProvider>
        </ContentContainer>
      </LicenseListProvider>
    </ApprovalContainer>
  );
}

const SidebarContent = () => {
  const {
    refetchLicenseList,
    loadingLicenseList,
    fetchingLicenseList,
    totalElements,
  } = useLicenseListContext();

  return (
    <ApprovalSidebarContainer>
      <SidebarTitle title="Dog Licenses Approval">
        <div className="space-y-2">
          <div className="text-sm text-neutral-600">
            Pending Approval: {totalElements}
          </div>
          <Button
            onClick={() => refetchLicenseList()}
            disabled={loadingLicenseList || fetchingLicenseList}
            variant="outline"
            size="sm"
            className={`h-8 w-full text-xs font-medium transition-all duration-200 ${
              loadingLicenseList
                ? "cursor-not-allowed border-blue-300 bg-blue-50 text-blue-700"
                : "border-slate-300 hover:border-slate-400 hover:bg-slate-50"
            }`}
          >
            <div
              className={`mr-1.5 flex h-4 w-4 items-center justify-center rounded-full transition-all duration-300 ${
                loadingLicenseList
                  ? "bg-blue-100 ring-2 ring-blue-200 ring-opacity-50"
                  : "bg-slate-100 group-hover:bg-slate-200"
              }`}
            >
              <RefreshCw
                className={`size-2.5 transition-transform duration-300 ${
                  loadingLicenseList || fetchingLicenseList
                    ? "animate-spin text-blue-600"
                    : "text-slate-600"
                }`}
              />
            </div>
            <span
              className={
                loadingLicenseList ? "text-blue-700" : "text-slate-700"
              }
            >
              {loadingLicenseList || fetchingLicenseList
                ? "Refreshing..."
                : "Refresh"}
            </span>
          </Button>
        </div>
      </SidebarTitle>
      <DogLicenseApprovalSidebar />
    </ApprovalSidebarContainer>
  );
};
