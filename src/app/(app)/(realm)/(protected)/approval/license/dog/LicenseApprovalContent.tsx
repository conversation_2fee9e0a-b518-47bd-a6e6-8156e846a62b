import { useLicenseListContext, useCurrentLicenseContext } from "@/components/approval/hooks/useDogLicenseApproval";
import LoadingSpinner from "@/components/ui/LoadingSpinner";
import { cn } from "@/lib/utils";
import { Individual } from "@/types/IndividualType";
import { License } from "@/types/LicenseType";
import React from "react";
import { DogsContentApproval } from "./DogsContentApproval";
import { OwnersContentApproval } from "./OwnerTabApproval";
import Image from "next/image";

export default function LicenseApprovalContent() {
  const { licenseList, loadingLicenseList } = useLicenseListContext();
  const { currentLicense, loadingCurrentLicense, currentLicenseId } = useCurrentLicenseContext();

  // Show loading when fetching license list or when loading a specific license
  if (loadingLicenseList || (currentLicenseId && loadingCurrentLicense))
    return (
      <div className="flex h-full w-full items-start overflow-hidden bg-white">
        <div className="flex h-full w-full items-center justify-center  gap-2">
          <LoadingSpinner className="m-0 size-6 p-0" /> Loading...
        </div>
      </div>
    );

  // No licenses available for approval
  if (licenseList.length === 0) {
    return (
      <div className="flex h-full flex-col items-center justify-center gap-10 rounded bg-white text-lg font-semibold shadow lg:text-2xl">
        No Licenses Require Approval
        <Image
          src="/images/resident/doghappy.png"
          alt="No Licenses Require Approval"
          width={200}
          height={200}
        />
      </div>
    );
  }

  // Licenses exist but none is selected
  if (!currentLicenseId) {
    return (
      <div className="flex h-full flex-col items-center justify-center gap-10 rounded bg-white text-lg font-semibold shadow lg:text-2xl">
        No License Selected
        <div className="text-base font-normal text-neutral-600">
          Select a license from the sidebar to begin approval
        </div>
        <Image
          src="/images/resident/doghappy.png"
          alt="No License Selected"
          width={200}
          height={200}
        />
      </div>
    );
  }

  return (
    <div className="flex h-full w-full flex-col gap-4 overflow-hidden">
      <LicenseInfoSection license={currentLicense?.license} />
      <LicenseTabs />
      <LicenseContent />
    </div>
  );
}

export const LicenseTabs = () => {
  const { setCurrentTab, currentTab } = useCurrentLicenseContext();

  const tabs: {
    id: string;
    label: string;
  }[] = [
    {
      id: "dog",
      label: "Dog",
    },
    {
      id: "owner",
      label: "Owner",
    },
  ];

  return (
    <div className="flex gap-4 rounded bg-white p-2 shadow">
      {tabs.map((tab) => (
        <button
          key={tab.id}
          className={cn(
            "rounded px-2 py-1",
            currentTab === tab.id && "bg-clerk-background text-white",
          )}
          onClick={() => {
            setCurrentTab(tab.id);
          }}
        >
          {tab.label}
        </button>
      ))}
    </div>
  );
};

export const LicenseContent = () => {
  const { currentTab, currentLicense } = useCurrentLicenseContext();
  console.log("currentLicense", currentLicense);
  if (!currentLicense) return null;


  return (
    <div
      className="flex w-full flex-col overflow-hidden"
      key={currentLicense.license.entityId}
    >
      {currentTab === "dog" && (
        <DogsContentApproval
          dogs={currentLicense.dog}
          license={currentLicense.license}
        />
      )}
      {currentTab === "owner" && (
        <OwnersContentApproval
          owners={currentLicense.individual}
          license={currentLicense}
        />
      )}
    </div>
  );
};

export const LicenseInfoSection = ({ license }: { license: License }) => {
  if (!license) {
    return <div>License not found</div>;
  }

  const display = [
    {
      label: "License Number",
      value: license.licenseNumber,
    },
    {
      label: "License Type",
      value: license.licenseType.name,
    },
    {
      label: "License Duration",
      value: `${license.licenseDuration} years`,
    }
  ]

  return (
    <div className="w-full rounded bg-white p-2 shadow">
      <h1 className="mb-2 font-bold text-xl">License Information</h1>
      <div className="flex gap-10 text-neutral-600">
        {
          display.map((item, index) => (
            <div key={index} className="">
              <h2 className="text-sm text-neutral-700">{item.label}:</h2>
              <p className="text-neutral-900 text-base font-semibold">{item.value}</p>
            </div>
          ))
        }
      </div>
    </div>
  );
};