import { useMergeApprovalContext } from "@/components/approval/hooks/useMergeApproval";
import { But<PERSON> } from "@/components/ui/button";
import IndividualProfile from "../../../(app)/entity/[entitytype]/[entityId]/profile/individual/IndividualProfile";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Document } from "@/types/DocumentType";
import { useCallback, useEffect, useState } from "react";
import Image from "next/image";
import { Dialog, DialogContent, DialogTrigger } from "@/components/ui/dialog";
import { cn } from "@/lib/utils";
import { FaArrowRotateLeft, FaArrowRotateRight } from "react-icons/fa6";
import { Slider } from "@/components/ui/slider";
import { motion } from "framer-motion";
import { useGetDocumentBlob } from "@/hooks/api/useServices";
import Link from "next/link";
import MergeApprovalModal from "./MergeApprovalModal";
import MergeDeniedModal from "./MergeDeniedModal";

const MergeAccountContent = () => {
  // const [isThumbnail, setIsThumbnail] = useState<boolean>(true);
  const {
    mergeApprovalList,
    currentMergeRequest,
    fetchingCurrentResident,
    loadingCurrentResident,
  } = useMergeApprovalContext();

  const foundResidents = currentMergeRequest?.foundResidents;

  if (loadingCurrentResident) return <div>Loading...</div>;

  // if (fetchingCurrentResident) return <div>Refreshing Page...</div>;

  if (mergeApprovalList.length === 0) return <div>No merge requests</div>;

  if (currentMergeRequest) {
    return (
      <>
        <div className="flex h-full flex-col gap-4 overflow-hidden">
          <div className="grid grid-cols-2 gap-6 overflow-hidden rounded">
            <ExistingResident />
            
            {foundResidents?.length > 0 ? <FoundResidents foundResidents={foundResidents}/> : <div>No residents found</div>}
          </div>
        </div>
      </>
    );
  }

  return <div className="text-2xl font-semibold flex flex-col bg-white rounded items-center justify-center h-full gap-10 shadow-lg">
      No Merge Requests
      <Image 
        src='/images/resident/doghappy.png'
        alt='No accounts require merging'
        width={200}
        height={200}
      />
    </div>
};

export default MergeAccountContent;

const ExistingResident = () => {
  const [isThumbnail, setIsThumbnail] = useState<boolean>(true);

  const { currentMergeRequest, refetchCurrentResident } =
    useMergeApprovalContext();
  const requestedResident = currentMergeRequest?.requestedResident;

  return (
    <div className="flex w-full flex-col overflow-y-auto">
      <div className="text-lg">Requesting Account</div>
      <Tabs defaultValue="resident" className="w-full">
        <TabsList className="p-0">
          <TabsTrigger value="resident">Resident</TabsTrigger>
          <TabsTrigger value="documents">Documents</TabsTrigger>
        </TabsList>
        <TabsContent
          value="resident"
          className="w-full rounded bg-white p-6 shadow-lg"
        >
          {requestedResident && (
            <IndividualProfile
              individual={currentMergeRequest?.requestedResident}
              refetch={refetchCurrentResident}
              layout="column"
              canEdit={true}
            />
          )}
        </TabsContent>
        <TabsContent
          value="documents"
          className="w-full rounded bg-white p-6 shadow-sm"
        >
          {requestedResident?.documents?.length === 0 && (
            <div>No documents found</div>
          )}
          {requestedResident?.documents?.map((doc: Document) => {
            console.log(doc);
            return (
              <FileModal
                file={doc}
                thumbnails={isThumbnail}
                key={doc.entityId}
              />
            );
          })}
        </TabsContent>
      </Tabs>
    </div>
  );
};

const FoundResidents = ({foundResidents}:{
  foundResidents: any[]
}) => {

  const firstResident = foundResidents[0]?.resident 

  const [selectedTab, setSelectedTab] = useState<string | undefined>(
    firstResident.entityId,
  );

  useEffect(() => {
    if (selectedTab) {
      setSelectedTab(selectedTab);
    }
  }, [selectedTab]);

  return (
    <div className="flex w-full flex-col overflow-y-auto" key={firstResident.entityId}>
      <div className="text-lg">Matched Account: {firstResident.firstName}</div>
      <Tabs defaultValue={foundResidents[0].resident.entityId} className="w-full">
        <TabsList>
          {foundResidents.map((resident: any) => {
            const res = resident?.resident;
            const fullName = `${res?.firstName} ${res?.lastName}`;
            if (!res) return null;
            return (
              <TabsTrigger key={res?.entityId} value={res?.entityId}
                onClick={() => setSelectedTab(res?.entityId)}
              >
                {fullName}
              </TabsTrigger>
            );
          })}
        </TabsList>

        {foundResidents.map((resident: any) => {
          const res = resident?.resident;

          return (
            <TabsContent
              key={res?.entityId}
              value={res?.entityId}
              className={cn(
                "w-full rounded bg-white p-6 shadow-sm",
                selectedTab === res?.entityId && "[data-state]=active",
              )}
            >
              <FoundResident resident={resident} />
            </TabsContent>
          );
        })}
      </Tabs>
    </div>
  );
};

const FoundResident = ({ resident }: { resident: any }) => {
  const { refetchCurrentResident } = useMergeApprovalContext();
  const [isThumbnail, setIsThumbnail] = useState<boolean>(true);

  const res = resident?.resident;

  console.log(res.entityId);

  if (!res) return <div>No resident found</div>;
  return (
    <Tabs defaultValue="resident" className="w-full">
      <div className="flex flex-wrap items-center justify-between gap-2">
        <TabsList>
          <TabsTrigger value="resident">Resident</TabsTrigger>
          <TabsTrigger value="documents">Documents</TabsTrigger>
        </TabsList>
        <div className="flex gap-4">
          <MergeApprovalModal existingUserId={res.entityId} />
          <MergeDeniedModal existingUserId={res.entityId} />
        </div>
      </div>
      <TabsContent
        value="resident"
        className="w-full rounded bg-white p-6 shadow-sm"
      >
        {resident && (
          <IndividualProfile
            individual={res}
            refetch={refetchCurrentResident}
            layout="column"
            canEdit={false}
          />
        )}
      </TabsContent>
      <TabsContent
        value="documents"
        className="w-full rounded bg-white p-6 shadow-sm"
      >
        {resident?.documents?.length === 0 && <div>No documents found</div>}
        {resident?.documents?.map((doc: Document) => {
          console.log(doc);
          return (
            <FileModal file={doc} thumbnails={isThumbnail} key={doc.entityId} />
          );
        })}
      </TabsContent>
    </Tabs>
  );
};

const FileModal = ({
  file,
  thumbnails,
}: {
  file: {
    documentUuid: string;
    name: string;
  };
  thumbnails: boolean;
}) => {
  const { data, isLoading, isError } = useGetDocumentBlob(file.documentUuid);

  const [zoomLevel, setZoomLevel] = useState<number>(100);
  const [rotate, setRotate] = useState<number>(0);

  const maxZoom = 200;
  const minZoom = 50;

  const zoomOut = () => setZoomLevel((prev) => Math.max(prev - 5, minZoom));
  const zoomIn = () => setZoomLevel((prev) => Math.min(prev + 5, maxZoom));
  const rotateRight = () => setRotate((prev) => prev + 90);
  const rotateLeft = () => setRotate((prev) => prev - 90);

  const handleZoomChange = useCallback((value: number[]) => {
    setZoomLevel(value[0]);
  }, []);

  const scale = zoomLevel / 100;

  if (isLoading) return <div>Loading...</div>;
  if (isError) return <div>Error</div>;

  if (data.type === "application/pdf") {
    const fileUrl = URL.createObjectURL(data);

    console.log(fileUrl);

    return (
      <Link
        target="_blank"
        href={fileUrl}
        download={file.name}
        className="cursor-pointer hover:bg-blue-100"
      >
        <div
          className={cn(
            "w-auto rounded bg-neutral-200 shadow",
            thumbnails && "w-60 shrink-0",
          )}
        >
          <div className="flex w-full items-center justify-center">
            <object
              data={fileUrl}
              type="application/pdf"
              className={cn(
                "w-full object-contain object-center",
                thumbnails ? "h-60 w-60 shrink-0" : "min-h-[400px]",
              )}
            >
              <p>
                Your browser does not support PDFs.{" "}
                <a href={fileUrl}>Download the PDF</a>.
              </p>
            </object>
          </div>
          <div className="px-2 py-1 text-lg">{file.name}</div>
        </div>
      </Link>
    );
  }

  return (
    <Dialog>
      <DialogTrigger asChild>
        <div
          className={cn(
            "w-auto rounded bg-neutral-200 shadow",
            thumbnails && "w-60 shrink-0",
          )}
        >
          <button
            className={cn(
              "relative aspect-square w-full cursor-pointer rounded border p-1",
              thumbnails && "w-60 shrink-0",
            )}
          >
            <Image
              src={URL.createObjectURL(data)}
              alt="Document image"
              fill
              className={cn(
                "object-contain object-center",
                thumbnails && "rounded object-cover",
              )}
            />
          </button>
          <div className="px-2 py-1 text-lg">{file.name}</div>
        </div>
      </DialogTrigger>
      <DialogContent className="h-full max-w-6xl">
        <div className="flex h-full flex-col overflow-hidden">
          <motion.div
            drag
            dragConstraints={{ left: 0, right: 0, top: 0, bottom: 0 }}
            className="relative h-full w-full cursor-grab overflow-hidden"
            style={{ touchAction: "none" }}
          >
            <div
              className="relative h-full w-full"
              style={{
                transform: `scale(${scale})`,
                transformOrigin: "center",
              }}
            >
              <Image
                src={URL.createObjectURL(data)}
                alt="Document image"
                fill
                className="object-contain object-center"
                style={{ transform: `rotate(${rotate}deg)` }}
              />
            </div>
          </motion.div>
          <div className="flex items-center justify-center gap-4 p-4">
            <Button onClick={rotateLeft}>
              <FaArrowRotateLeft />
            </Button>
            <Button onClick={zoomOut}>-</Button>
            <Slider
              className="slider-container"
              min={minZoom}
              max={maxZoom}
              step={1}
              value={[zoomLevel]}
              onValueChange={handleZoomChange}
            />
            <Button onClick={zoomIn}>+</Button>
            <Button onClick={rotateRight}>
              <FaArrowRotateRight />
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
