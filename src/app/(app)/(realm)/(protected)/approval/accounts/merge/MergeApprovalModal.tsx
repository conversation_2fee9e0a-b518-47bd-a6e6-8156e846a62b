import React, { useState } from "react";

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";

import { useForm } from "react-hook-form";
import { Button } from "@/components/ui/button";
import { useAtom } from "jotai";
import { toastAtom } from "@/components/ui/toast/toast";
import {
  usePathname,
  useRouter,
  useSearchParams,
} from "next/navigation";
import { useQueryClient } from "@tanstack/react-query";
import { useMergeApprovalContext } from "@/components/approval/hooks/useMergeApproval";
import { useApproveMergeRequest } from "@/hooks/api/useApprovals";
import { cn } from "@/lib/utils";
import LoadingSpinner from "@/components/ui/LoadingSpinner";

export default function MergeApprovalModal({
  existingUserId,
}: {
  existingUserId: string;
}) {
  const { handleSubmit, reset } = useForm();
  const searchParams = useSearchParams();
  const residentId = searchParams.get("requestedUserId") as string;
  const { mergeApprovalList, setMergeApprovalList, refetchMergeList, refetchCurrentResident } =
    useMergeApprovalContext();

  const [_, setToast] = useAtom(toastAtom);

  const approveMutate = useApproveMergeRequest();
  const [isOpen, setIsOpen] = useState(false);
  const [isApproved, setIsApproved] = useState(false);
  const [isInCompletionMode, setIsInCompletionMode] = useState<boolean>(false);

  const pathname = usePathname();
  const { replace } = useRouter();
  
  const queryClient = useQueryClient();

  const nextResident = () => {
    const currentResidentIndex = mergeApprovalList.findIndex(
      (item: any) => item.entityId === residentId,
    );

    const nextResidentIndex = currentResidentIndex + 1;

    if (nextResidentIndex >= mergeApprovalList.length) {
      return null;
    }

    return mergeApprovalList[nextResidentIndex].entityId;
  };

  const nextResidentId = nextResident();
  

  const onSubmit = () => {
    approveMutate.mutate(
      {
        existingUserIds: [existingUserId],
        requestedUserId: residentId,
      },
      {
        onSuccess: () => {
          setToast({
            status: "success",
            label: "Merge Approved",
            message: `Merge request has been approved successfully`,
          });
          setIsApproved(true);
          setIsInCompletionMode(true);
        },
        onError: (error: any) => {
          setToast({
            status: "error",
            label: "Error approving merge request",
            message: error.message,
          });
        },
      },
    );
  };

  const handleOpenChange = (newOpenState: boolean) => {
    // If in completion mode (after approval), absolutely prevent any closure
    if (isInCompletionMode && !newOpenState) {
      console.log("Merge modal closure blocked - completion mode active");
      return;
    }
    
    setIsOpen(newOpenState);
    setIsApproved(false);
    setIsInCompletionMode(false);
    if (!newOpenState) {
      reset();
    }
  };

  const handleCloseNext = () => {
    // Move
    setIsInCompletionMode(false); // Exit completion mode
    setIsOpen(false);
    setIsApproved(false);
    refetchMergeList();
    refetchCurrentResident();
    queryClient.invalidateQueries();
    if (nextResidentId) {
      replace(`${pathname}?requestedUserId=${nextResidentId}`);
    } else {
      replace(`${pathname}`);
    }
  }

  const handleClose = () => {
    setIsOpen(false);
    setIsApproved(false);
    setIsInCompletionMode(false);
    reset();
    replace(`${pathname}`);
    refetchMergeList();
    setMergeApprovalList(mergeApprovalList.filter((item: any) => item.entityId !== residentId));
  }

  const handleGoToAccount = () => {
    // Move
    setIsInCompletionMode(false); // Exit completion mode
    setIsOpen(false);
    setIsApproved(false);
    refetchMergeList();
    refetchCurrentResident();
    queryClient.invalidateQueries();
    replace(`/profile/individual/${residentId}?tab=profile`);
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleOpenChange}>
      <DialogTrigger
        onClick={() => {
          setIsOpen(true);
        }}
      >
        <Button variant="success" size={"sm"}>
          Approve
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-lg mx-4 sm:mx-auto">
        <DialogHeader className="text-center space-y-3">
          <DialogTitle className="text-2xl font-bold text-gray-900">
            {isApproved ? "Account Merged! 🎉" : "Approve Merge Request"}
          </DialogTitle>
          <DialogDescription className="text-gray-600">
            {isApproved ? (
              "The accounts have been successfully merged and are now unified."
            ) : (
              "Please review the merge request details before approving."
            )}
          </DialogDescription>
        </DialogHeader>
        
        {isApproved ? (
          <div className="py-6 space-y-8">
            {/* Success State */}
            <div className="text-center space-y-4">
              <div className="mx-auto w-16 h-16 bg-gradient-to-r from-green-400 to-emerald-500 rounded-full flex items-center justify-center shadow-lg">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={3} d="M5 13l4 4L19 7" />
                </svg>
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Merge Request Approved</h3>
                <p className="text-sm text-gray-500">
                  {nextResidentId 
                    ? "Successfully processed and accounts unified" 
                    : "Successfully processed and accounts unified • Last request in queue"
                  }
                </p>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="space-y-4">
              {/* Secondary Actions */}
              {nextResidentId ? (
                // When there are more requests - show View Account and Next Request
                <div className="grid grid-cols-2 gap-3">
                  <Button 
                    variant="outline" 
                    className="h-11 border-gray-300 hover:border-gray-400 hover:bg-gray-50 font-medium rounded-lg transition-all duration-200"
                    onClick={handleGoToAccount}
                  >
                    <span className="text-sm">View Account</span>
                  </Button>
                  <Button 
                    variant="outline"
                    className="h-11 border-blue-300 hover:border-blue-400 hover:bg-blue-50 text-blue-700 font-medium rounded-lg transition-all duration-200"
                    onClick={handleCloseNext}
                  >
                    <span className="text-sm">Next Request</span>
                  </Button>
                </div>
              ) : (
                // When no more requests - show View Account and Close
                <div className="grid grid-cols-2 gap-3">
                  <Button 
                    variant="outline" 
                    className="h-11 border-gray-300 hover:border-gray-400 hover:bg-gray-50 font-medium rounded-lg transition-all duration-200"
                    onClick={handleGoToAccount}
                  >
                    <span className="text-sm">View Account</span>
                  </Button>
                  <Button 
                    variant="outline"
                    className="h-11 border-green-300 hover:border-green-400 hover:bg-green-50 text-green-700 font-medium rounded-lg transition-all duration-200"
                    onClick={handleClose}
                  >
                    <span className="text-sm">Close</span>
                  </Button>
                </div>
              )}
            </div>
          </div>
        ) : (
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            {/* Merge Request Details Card */}
            <div className="bg-gradient-to-r from-gray-50 to-gray-100 rounded-xl p-6 border border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
                Merge Request Details
              </h3>
              <div className="space-y-3">
                <div className="bg-white rounded-lg p-4 border border-gray-100">
                  <p className="text-sm font-medium text-gray-600 mb-1">Action</p>
                  <p className="text-base text-gray-900">
                    Merge duplicate account into existing resident profile
                  </p>
                </div>
              </div>
            </div>

            {/* Warning Notice */}
            <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
              <div className="flex items-start gap-3">
                <svg className="w-5 h-5 text-amber-600 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 18.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
                <div>
                  <p className="text-sm font-medium text-amber-800">Merge Confirmation</p>
                  <p className="text-sm text-amber-700 mt-1">
                    This action will permanently merge the accounts and cannot be undone.
                  </p>
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-col-reverse sm:flex-row gap-3 pt-2">
              <Button
                type="button"
                variant="outline"
                className="flex-1 h-11 border-gray-300 hover:border-gray-400 hover:bg-gray-50 font-medium rounded-lg transition-all duration-200"
                onClick={() => handleOpenChange(false)}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                className="flex-1 h-11 bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white font-semibold rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                disabled={approveMutate.isLoading}
              >
                {approveMutate.isLoading ? (
                  <div className="flex items-center gap-2">
                    <LoadingSpinner className="w-4 h-4" />
                    Approving...
                  </div>
                ) : (
                  <div className="flex items-center gap-2">
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    Approve Merge
                  </div>
                )}
              </Button>
            </div>
          </form>
        )}
      </DialogContent>
    </Dialog>
  );
}
