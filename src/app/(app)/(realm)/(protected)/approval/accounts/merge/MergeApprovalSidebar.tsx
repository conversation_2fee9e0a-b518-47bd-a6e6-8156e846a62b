"use client"
import { SidebarCard, SidebarHeader } from "@/components/approval/components/ApprovalSidebar";
import { useMergeApprovalContext } from "@/components/approval/hooks/useMergeApproval";
import { cn } from "@/lib/utils";
import { format } from "date-fns";
import { usePathname, useSearchParams, useRouter } from "next/navigation";

export const MergeApprovalSidebar = () => {
  const { replace } = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  const {
    loadingMergeList,
    errorMergeList, 
    mergeApprovalList,
    fetchingMergeList,
    refetchMergeList,
    refetchCurrentResident
  } = useMergeApprovalContext();

  if (loadingMergeList) return <div>Loading...</div>;
  if (errorMergeList) return <div>Error</div>;

  if (mergeApprovalList) {
    console.log(mergeApprovalList)
    return (
      <>
        {mergeApprovalList?.map((item: any) => {
          const fullname = `${item?.firstName} ${item?.lastName}`;
          const active = item?.entityId === searchParams.get("requestedUserId");
          const firstRequestDate = item.createdDate as string ?? "N/A";

          return (
            <SidebarCard
              key={item.entityId}
              onClick={() => {
                replace(`${pathname}?requestedUserId=${item.entityId}`);
                refetchMergeList();
              }}
              className={active ? "bg-blue-50" : "hover:bg-blue-100"}
            >
              <SidebarHeader>
                <div className="font-base text-neutral-700">
                  {item?.length} Requested:
                </div>
                <div
                  className={cn(
                    "text-neutral-600",
                    active ? "text-neutral-800" : "text-neutral-500",
                  )}
                >
                  {format(new Date(firstRequestDate), "MM/dd/yyyy")}
                </div>
              </SidebarHeader>
              <div className="text-xl font-medium line-clamp-1 break-all">{fullname}</div>
              <div className={cn("text-xs lg:text-sm text-neutral-600 line-clamp-1 break-all")}>
                {item?.email ?? "No Email"}
              </div>
            </SidebarCard>
          );
        })}
      </>
    );
  }
  return null;
};
