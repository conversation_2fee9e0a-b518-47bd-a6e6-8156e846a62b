import RangeBar from "@/components/dashboard/RangeBar";
import { Card } from "@/components/ui/card";
import LicenseApprovals from "./LicenseApprovals";
import MergeApprovals from "./MergeApprovals";
import KPIWidgets from "./KPIWidgets";
import KPIGroup from "./KPIGroup";

export default function Overview() {
  return (
    <div className="flex w-full gap-4 overflow-hidden">
      <MainDashboardSection />
      <RequestsDashboardSection />
    </div>
  );
}

const MainDashboardSection = () => {
  return (
    <Card className="flex w-full flex-col gap-8 p-6 shadow-sm overflow-x-hidden overflow-y-auto">
      <div className="flex w-full items-center justify-between">
        <h1 className="shrink-0 text-2xl font-bold text-slate-800">
          Dashboard Overview
        </h1>
        <RangeBar />
      </div>

      <KPIWidgets />
      <KPIGroup />
    </Card>
  );
};

const RequestsDashboardSection = () => {
  return (
    <Card className="flex w-[400px] flex-col gap-4 p-6 shadow-sm overflow-x-hidden overflow-y-auto">
      <h2 className="flex items-center justify-between text-xl font-semibold">
        <span>Requests</span>
      </h2>
      <div className="px-2">
        <hr />
      </div>
      <LicenseApprovals />
      <MergeApprovals />
    </Card>
  );
};
