import { Button } from "@/components/ui/button";
import { useGetMergeAccountsList } from "@/hooks/api/useApprovals";
import { Users } from "lucide-react";
import { useRouter } from "next/navigation";

export default function MergeApprovals() {
  const { push } = useRouter();
  const { data, isLoading, isError } = useGetMergeAccountsList();

  if (isLoading)
    return (
      <div className="flex justify-center px-2">
        <div className="h-6 w-6 animate-spin rounded-full border-b-2 border-blue-500"></div>
      </div>
    );
  if (isError)
    return <div className="px-2 text-red-500">Error loading merge data...</div>;

  const residents: RequestedResidentProp[] = data?.content;
  const url = `/approval/accounts/merge`;

  return (
    <div className="mt-6 h-full">
      <h3 className="mb-3 flex items-center justify-between px-2 font-bold tracking-tight text-neutral-700">
        <div>Merge Accounts</div>
        <div>{data.content.length}</div>
      </h3>
      <div className="flex flex-col gap-2">
        {data.content.length === 0 && (
          <div className="rounded-lg bg-slate-50 p-4 text-center text-slate-500">
            <Users className="mx-auto mb-2 h-8 w-8 text-slate-300" />
            <p>No pending merge requests</p>
          </div>
        )}
        {residents.slice(0, 5).map((item: RequestedResidentProp) => {
          return <MergeApprovalItem item={item} key={item.entityId} />;
        })}
        {data.content.length > 0 && (
          <Button
            onClick={() => {
              push(url);
            }}
            className="mt-2 w-full bg-blue-600 text-white hover:bg-blue-700"
            size={"sm"}
          >
            View All
          </Button>
        )}
      </div>
    </div>
  );
}

type RequestedResidentProp = {
  entityId: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  foundResidentCount: number;
  createdDate: string;
};

const MergeApprovalItem = ({ item }: { item: RequestedResidentProp }) => {
  const { replace } = useRouter();
  const firstName = item?.firstName ?? "";
  const lastName = item?.lastName ?? "";
  const url = `/approval/accounts/merge`;

  return (
    <button
      className="flex justify-between rounded-lg border border-slate-200 px-3 py-2 text-sm transition-colors hover:border-blue-300 hover:bg-blue-50"
      onClick={() => {
        replace(`${url}?requestedUserId=${item.entityId}`);
      }}
    >
      <div>
        <div className="text-left font-medium">
          {firstName + " " + lastName}
        </div>
      </div>
      <div className="self-center">
        <span className="rounded-full bg-slate-100 px-2 py-1 text-xs text-slate-700">
          {item?.foundResidentCount + " Requests"}
        </span>
      </div>
    </button>
  );
};
