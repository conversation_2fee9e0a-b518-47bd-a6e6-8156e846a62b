import { Button } from "@/components/ui/button";
import { useGetLicenses } from "@/hooks/api/useLicense";
import { Dog } from "@/types/DogType";
import { Individual } from "@/types/IndividualType";
import { License } from "@/types/LicenseType";
import { FileText } from "lucide-react";
import { useRouter } from "next/navigation";

export default function LicenseApprovals() {
  const { push } = useRouter();
  const { data, isLoading, isError } = useGetLicenses({
    page: "0",
    status: "Pending Approval",
    approved: "false",
    sort: "applicationDate,asc",
    refetchIntervalDuration: 30000,
  });

  if (isLoading)
    return (
      <div className="flex justify-center px-2">
        <div className="h-6 w-6 animate-spin rounded-full border-b-2 border-blue-500"></div>
      </div>
    );
  if (isError)
    return (
      <div className="px-2 text-red-500">Error loading approval data...</div>
    );

  return (
    <div className="h-full">
      <h3 className="mb-3 flex items-center justify-between px-2 font-bold tracking-tight text-neutral-700">
        <div>License Approvals</div>
        <div>{data?.totalElements ?? "N/A"}</div>
      </h3>
      <div className="flex flex-col gap-2">
        {data.content.length === 0 && (
          <div className="rounded-lg bg-slate-50 p-4 text-center text-slate-500">
            <FileText className="mx-auto mb-2 h-8 w-8 text-slate-300" />
            <p>No pending approvals</p>
          </div>
        )}
        {data?.content
          .slice(0, data.content.length > 5 ? 5 : data.content.length)
          .map((item) => (
            <LicenseApprovalItem item={item} key={item.license.entityId} />
          ))}
        {data.content.length > 0 && (
          <Button
            onClick={() => {
              const firstLicenseId = data.content[0]?.license?.entityId;
              push(`/approval/license/dog${firstLicenseId ? `?licenseId=${firstLicenseId}` : ''}`);
            }}
            className="mt-2 w-full bg-blue-600 text-white hover:bg-blue-700"
            size={"sm"}
          >
            View All
          </Button>
        )}
      </div>
    </div>
  );
}

interface LicenseData {
  license: License;
  individual: Individual[];
  dog: Dog[];
}

const LicenseApprovalItem = ({ item }: { item: LicenseData }) => {
  const { push } = useRouter();

  const firstName = item?.individual?.[0]?.firstName ?? "N/A";
  const lastName = item?.individual?.[0]?.lastName ?? "N/A";
  const licenseNumber = item.license.licenseNumber;
  const licenseType = item.license.licenseType.name;

  return (
    <button
      onClick={() => {
        push(`/approval/license/dog?licenseId=${item.license.entityId}`);
      }}
      className="flex justify-between rounded-lg border border-slate-200 px-3 py-2 text-sm transition-colors hover:border-blue-300 hover:bg-blue-50"
    >
      <div>
        <div className="text-left text-xs text-slate-500">{licenseNumber}</div>
        <div className="line-clamp-1 break-all text-left font-medium">
          {firstName + " " + lastName}
        </div>
      </div>
      <div className="shrink-0 self-center">{licenseType}</div>
    </button>
  );
};
