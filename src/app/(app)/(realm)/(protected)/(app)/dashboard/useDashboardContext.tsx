import React, { createContext, useContext, useState, ReactNode } from "react";

type DashboardContextType = {
  date: Date;
  setDate: React.Dispatch<React.SetStateAction<Date>>;
  sortType: string;
  setSortType: React.Dispatch<React.SetStateAction<string>>;
};

const DashboardContext = createContext<DashboardContextType | undefined>(
  undefined,
);

export const DashboardProvider = ({ children }: { children: ReactNode }) => {
  const [date, setDate] = useState<Date>(new Date());
  const [sortType, setSortType] = useState<string>("month");

  return (
    <DashboardContext.Provider value={{ date, setDate, sortType, setSortType }}>
      {children}
    </DashboardContext.Provider>
  );
};

export const useDashboardContext = () => {
  const context = useContext(DashboardContext);
  if (context === undefined) {
    throw new Error(
      "useDashboardContext must be used within a DashboardProvider",
    );
  }
  return context;
};
