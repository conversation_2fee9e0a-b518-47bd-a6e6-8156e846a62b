import React from "react";

export function ListLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="relative flex min-h-[500px] flex-col gap-4 rounded bg-white p-4 shadow w-full shrink-0">
      {children}
    </div>
  );
}

export const ListContent = ({ children }: { children: React.ReactNode }) => {
  return (
    <div className="flex h-full w-full justify-center overflow-auto">
      {children}
    </div>
  );
};

export const ListTitle = ({ title, children }: { title: string, children?: React.ReactNode }) => {
  return (
    <div className="flex items-center justify-between">
      <h2 className="text-2xl">{title}</h2>
      {children}
    </div>
  );
};
