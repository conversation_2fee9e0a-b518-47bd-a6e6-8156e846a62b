import { useGetLicenses } from "@/hooks/api/useLicense";
import React, { useState } from "react";
import { MdOutlinePendingActions } from "react-icons/md";
import { currentRange, rangeDisplay } from "../dashboardHelper";

export default function NewAccounts({ range, date }: { range: string, date: Date}) {
  const [now, setNow ] = useState(new Date(date));
  
  const { data, isLoading, isError } = useGetLicenses({
    issuedDate: currentRange(range, now),
    // status: 'Expired',
    size: '1',
    approved: 'false'
  });

  if(isLoading) return <div>Loading...</div>
  if(isError) return <div>Error...</div>

  console.log(data)

  return (
    <div className="shadow bg-white rounded p-4 flex gap-4 items-center relative">
      <div className="bg-black p-1 text-white w-10 h-10 flex items-center justify-center rounded-full">
        <MdOutlinePendingActions />
      </div>
      <div>
        <div className="text-3xl font-medium">{data.totalElements} </div>
        <div className=" text-neutral-600">New Accounts</div>
        <div className={`
          text-xs absolute top-0 right-2 -translate-y-1/2 rounded px-2 py-1 shadow
          bg-clerk-primary text-white
        `}>{rangeDisplay(range,now)}</div>
      </div>
    </div>
  );
}
