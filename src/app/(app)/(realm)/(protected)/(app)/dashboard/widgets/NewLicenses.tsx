import { useGetLicenses } from "@/hooks/api/useLicense";
import {
  endOfMonth,
  endOfWeek,
  format,
  isThisMonth,
  startOfMonth,
  startOfWeek,
} from "date-fns";
import React, { useState } from "react";
import { MdOutlinePendingActions } from "react-icons/md";
import { currentRange, rangeDisplay } from "../dashboardHelper";
import { BiEqualizer, BiTrendingDown, BiTrendingUp } from "react-icons/bi";
import LoadingSpinner from "@/components/ui/LoadingSpinner";

const Icon = ({ current, previous }: { current: number; previous: number }) => {
  if (current > previous) return <BiTrendingUp className="text-green-500" />;
  if (current < previous) return <BiTrendingDown className="text-red-500" />;
  if (current === previous) return <BiEqualizer className="text-neutral-500" />;
  return null;
};

export default function NewLicenses({
  range,
  date,
}: {
  range: string;
  date: Date;
}) {
  // previous year
  const prevYear = new Date(date);
  prevYear.setFullYear(prevYear.getFullYear() - 1);

  // previous Month
  const prevMonth = new Date(date);
  prevMonth.setMonth(prevMonth.getMonth() - 1);

  const { data, isLoading, isError } = useGetLicenses({
    issuedDate: currentRange(range, date),
    size: "1",
    status: "active",
    refetchIntervalDuration: 30000,
  });

  if (isLoading)
    return (
      <div className="shadow bg-white rounded p-4 flex items-center justify-center flex-col gap-4 relative">
        <LoadingSpinner />
      </div>
    );
  if (isError) return <div>Error...</div>;

  return (
    <div className="shadow bg-white rounded p-4 flex flex-col gap-4 relative">
      <div className="flex gap-4 items-center w-full">
        <div className="bg-black p-1 text-white w-10 h-10 flex items-center justify-center rounded-full">
          <MdOutlinePendingActions />
        </div>
        <div>
          <div className="text-3xl font-medium">{data.totalElements} </div>
          <div className=" text-neutral-600">Licenses Processed</div>
          {/* <div
            className={`
            text-xs absolute top-0 right-2 -translate-y-1/2 rounded px-2 py-1 shadow
            bg-clerk-primary text-white
          `}
          >
            {rangeDisplay(range, date)}
          </div> */}
        </div>
      </div>
      <div className="flex justify-between">
        {/* <div className="flex gap-2 items-center">
          {previousYear?.totalElements} YoY{" "}
          <Icon
            current={data.totalElements}
            previous={previousYear?.totalElements || 0}
          />
        </div> */}
        {/* <div className="flex gap-2 items-center">
          {previousMonth?.totalElements} MoM{" "}
          <Icon
            current={data.totalElements}
            previous={previousMonth?.totalElements || 0}
          />
        </div> */}
      </div>
    </div>
  );
}
