"use client";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { useRouter } from "next/navigation";
import { useMyProfile } from "@/hooks/providers/useMyProfile";
import { BarChart3 } from "lucide-react";
import Overview from "./overview/Overview";
import { DashboardProvider } from "./useDashboardContext";

const DashboardContent = () => {
  const { push } = useRouter();
  const { hasPermissions } = useMyProfile();

  if (hasPermissions(["resident"])) {
    push(`/home`);
  }

  return (
    <DashboardProvider>
      <Tabs
        defaultValue="overview"
        className="container mx-auto flex flex-col p-6 overflow-auto"
      >
        <div className="mb-6 flex w-full items-start justify-start ">
          <TabsList className="bg-white shadow-sm">
            <TabsTrigger
              value="overview"
              className="flex items-center gap-2 data-[state=active]:bg-blue-100 data-[state=active]:text-blue-700"
            >
              <BarChart3 className="h-4 w-4" />
              Overview
            </TabsTrigger>
            {/* <TabsTrigger
              value="licenses"
              className="flex items-center gap-2 data-[state=active]:bg-blue-100 data-[state=active]:text-blue-700"
            >
              <FileText className="h-4 w-4" />
              Licenses
            </TabsTrigger>
            <TabsTrigger
              value="approvals"
              className="flex items-center gap-2 data-[state=active]:bg-blue-100 data-[state=active]:text-blue-700"
            >
              <Award className="h-4 w-4" />
              Approvals
            </TabsTrigger> */}
          </TabsList>
        </div>

        <TabsContent value="overview">
          <Overview />
        </TabsContent>

        {/* Will add something back in the future -- Sean B */}
        {/* <TabsContent value="licenses">
          <Card className="p-6 shadow-sm">
            <h2 className="mb-4 text-xl font-bold">License Management</h2>
            <p className="text-slate-600">
              Manage all license types and perform bulk operations.
            </p>
          </Card>
        </TabsContent>

        <TabsContent value="approvals">
          <Card className="p-6 shadow-sm">
            <h2 className="mb-4 text-xl font-bold">Approval Workflows</h2>
            <p className="text-slate-600">
              Review and process all pending approval requests.
            </p>
          </Card>
        </TabsContent> */}
      </Tabs>
    </DashboardProvider>
  );
};

export default DashboardContent;
