"use client";
import { Individual } from "@/types/IndividualType";
import { Switch } from "@/components/ui/switch";
import { useAtom } from "jotai";
import { toast<PERSON>tom } from "@/components/ui/toast/toast";
import { useQueryClient } from "@tanstack/react-query";
import {
  useClerkDeactivateResident,
  useClerkReactivateResident,
  useDisableResidentProfile,
  useGetProfile,
  useUpdateEntityProfile2,
} from "@/hooks/api/useProfiles";
import { Card, CardHeader } from "@/components/ui/card";
import PageContainer from "@/components/ui/Page/PageContainer";
import { motion } from "framer-motion";
import { Title } from "@/components/profile/ProfileComponents";
import { Section } from "../profile/Dialogs";
import { createFormData } from "@/app/(app)/(realm)/(protected)/(resident)/(registered)/resident/[entityType]/[entityId]/components/modals/EditDialog";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  <PERSON>alog<PERSON>ooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { useState } from "react";

type Params = {
  [key: string]: any;
};

export type OptInStateType = {
  [key: string]: boolean;
}[];

// Function to format the optIns array
const formatOptIns = (optIns: any) => {
  const formattedOptIns: OptInStateType = optIns.map((a: any) => {
    return {
      [a.name]: a.active,
    };
  });

  return formattedOptIns;
};

export default function Page({
  params: { entityId, entitytype },
}: {
  params: { entityId: string; entitytype: string };
}) {
  const { data, isLoading, isError } = useGetProfile(
    entitytype as string,
    entityId as string,
  );

  const [open, setOpen] = useState(false);

  console.log(data);

  const {
    individual,
  }: {
    individual: Individual;
  } = data;

  const accountActive = individual.active;
  console.log(accountActive);

  const updateEntity = useUpdateEntityProfile2();
  const deactivateAccount = useClerkDeactivateResident(entityId);
  const reactivateAccount = useClerkReactivateResident(entityId);
  const updateStatus = accountActive ? deactivateAccount : reactivateAccount;
  const clerkDisableResidentOnlineAccount = useDisableResidentProfile();
  const [, setToast] = useAtom(toastAtom);
  const queryClient = useQueryClient();

  const [entityStatus, setEntityStatus] = useState(accountActive);
  const [entityOptIns, setEntityOptIns] = useState<OptInStateType>(
    formatOptIns(individual.optIns),
  );

  const handleSave = (fieldName: string, value: any, params?: Params) => {
    let data = {};

    if (params) {
      data = { ...params };
    } else data = { [fieldName]: value };

    const formData = createFormData(data);

    updateEntity.mutate(
      {
        entityId: individual.entityId,
        body: formData,
      },
      {
        onSuccess: () => {
          queryClient.invalidateQueries();
        },
        onError: (error: any) => {
          console.log(error);
          setToast({
            status: "error",
            label: "Error Updating Profile",
            message: error?.response?.data?.message,
          });
          setEntityOptIns((prev) => {
            return prev.map((a) => {
              return {
                ...a,
                [fieldName]: !value,
              };
            });
          });
        },
      },
    );
  };

  if (isLoading) {
    return (
      <div className="container">
        <Card>
          <CardHeader>
            <h1>Loading Settings...</h1>
          </CardHeader>
        </Card>
      </div>
    );
  }

  if (isError) {
    return (
      <div className="container">
        <PageContainer>
          <h1>Error Loading Page</h1>
        </PageContainer>
      </div>
    );
  }

  // const accountStatus = individual.status.toLowerCase() === "active";

  return (
    <motion.div
      initial={{ opacity: 0, x: -100 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: -100 }}
      className="mx-auto flex flex-col gap-10 sm:container "
    >
      <PageContainer>
        <div className="flex flex-col gap-10 xl:flex-row">
          <div className="flex w-full flex-col gap-7">
            <div className="flex w-full flex-col gap-20">
              {/* Document Management */}
              <div>
                <Title>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <span>Document Management</span>
                    </div>
                  </div>
                </Title>

                <div className="flex flex-col gap-4">
                  {optIns.map((a) => {
                    const checked = entityOptIns.find(
                      (b) => b[a.key] === true,
                    ) as any;

                    return (
                      <Section label={a.label} key={a.key}>
                        <Switch
                          checked={checked}
                          onCheckedChange={(value) => {
                            const newOptIns = entityOptIns.map((b) => {
                              if (b[a.key]) {
                                return {
                                  [a.key]: value,
                                };
                              }
                              return b;
                            });

                            setEntityOptIns(newOptIns);
                            handleSave(a.key, value);
                          }}
                        />
                      </Section>
                    );
                  })}
                </div>
              </div>

              {/* Security */}
              {/* TODO: add back later */}
              {/* <div>
                <Title>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <span>Account Security</span>
                    </div>
                  </div>
                </Title>

                <div className="flex flex-col gap-4">
                  <Section label={`Status (${individual.status})`}>
                    <Switch
                      checked={entityStatus}
                      onCheckedChange={(value) => {
                        setEntityStatus(value);
                        updateStatus.mutate(
                          {
                            entityId: individual.entityId as string,
                            body: new FormData(),
                          },
                          {
                            onSuccess: () => {
                              queryClient.invalidateQueries();
                              setOpen(false);
                            },
                            onError: (error: any) => {
                              console.log(error);
                              setToast({
                                status: "error",
                                label: "Error Disabling Account",
                                message: error?.response?.data?.message,
                              });
                              setEntityStatus(!value);
                            },
                          },
                        );
                      }}
                    />
                  </Section>
                </div>
              </div> */}

              {/* Disable Account */}
              <div className="rounded-lg border-2 border-red-600 bg-red-50/60 p-4">
                <Title>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <span>Permanent Settings</span>
                    </div>
                  </div>
                </Title>

                <div className="flex flex-col gap-4">
                  <Dialog open={open} onOpenChange={setOpen}>
                    <DialogTrigger asChild>
                      <Button
                        className="w-fit hover:bg-red-400"
                        size="sm"
                        variant="destructive"
                        onClick={() => setOpen(true)}
                      >
                        Delete Online Account
                      </Button>
                    </DialogTrigger>
                    <DialogContent>
                      <DialogHeader>
                        <DialogTitle>Are you absolutely sure?</DialogTitle>
                        <DialogDescription>
                          This will delete this user&apos;s online account.
                        </DialogDescription>
                      </DialogHeader>
                      <DialogFooter>
                        <Button
                          variant="outline"
                          onClick={() => setOpen(false)}
                        >
                          Cancel
                        </Button>
                        <Button
                          variant="destructive"
                          onClick={() => {
                            console.log("Disabling Account");
                            clerkDisableResidentOnlineAccount.mutate(
                              {
                                entityId: individual.entityId as string,
                              },
                              {
                                onSuccess: () => {
                                  queryClient.invalidateQueries();
                                  setToast({
                                    status: "success",
                                    label: "Account Disabled",
                                    message: "Successfully Disabled Account",
                                  });
                                  setOpen(false);
                                },
                                onError: (error: any) => {
                                  console.log(error);
                                  setToast({
                                    status: "error",
                                    label: "Error Disabling Account",
                                    message: error?.response?.data?.message,
                                  });
                                },
                              },
                            );
                          }}
                          className="hover:bg-red-400"
                        >
                          Continue
                        </Button>
                      </DialogFooter>
                    </DialogContent>
                  </Dialog>
                </div>
              </div>
            </div>
          </div>
        </div>
      </PageContainer>
    </motion.div>
  );
}

const optIns: {
  key: string;
  label: string;
}[] = [
  {
    key: "optInPaperless",
    label: "Paperless",
  },
  // {
  //   key: "optInLicenseUpdates",
  //   label: "License Updates",
  // },
  // {
  //   key: "optInDogUpdates",
  //   label: "Dog Updates",
  // },
];
