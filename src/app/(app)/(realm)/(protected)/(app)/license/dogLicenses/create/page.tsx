"use client";
import React from "react";
import Machine from "@/components/formBuilderV2/components/Machine";
import { useSearchParams } from "next/navigation";
import { useClerkGetResident } from "@/hooks/api/useResident";
import { useGetJSONFormConfig } from "@/hooks/api/useAdmin";

const CreateNewDogLicensePage = () => {
  const searchParams = useSearchParams();
  const individualId = searchParams.get("individualId");

  const { data, isError, isLoading } = useClerkGetResident(individualId ?? "");
  const {
    data: config,
    isLoading: jsonLoading,
    isError: errorLoading,
  } = useGetJSONFormConfig("license", "dog", "admin");

  if (isLoading || jsonLoading) return <div className="p-6">Loading...</div>;
  if (isError || errorLoading)
    return <div className="p-6">Error Loading Resident</div>;

  if (data) {
    return <Machine config={config} />;
  }

  return <div>No User Found</div>;
};

export default CreateNewDogLicensePage;
 