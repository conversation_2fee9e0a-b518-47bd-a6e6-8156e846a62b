import Button from '@/components/ui/buttons/Button';
import { useParams, usePathname, useSearchParams } from 'next/navigation';
import { useRouter } from 'next/navigation';
import React, { useCallback, useEffect, useState } from 'react'
import { endOfMonth, startOfMonth } from 'date-fns';
import { currentRange } from '../../../dashboard/dashboardHelper';

export default function FilterDates({
  name,
  dates,
  setDates
}:{
  name: string
  dates: string | null,
  setDates: (value: string) => void
}) {

  const parseDates = (datesString: string | null) => {
    if (!datesString) {
      const dates = currentRange('month', new Date());
      const [startStr, endStr] = dates.split(',');
  
      return {
        start: new Date(startStr),
        end: new Date(endStr)
      };
    }
  
    const [startStr, endStr] = datesString.split(',');
    return {
      start: startStr ? new Date(startStr) : new Date(),
      end: endStr ? new Date(endStr) : new Date()
    };
  };
  



  const initialDates = parseDates(dates);

  const [startDateString, setStartDateString] = useState(initialDates.start.toISOString().split('T')[0]);
  const [endDateString, setEndDateString] = useState(initialDates.end.toISOString().split('T')[0]);

  const validateAndParseDate = (dateString:string | null) => {
    if (!dateString) return null;
    
    if (/^\d{4}-\d{2}-\d{2}$/.test(dateString)) {
      return new Date(dateString);
    }
    return null;
  };

  useEffect(() => {
    const validStart = validateAndParseDate(startDateString);
    const validEnd = validateAndParseDate(endDateString);

    if (validStart && validEnd) {
      setDates(`${validStart.toISOString().split('T')[0]},${validEnd.toISOString().split('T')[0]}`);
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [startDateString, endDateString]);


  return (
    <div className='flex gap-10'>
      <div className='font-semibold'>
        {name}:
      </div>
      <div className='flex gap-2'>
        From:
        <input 
          type="date" 
          value={startDateString}
          onChange={(e) => setStartDateString(e.target.value)}
        />
      </div>
      <div className='flex gap-2'>
        To:
        <input 
          type="date" 
          value={endDateString}
          onChange={(e) => setEndDateString(e.target.value)}
        />
      </div>
    </div>
  )
}
