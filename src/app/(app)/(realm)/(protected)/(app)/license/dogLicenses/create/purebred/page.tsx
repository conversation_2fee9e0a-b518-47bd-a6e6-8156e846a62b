"use client";
import { useSearchParams } from "next/navigation";
import { useClerkGetResident } from "@/hooks/api/useResident";
import Machine from "@/components/formBuilderV2/components/Machine";
import config from "@/components/formBuilderV2/forms/newPurebredDogLicenseConfigClerk.json";

const CreateNewPurebredDogLicensePage = () => {
  const searchParams = useSearchParams();
  const participantId = searchParams.get("entityId");
  const licenseType = searchParams.get("entityType");

  const { data, isError, isLoading } = useClerkGetResident(participantId ?? "");

  if (isLoading) return <div>Loading...</div>;
  if (isError) return <div>Error Loading Resident</div>;


  if (data) {
    const additionalContext = {
      licenseType,
      participantId,
    };

    console.log(config)

    return <Machine config={config} additionalContext={additionalContext} />;
  }

  return <div>No User Found</div>;
};

export default CreateNewPurebredDogLicensePage;