"use client";
import FormBuilder from "@/components/forms/formbuilder/FormBuilder";
import React from "react";

import { useSearchParams } from "next/navigation";

import { useGetProfile } from "@/hooks/api/useProfiles";
import LoadingSpinner from "@/components/ui/LoadingSpinner";

const DraftDogLicensePage = () => {
  const params = useSearchParams();
  const entityType = params.get("entityType");
  const entityId = params.get("entityId");

  const { data, isLoading, isError } = useGetProfile(
    entityType ?? "",
    entityId ?? "",
  );

  // const form = useGetForm("draftDogLicenseForm");

  if (isLoading) return <LoadingSpinner />;
  if (isError) return <div>Failed to load data</div>;

  if (data) {
    return <FormBuilder form={draftDogLicenseForm} prevData={data?.dog} />;
  }

  return <div>Failed to load data</div>;
};
export default DraftDogLicensePage;
