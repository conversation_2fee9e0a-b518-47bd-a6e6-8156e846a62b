"use client"

import * as React from "react"
import { DropdownMenuCheckboxItemProps } from "@radix-ui/react-dropdown-menu"

import { Button } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { FilterConfigProps } from "./page"

type Checked = DropdownMenuCheckboxItemProps["checked"]

type FilterOptionProps = {
  filterConfig: FilterConfigProps[];
  setFilterConfig: (newConfig: FilterConfigProps[]) => void;
};


export function FilterOption({
  filterConfig,
  setFilterConfig
}: FilterOptionProps) {

  const handleCheckboxChange = (key: string, checked: Checked) => {
    setFilterConfig(
      filterConfig.map((configItem) =>
        configItem.key === key ? { ...configItem, isChecked: !!checked } : configItem
      )
    );
  };
  

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline">Filters</Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-56">
        {
          filterConfig.map(({ key, label, isChecked }) => (
            <DropdownMenuCheckboxItem
              key={key}
              checked={isChecked}
              onCheckedChange={(checked) => handleCheckboxChange(key, checked)}
            >
              {label}
            </DropdownMenuCheckboxItem>
          ))
        }
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
