"use client";
import { useGetLicenses } from "@/hooks/api/useLicense";
import { format } from "date-fns";
import React, { useEffect, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import Link from "next/link";
import { FiExternalLink, FiFilter, FiSearch, FiX } from "react-icons/fi";
import AvatarImage, { getAvatarBlob } from "@/components/AvatarImage";

// UI Components
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { CalendarIcon } from "lucide-react";
import { Calendar } from "@/components/ui/calendar";
import PaginationLayout from "../../components/PaginationLayout";

// Types
import { License } from "@/types/LicenseType";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Button } from "@/components/ui/button";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";

// Filter config types
export type FilterConfigProps = {
  key: string;
  label: string;
  value: string | null;
  isChecked: boolean;
  type: string;
};

type ColumnsProp = {
  key: string;
  label: string;
  sortable?: boolean;
  className?: string;
};

type LicenseObjectProps = {
  size: string;
  page: string;
  [key: string]: string | null | undefined;
};

// Date formatter helper
interface FormatDate {
  (date: string | Date | null | undefined): string;
}

const formatDate: FormatDate = (date) => {
  if (!date) return "N/A";
  return format(new Date(date), "MMM d, yyyy");
};

// Status badge component
const StatusBadge = ({ status }: { status: string | null }) => {
  interface GetStatusColor {
    (status: string | null | undefined): string;
  }

  const getStatusColor: GetStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case "active":
        return "bg-green-100 text-green-800 border-green-300";
      case "expired":
        return "bg-red-100 text-red-800 border-red-300";
      case "canceled":
        return "bg-neutral-100 text-neutral-800 border-neutral-300";
      default:
        return "bg-gray-100 text-gray-800 border-gray-300";
    }
  };

  return (
    <span
      className={`inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-medium ${getStatusColor(
        status,
      )}`}
    >
      {status || "Unknown"}
    </span>
  );
};

// Date picker component
type DatePickerProps = {
  value: string;
  onChange: (value: string | null) => void;
  label: string;
};

const DatePicker = ({ value, onChange, label }: DatePickerProps) => {
  const [date, setDate] = useState(value ? new Date(value) : null);

  return (
    <div className="flex flex-col gap-2">
      <label className="text-sm font-medium text-gray-700">{label}</label>
      <Popover>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            className="w-full justify-start text-left font-normal"
          >
            {date ? (
              formatDate(date)
            ) : (
              <span className="text-gray-400">Select a date</span>
            )}
            <CalendarIcon className="ml-auto h-4 w-4 text-gray-500" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0">
          <Calendar
            mode="single"
            selected={date ?? undefined}
            onSelect={(newDate) => {
              setDate(newDate ?? null);
              onChange(newDate ? format(newDate, "yyyy-MM-dd") : null);
            }}
            initialFocus
          />
        </PopoverContent>
      </Popover>
    </div>
  );
};

export default function DogLicensesPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [isFiltersVisible, setIsFiltersVisible] = useState(false);
  const [licenseObject, setLicenseObject] = useState<LicenseObjectProps | null>(
    null,
  );
  const [searchTerm, setSearchTerm] = useState(
    searchParams.get("search") || "",
  );

  // Pagination state
  const [page, setPage] = useState(searchParams.get("page") || "0");
  const [size, setSize] = useState(searchParams.get("size") || "10");
  const [sort, setSort] = useState(
    searchParams.get("sort") || "issuedDate,desc",
  );
  const [status, setStatus] = useState(searchParams.get("status") || "active");

  // Helper functions
  const getParam = (key: string) => {
    return hasParam(key) ? searchParams.get(key) : null;
  };

  const hasParam = (key: string): boolean => {
    return searchParams.has(key);
  };

  // Filter configuration
  const [filterConfig, setFilterConfig] = useState<FilterConfigProps[]>([
    {
      key: "validFromDate",
      label: "License Start Date",
      isChecked: hasParam("validFromDate"),
      value: getParam("validFromDate"),
      type: "date",
    },
    {
      key: "validToDate",
      label: "License Expiration Date",
      isChecked: hasParam("validToDate"),
      value: getParam("validToDate"),
      type: "date",
    },
    {
      key: "issuedDate",
      label: "Issued Date",
      isChecked: hasParam("issuedDate"),
      value: getParam("issuedDate"),
      type: "date",
    },
  ]);

  // Status filter configuration
  const [statusConfig, setStatusConfig] = useState<FilterConfigProps[]>([
    {
      key: "active",
      label: "Active",
      value: getParam("status"),
      isChecked: status === "active",
      type: "text",
    },
    {
      key: "expired",
      label: "Expired",
      value: getParam("status"),
      isChecked: status === "expired",
      type: "text",
    },
    {
      key: "canceled",
      label: "Canceled",
      value: getParam("status"),
      isChecked: status === "canceled",
      type: "text",
    },
  ]);

  // Table columns definition
  const columns: ColumnsProp[] = [
    {
      key: "licenseNumber",
      label: "License #",
      sortable: true,
      className: "w-[120px]",
    },
    {
      key: "licenseType.name",
      label: "License Type",
      sortable: true,
    },
    {
      key: "validFromDate",
      label: "Start Date",
      sortable: true,
    },
    {
      key: "validToDate",
      label: "Expiration",
      sortable: true,
    },
    {
      key: "resident",
      label: "Resident",
      sortable: false,
    },
    {
      key: "status",
      label: "Status",
      sortable: true,
      className: "w-[100px]",
    },
    {
      key: "issuedDate",
      label: "Issued Date",
      sortable: true,
      className: "text-right w-[130px]",
    },
  ];

  // Create license object for API call
  const createLicenseObject = () => {
    const LicenseObjectDraft: LicenseObjectProps = {
      size: size,
      page: page,
      sort: sort,
      status: status,
    };

    filterConfig.forEach((filter) => {
      if (filter.isChecked) {
        LicenseObjectDraft[filter.key] = filter.value;
      }
    });

    // Add search term if available
    if (searchTerm) {
      LicenseObjectDraft.search = searchTerm;
    }

    setLicenseObject(LicenseObjectDraft);
  };

  // API data fetch
  const { data, isLoading, isError, refetch } = useGetLicenses(
    licenseObject ?? {},
    false,
  );

  // Initialize licenseObject on first load
  useEffect(() => {
    createLicenseObject();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Refetch when licenseObject changes
  useEffect(() => {
    if (licenseObject) {
      refetch();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [licenseObject]);

  // Handle search & filters
  const handleSearch = () => {
    let queryParams = new URLSearchParams();

    // Add all active filters
    filterConfig.forEach((filter) => {
      if (filter.isChecked && filter.value) {
        queryParams.set(filter.key, filter.value);
      } else {
        queryParams.delete(filter.key);
      }
    });

    // Add pagination and sorting
    if (sort) queryParams.set("sort", sort);
    if (size) queryParams.set("size", size);
    if (page) queryParams.set("page", page);
    if (status) queryParams.set("status", status);

    // Add search term if available
    if (searchTerm) queryParams.set("search", searchTerm);

    const queryString = queryParams.toString();

    // Update URL and trigger data fetch
    router.push(`/license/dogLicenses/dogLicenses?${queryString}`);

    // Reset to page 0 when performing a new search
    if (searchTerm) {
      setPage("0");
    }

    // Create and set the license object to trigger API call
    createLicenseObject();
  };

  // Update URL and trigger data fetch when pagination/sorting changes
  useEffect(() => {
    // Don't trigger this on initial render
    if (licenseObject !== null) {
      handleSearch();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [page, size, sort, status]);

  // Update filter config
  const updateFilterConfig = (key: string, newValue: string | null) => {
    setFilterConfig((currentConfig) =>
      currentConfig.map((fc) =>
        fc.key === key ? { ...fc, value: newValue } : fc,
      ),
    );
  };

  // Toggle filter checked state
  const toggleFilterChecked = (key: string) => {
    setFilterConfig((currentConfig) =>
      currentConfig.map((fc) =>
        fc.key === key ? { ...fc, isChecked: !fc.isChecked } : fc,
      ),
    );
  };

  // Update status filter
  const handleStatusChange = (newStatus: string) => {
    setStatus(newStatus);

    setStatusConfig((currentConfig) =>
      currentConfig.map((sc) => ({
        ...sc,
        isChecked: sc.key === newStatus,
      })),
    );
  };

  // Pagination handlers
  const handlePrevious = () => {
    const newPage = Math.max(0, parseInt(page) - 1).toString();
    setPage(newPage);
  };

  const handleNext = () => {
    if (!data) return;
    const newPage = Math.min(
      data?.totalPages - 1,
      parseInt(page) + 1,
    ).toString();
    setPage(newPage);
  };

  const handlePageSelect = (index: number) => {
    setPage(index.toString());
  };

  // Sorting handler
  const handleSort = (columnKey: string) => {
    const newSort =
      sort === `${columnKey},asc` ? `${columnKey},desc` : `${columnKey},asc`;
    setSort(newSort);
  };

  // Clear all filters
  const clearFilters = () => {
    setFilterConfig((current) =>
      current.map((filter) => ({ ...filter, isChecked: false, value: null })),
    );
    setSearchTerm("");
    setPage("0");
    handleSearch(); // Important: trigger search after clearing filters
  };

  return (
    <div className="flex h-full flex-col overflow-auto overflow-x-hidden bg-gray-50 p-6">
      <Card className="border-0 shadow-sm">
        <CardHeader className="px-6 pb-0 pt-6">
          <div className="flex flex-col justify-between gap-4 sm:flex-row sm:items-center">
            <CardTitle className="text-2xl font-bold">Dog Licenses</CardTitle>

            <div className="flex items-center gap-3">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsFiltersVisible(!isFiltersVisible)}
                className="flex items-center gap-1"
              >
                <FiFilter className="h-4 w-4" />
                Filters
                {filterConfig.some((f) => f.isChecked) && (
                  <Badge
                    variant="secondary"
                    className="ml-1 px-1.5 py-0 text-xs"
                  >
                    {filterConfig.filter((f) => f.isChecked).length}
                  </Badge>
                )}
              </Button>

              <Select value={size} onValueChange={(value) => setSize(value)}>
                <SelectTrigger className="w-[130px]">
                  <SelectValue placeholder={`${size} per page`} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="10">10 per page</SelectItem>
                  <SelectItem value="25">25 per page</SelectItem>
                  <SelectItem value="50">50 per page</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardHeader>

        <CardContent className="px-6 py-4">
          {/* Status Tabs and Search */}
          <div className="mb-6 flex flex-col items-start justify-between gap-4 md:flex-row md:items-center">
            <Tabs
              value={status}
              onValueChange={handleStatusChange}
              className="w-full md:w-auto"
            >
              <TabsList>
                <TabsTrigger value="active">Active</TabsTrigger>
                <TabsTrigger value="expired">Expired</TabsTrigger>
                <TabsTrigger value="canceled">Canceled</TabsTrigger>
              </TabsList>
            </Tabs>
          </div>

          {/* Filters Panel */}
          {isFiltersVisible && (
            <div className="mb-6 rounded-md border border-gray-200 bg-gray-50 p-4">
              <div className="mb-4 flex items-center justify-between">
                <h3 className="font-medium">Filter Options</h3>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={clearFilters}
                  className="text-sm text-gray-500"
                >
                  Clear All
                </Button>
              </div>

              <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                {filterConfig.map((filter) => (
                  <div key={filter.key} className="space-y-2">
                    <div className="flex items-center gap-2">
                      <input
                        type="checkbox"
                        id={filter.key}
                        checked={filter.isChecked}
                        onChange={() => toggleFilterChecked(filter.key)}
                        className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <label
                        htmlFor={filter.key}
                        className="text-sm font-medium text-gray-700"
                      >
                        {filter.label}
                      </label>
                    </div>

                    {filter.isChecked && filter.type === "date" && (
                      <DatePicker
                        value={filter.value || ""}
                        onChange={(value) =>
                          updateFilterConfig(filter.key, value)
                        }
                        label=""
                      />
                    )}
                  </div>
                ))}
              </div>

              <div className="mt-4 flex justify-end">
                <Button onClick={handleSearch}>Apply Filters</Button>
              </div>
            </div>
          )}

          {/* Data Table */}
          {isLoading ? (
            <div className="space-y-2">
              {[...Array(5)].map((_, i) => (
                <Skeleton key={i} className="h-12 w-full bg-gray-100" />
              ))}
            </div>
          ) : isError ? (
            <div className="p-8 text-center">
              <div className="text-gray-500">Error loading license data</div>
              <Button
                variant="outline"
                onClick={() => refetch()}
                className="mt-4"
              >
                Try Again
              </Button>
            </div>
          ) : data?.content?.length === 0 ? (
            <div className="p-8 text-center">
              <div className="text-gray-500">
                No licenses found with the current filters
              </div>
              <Button variant="outline" onClick={clearFilters} className="mt-4">
                Clear Filters
              </Button>
            </div>
          ) : (
            <>
              <div className="overflow-x-auto rounded-md border border-gray-200">
                <Table>
                  <TableHeader>
                    <TableRow className="bg-gray-50">
                      {columns.map((column) => (
                        <TableHead
                          key={column.key}
                          className={`${column.className || ""} py-3 text-xs font-medium uppercase tracking-wider text-gray-500`}
                        >
                          {column.sortable ? (
                            <button
                              onClick={() => handleSort(column.key)}
                              className="flex items-center gap-1 hover:text-blue-600"
                            >
                              {column.label}
                              {column.key === sort?.split(",")[0] && (
                                <span className="text-blue-600">
                                  {sort?.split(",")[1] === "asc" ? " ▲" : " ▼"}
                                </span>
                              )}
                            </button>
                          ) : (
                            column.label
                          )}
                        </TableHead>
                      ))}
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {data?.content?.map((data) => {
                      const license = data.license as License;
                      const individual =
                        data?.individual &&
                        Array.isArray(data.individual) &&
                        data.individual.length > 0
                          ? data.individual[0]
                          : null;

                      const name = individual
                        ? `${individual.firstName} ${individual.lastName}`
                        : "N/A";

                      return (
                        <TableRow
                          key={license.entityId}
                          onClick={() => {
                            router.push(
                              `/a/license/${license.entityId}?tab=license`,
                            );
                          }}
                          className="cursor-pointer transition-colors hover:bg-blue-50"
                        >
                          <TableCell className="font-medium">
                            <div className="flex items-center gap-1">
                              {license.licenseNumber}
                              <Link
                                href={`/a/license/${license.entityId}?tab=license`}
                                target="_blank"
                                onClick={(e) => {
                                  e.stopPropagation();
                                }}
                                className="ml-1 text-neutral-500 hover:text-blue-700"
                              >
                                <FiExternalLink className="h-3.5 w-3.5" />
                              </Link>
                            </div>
                          </TableCell>
                          <TableCell>
                            {license.licenseType.name}
                            <span className="ml-1 text-xs text-gray-500">
                              ({license.activityType})
                            </span>
                          </TableCell>
                          <TableCell>
                            {formatDate(license.validFromDate)}
                          </TableCell>
                          <TableCell>
                            {formatDate(license.validToDate)}
                          </TableCell>
                          <TableCell>
                            {individual && (
                              <Link
                                href={`/profile/individual/${individual.entityId}?tab=profile`}
                                className="flex items-center gap-1 hover:text-blue-600"
                                onClick={(e) => {
                                  e.stopPropagation();
                                }}
                              >
                                <AvatarImage
                                  entityType="individual"
                                  src={
                                    getAvatarBlob(
                                      individual.documents,
                                    ) as string
                                  }
                                  alt={name}
                                  width={24}
                                  height={24}
                                  className="rounded-full border border-gray-200"
                                />
                                <span>{name}</span>
                                <Link
                                  href={`/profile/individual/${individual.entityId}?tab=profile`}
                                  target="_blank"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                  }}
                                  className="ml-1 text-neutral-500 hover:text-blue-700"
                                >
                                  <FiExternalLink className="h-3.5 w-3.5" />
                                </Link>
                              </Link>
                            )}
                          </TableCell>
                          <TableCell>
                            <StatusBadge status={license.status} />
                          </TableCell>
                          <TableCell className="text-right text-sm text-gray-500">
                            {formatDate(license.issuedDate)}
                          </TableCell>
                        </TableRow>
                      );
                    })}
                  </TableBody>
                </Table>
              </div>

              {/* Pagination */}
              <div className="mt-4">
                <PaginationLayout
                  currentPage={page}
                  totalPages={data.totalPages}
                  back={handlePrevious}
                  next={handleNext}
                  pageSelect={handlePageSelect}
                />
              </div>
            </>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
