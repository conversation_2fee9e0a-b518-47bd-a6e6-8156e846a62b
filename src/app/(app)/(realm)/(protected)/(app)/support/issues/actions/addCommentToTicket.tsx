"use server";
import { Issue } from "../../types/supportTypes";

var Redmine = require('node-redmine');

export async function addCommentToTicket(issueId: number, comment: string, apiKey: string | null) {
  var hostname = process.env.REDMINE_HOST || "";
  var config = {
    apiKey: apiKey || process.env.REDMINE_API_KEY,
  };

  const redmine = new Redmine(hostname, config);

  try {
    const updatedIssue = await new Promise<any>((resolve, reject) => {
      redmine.update_issue(
        issueId,
        { issue: { notes: comment } },
        function (err: any, data: any) {
          if (err) {
            // Check if the error is actually a 204 No Content response
            if (err.ErrorCode === 204) {
              console.log("Comment added successfully to issue ID:", issueId);
              resolve(data);
            } else {
              console.error("Error updating issue:", err);
              reject(err);
            }
          } else {
            resolve(data);
          }
        }
      );
    });

    return { success: true, issueId: issueId, data: updatedIssue };
  } catch (error) {
    console.error("Error caught in addCommentToTicket function:", error);
    return { success: false, error: error };
  }
}
