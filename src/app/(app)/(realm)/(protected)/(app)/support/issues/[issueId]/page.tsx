"use client";
import React, { useState } from "react";
import { useGetProjectIssue } from "@/hooks/api/useSupport";
import Button from "@/components/ui/buttons/Button";
import NewReply from "@/components/navbar/menu/NewReply";

const statusStyle: {
  [key: string]: string;
} = {
  New: "bg-green-500/30",
  "In Progress": "bg-yellow-500/30",
  "Pre Discovery": "bg-sky-500/30",
  Discovery: "bg-blue-500/30",
  QA: "bg-purple-500/30",
  UAT: "bg-violet-500/30",
  Feedback: "bg-pink-500/30",
  Canceled: "bg-neutral-500/30",
  "On Hold": "bg-orange-500/30",
  "Ready for Release": "bg-green-500/30",
  Completed: "bg-green-500/30",
};

export default function IssuePage({
  params: { issueId },
}: {
  params: { issueId: number };
}) {
  const { data, isLoading, isError, error } = useGetProjectIssue(issueId);
  const [newCommentModal, setNewCommentModal] = useState<boolean>(false);

  if (isLoading) return <div className="p-6">Loading...</div>;
  if (isError) return <div className="p-6">Error Loading Open Tickets</div>;
  if (!data) return <div className="p-6">No Data</div>;

  const t = data as any;

  const Label = ({ children }: { children: string }) => (
    <div className="w-[100px] font-medium text-slate-500">{children}</div>
  );

  return (
    <>
      <div className="overflow-auto p-6">
        <Button
          className="mb-4"
          size="sm"
          onClick={() => window.history.back()}
        >
          Back
        </Button>
        <h1 className="mt-4 text-2xl font-semibold">{t.subject}</h1>
        <h2 className="text-lg text-neutral-700">
          {t.tracker.name} #: {t.id}
        </h2>

        <div className="mt-10 flex flex-col gap-3">
          <div className="flex items-center gap-2">
            <Label>Status</Label>
            <div
              className={`rounded px-2 font-medium text-neutral-900 ${
                statusStyle[t.status.name]
              }`}
            >
              {t.status.name}
            </div>
          </div>

          {/* Priority */}
          <div className="flex items-center gap-2">
            <Label>Priority</Label>

            <div className="">{t.priority.name}</div>
          </div>
        </div>

        <div className="mt-14">
          <h3 className="text-lg font-semibold">Attachments</h3>
          <div>
            {t.attachments && t.attachments.length > 0 ? (
              t.attachments?.map((a: any) => {
                return (
                  <div className="mt-2" key={a.id}>
                    <a
                      href={a.content_url}
                      className="text-blue-500 hover:underline"
                    >
                      {a.filename}
                    </a>
                  </div>
                );
              })
            ) : (
              <div className="">No Attachments</div>
            )}
          </div>
        </div>

        <div className="mt-14">
          <h3 className="text-lg font-semibold">Description</h3>
          <div
            className="mt-2"
            dangerouslySetInnerHTML={{ __html: t.description }}
          />
        </div>

        {/* Removed comments for now per Nick's / Adam's request -- Sean B */}
        {/* <div className="mt-14">
          <h3 className="text-xl font-semibold mb-4 flex items-center justify-between">
            Comments
            <Button
              size="sm"
              onClick={() => {
                setNewCommentModal(true);
              }}
            >
              New Comment
            </Button>
          </h3>
          <div className="flex flex-col gap-6">
            {t.journals &&
              [...t.journals].reverse().map((j: any) => {
                const createdBy = j.user.name;
                const createdDate = new Date(j.created_on);
                const hasDescription = j?.notes?.length > 0;
                if (hasDescription) {
                  return (
                    <div className="p-2 shadow rounded" key={j.id}>
                      <p className="font-medium">{createdBy}</p>
                      <small className="text-neutral-700">
                        {createdDate.toLocaleString()}
                      </small>
                      <div dangerouslySetInnerHTML={{ __html: j.notes }} />
                    </div>
                  );
                } else {
                  return null;
                }
              })}

            {t?.journals > 0 && (
              <div>
                <Button
                  onClick={() => {
                    setNewCommentModal(true);
                  }}
                >
                  New Comment
                </Button>
              </div>
            )}
            <NewReply
              newCommentModal={newCommentModal}
              setNewCommentModal={setNewCommentModal}
              issueId={issueId}
            />
          </div>
        </div> */}
      </div>
    </>
  );
}
