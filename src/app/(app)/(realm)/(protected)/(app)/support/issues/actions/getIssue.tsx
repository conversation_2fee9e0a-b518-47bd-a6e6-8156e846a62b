"use server";
import { Issue } from "../../types/supportTypes";

var Redmine = require('node-redmine');
 

export async function getIssue(issueId: number, apiKey:string | null): Promise<Issue[]> {

  console.log(Number(issueId))

  var hostname = process.env.REDMINE_HOST || '';
  var config = {
    apiKey: apiKey || process.env.REDMINE_API_KEY
  };

  const redmine = new Redmine(hostname, config);

  
  const issue = await new Promise<any[]>((resolve, reject) => {
    redmine.get_issue_by_id(
      Number(issueId),
      {
        limit: 100,
        include: 'journals, attachments',
      },
      function (err: any, data: any) {
        if (err) reject(err);
        else resolve(data.issue);
      }
    );
  });

  console.log(issue)

  return issue;
}