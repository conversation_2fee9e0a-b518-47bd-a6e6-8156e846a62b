"use server";
import { Issue } from "../../types/supportTypes";

var Redmine = require('node-redmine');
 

export async function getIssues(projectId: number, apiKey:string | null): Promise<Issue[]> {

  var hostname = process.env.REDMINE_HOST || '';
  var config = {
    apiKey: apiKey || process.env.REDMINE_API_KEY
  };


  const redmine = new Redmine(hostname, config);


  const issues = await new Promise<any[]>((resolve, reject) => {
    redmine.issues(
      {
        limit: 100,
        project_id: projectId,
        tracker_id: 3
      },
      function (err: any, data: any) {
        if (err) reject(err);
        else resolve(data.issues);
      }
    );
  });


  return issues;
}