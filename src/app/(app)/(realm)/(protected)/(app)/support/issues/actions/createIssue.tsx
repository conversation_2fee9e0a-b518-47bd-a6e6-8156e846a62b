"use server";

var Redmine = require('node-redmine');
 

export async function createIssue(projectId: number, data:any, apiKey:string | null) {


  var hostname = process.env.REDMINE_HOST || '';
  var config = {
    apiKey: apiKey || process.env.REDMINE_API_KEY
  };

  const redmine = new Redmine(hostname, config);
  
  const issue = await new Promise<any[]>((resolve, reject) => {
    redmine.create_issue(
      {
        issue: {
          project_id: projectId,
          subject: data.subject,
          description: data.description,
          priority_id: data.priority_id,
          // tracker_id: body.tracker_id,
          status_id: 1,
          // assigned_to_id: body.assigned_to_id,
          // custom_fields: body.custom_fields,
          custom_fields: [
            { id: 2, name: 'Issue Work Type', value: 'Development' },
          ],
          // tracker_id: data.ticketType,
          // Making this only create support tickets for now -- Sean B
          tracker_id: 3
        },
      },
      function (err: any, data: any) {
        if (err) reject(err);
        else resolve(data.issue);
      }
    );
  });


  return issue;
}
