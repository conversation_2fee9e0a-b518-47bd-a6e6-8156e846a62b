export type IssueDetail = {
  id: number;
  name: string;
}

export type Project = IssueDetail;

export type Tracker = IssueDetail;

export type Status = IssueDetail;

export type Priority = IssueDetail;

export type User = IssueDetail;

export type Issue = {
  id: number;
  project: Project;
  tracker: Tracker;
  status: Status;
  priority: Priority;
  author: User;
  assigned_to: User;
  subject: string;
  description: string;
  start_date: string | null;
  due_date: string | null;
  done_ratio: number;
  is_private: boolean;
  estimated_hours: number | null;
  custom_fields: CustomField[];
  created_on: string;
  updated_on: string;
  closed_on: string | null;
  journals?: Journal[];
  attachments: Attachment[];
};

export type CustomField = {
  id: number;
  name: string;
  value: string;
}

export type Attachment = {
  id: number;
  filename: string;
  filesize: number;
  content_type: string;
  description: string;
  content_url: string;
  thumbnail_url: string;
  author: User;
  created_on: string;
};

export type Journal = {
  id: number;
  user: User;  
  notes: string;
  created_on: string;
  private_notes: boolean;
  details: [];
};