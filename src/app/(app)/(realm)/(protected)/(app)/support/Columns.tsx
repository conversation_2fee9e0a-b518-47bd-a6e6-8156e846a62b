"use client"
import { ColumnDef } from "@tanstack/react-table"
import { RxCaretDown, RxCaretUp } from 'react-icons/rx'
import { BsPinAngle } from "react-icons/bs"
import { FiPlus } from "react-icons/fi"
import { Issue } from "./types/supportTypes"


const Sort = ({
  column,
  name
}:{
  column: any
  name: string
}) => {
  const isSorted = column.getIsSorted()
  return (
    <button
      className="flex items-center gap-0.5"
      onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
    >
      {name}
      {/* {column.getIsSorted() === "asc" 
        ? <RxCaretDown className="ml-2 h-4 w-4" /> 
        : <RxCaretUp className="ml-2 h-4 w-4" />
      } */}
      { isSorted 
        ? 
          isSorted === "asc" 
            ? <RxCaretDown className="ml-1 h-4 w-4" /> 
            : <RxCaretUp className="ml-1 h-4 w-4" />
        : null
      }
      
    </button>
  )
}

const Assignees = ({ value }:any) => {
  console.log(value)
  if(!value) return null

  if(value.length > 0){
    return(
      <div className="flex items-center">
        {value.map((assignee:any, index:number) => (
          <div key={assignee}
            style={{
              zIndex: value.length - index,
              marginLeft: -8,
            }}
          >
            {/* <AvatarFallback fullName={assignee} /> */}
          </div>
        ))}
        <div 
          className="flex items-center justify-center  w-7 h-7 rounded-full dark:border-neutral-200 border-neutral-500 border dark:text-neutral-200 text-xs font-medium"
          style={{
            marginLeft: -8,
            zIndex: 0,
          }}
        >
          <FiPlus />
        </div>
      </div>
    )
  }

  // return <AvatarFallback fullName={value}/>
}

export const columnHeaderNames:{
  [key:string]:string
} = {
  id: "Ticket",
}

export const columns: ColumnDef<Issue>[] = [
  {
    accessorKey: "id",
    header: ({ column }) => {
      return <Sort column={column} name="Ticket" />
    },
  },
  {
    accessorKey: "tracker.name",
    header: ({ column }) => {
      return <Sort column={column} name="Type" />
    },
  },
  {
    accessorKey: "subject",
    header: ({ column }) => {
      return <div className="w-[300px]">
        <Sort column={column} name="Subject" />
        </div>
    },
  },
  {
    accessorKey: "status.name",
    header: ({ column }) => {
      return <Sort column={column} name="Status" />
    },
  },
  {
    accessorKey: "priority.name",
    header: ({ column }) => {
      return <Sort column={column} name="Priority" />
    },
  },
  {
    accessorKey: "custom_fields",
    header: "Work Type",
    cell: ({ row }: any) => {
      const workTypeField = row.getValue('custom_fields').find((field: any) => field.name === "Issue Work Type");
      return workTypeField ? workTypeField.value : null;
    }
  },
  // {
  //   accessorKey: "assigned_to.name",
  //   header: "Assignee",
  //   cell: ({ row }: any) => {
  //     const value = row.getValue('assigned_to.name')
  //     // return <Assignees value={value} />
  //     return <div>{value}</div>
  //   }
  // },
]
