"use client";

import { columnHeaderNames } from "./Columns";

import {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
  flexRender,
  getCoreRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
  getFilteredRowModel,
  VisibilityState,
} from "@tanstack/react-table";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

import { useState } from "react";
import Button from "@/components/ui/buttons/Button";

import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

import { FiGrid, FiMenu, FiPlusCircle, FiTrash } from "react-icons/fi";
import {
  AiOutlineSortAscending,
  AiOutlineSortDescending,
} from "react-icons/ai";
import { BsPinAngle } from "react-icons/bs";
import { usePara<PERSON>, useRouter } from "next/navigation";

interface DataTableProps<TData extends { id?: any }, TValue> {
  columns: ColumnDef<TData, TValue>[];
  data: TData[];
}

const buttonStyle = `
  flex gap-2 text-neutral-500 items-center border border-neutral-400 rounded px-2
  hover:dark:text-neutral-200 hover:dark:border-neutral-200
`;

const DeleteButton = ({ enabled }: { enabled: boolean }) => {
  return (
    <button
      className={`
        ${buttonStyle}
        ${enabled ? "" : "opacity-50 cursor-not-allowed"}
      `}
      disabled={!enabled}
    >
      <FiTrash /> Delete
    </button>
  );
};

const PinButton = ({ enabled }: { enabled: boolean }) => {
  return (
    <button
      className={`
        ${buttonStyle}
        ${enabled ? "" : "opacity-50 cursor-not-allowed"}
      `}
      disabled={!enabled}
    >
      <BsPinAngle /> Pin
    </button>
  );
};

const NewTicketButton = () => {
  return (
    <button
      className="
        flex gap-2 dark:text-neutral-300 items-center border dark:border-neutral-500 rounded px-2 text-neutral-700 border-neutral-700
        hover:dark:text-neutral-200 hover:dark:border-neutral-200 hover:bg-yellow-400/60
      "
    >
      <FiPlusCircle /> New Ticket
    </button>
  );
};

const View = () => {
  const [sort, setSort] = useState("grid");
  const active = "dark:text-amber-300 text-amber-900 font-semibold";
  return (
    <div className="p-1 border border-neutral-500 rounded flex gap-2 items-center justify-center text-lg">
      <button
        onClick={() => setSort("grid")}
        className={`${
          sort === "grid"
            ? active
            : "hover:dark:text-neutral-100 hover:text-yellow-700"
        }`}
      >
        <FiGrid />
      </button>
      <button
        onClick={() => setSort("list")}
        className={`${
          sort === "list"
            ? active
            : "hover:dark:text-neutral-100 hover:text-yellow-700"
        }`}
      >
        <FiMenu />
      </button>
    </div>
  );
};

export function DataTable<TData extends { id?: any; }, TValue>({
  columns,
  data,
}: DataTableProps<TData, TValue>) {
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
  const [rowSelection, setRowSelection] = useState({});

  const router = useRouter();
  const { realm } = useParams()

  const table = useReactTable({
    data,
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
    },
  });

  return (
    <div className="">
      <div className="flex justify-between w-full mb-4">
        {/* <div className="flex gap-4 w-full justify-start"> 
          <View />
          <NewTicketButton />
          <PinButton enabled={true} />
          <DeleteButton enabled={true} />
        </div> */}
        <div className="flex gap-4 w-full justify-end">
          <input
            placeholder="Filter titles..."
            value={
              (table.getColumn("subject")?.getFilterValue() as string) ?? ""
            }
            onChange={(event) =>
              table.getColumn("subject")?.setFilterValue(event.target.value)
            }
            className="dark:bg-neutral-800 bg-neutral-50 w-full max-w-xs border border-gray-500 rounded-md px-2 py-1 text-sm focus:outline-none"
          />

          {/* <div className="flex items-center gap-2 shrink-0">
          Sort: {columnHeaderNames[sorting[0]?.id] || "None"}{" "}
          {sorting[0]?.desc ? (
            <AiOutlineSortDescending />
          ) : (
            <AiOutlineSortAscending />
          )}
        </div> */}

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <div className="flex items-center gap-2 cursor-pointer shrink-0">
                Filters: None{" "}
                <FiPlusCircle className="dark:text-yellow-300 text-neutral-500 font-bold" />
              </div>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {table
                .getAllColumns()
                .filter((column) => column.getCanHide())
                .map((column) => {
                  return (
                    <DropdownMenuCheckboxItem
                      key={column.id}
                      className="capitalize"
                      checked={column.getIsVisible()}
                      onCheckedChange={(value) =>
                        column.toggleVisibility(!!value)
                      }
                    >
                      {column.id}
                    </DropdownMenuCheckboxItem>
                  );
                })}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      <Table>
        <TableHeader>
          {table.getHeaderGroups().map((headerGroup) => (
            <TableRow key={headerGroup.id}>
              {headerGroup.headers.map((header) => {
                return (
                  <TableHead key={header.id}>
                    {header.isPlaceholder
                      ? null
                      : flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                  </TableHead>
                );
              })}
            </TableRow>
          ))}
        </TableHeader>
        <TableBody>
          {table.getRowModel().rows?.length ? (
            table.getRowModel().rows.map((row) => (
              <TableRow
                key={row.id}
                data-state={row.getIsSelected() && "selected"}
                onClick={() => router.push(`/support/issues/${row.original.id}`)}
                
              >
                {row.getVisibleCells().map((cell) => (
                  <TableCell key={cell.id}>
                    {flexRender(cell.column.columnDef.cell, cell.getContext())}
                  </TableCell>
                ))}
              </TableRow>
            ))
          ) : (
            <TableRow>
              <TableCell colSpan={columns.length} className="h-24 text-center">
                No results.
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
      <div className="flex items-center justify-start space-x-2 py-4">
        <div className="flex items-center space-x-2">
          <p className="text-sm font-medium">Rows per page</p>
          <Select
            value={`${table.getState().pagination.pageSize}`}
            onValueChange={(value) => {
              table.setPageSize(Number(value));
            }}
          >
            <SelectTrigger className="h-8 w-[70px]">
              <SelectValue placeholder={table.getState().pagination.pageSize} />
            </SelectTrigger>
            <SelectContent side="top">
              {[10, 20, 30, 40, 50].map((pageSize) => (
                <SelectItem key={pageSize} value={`${pageSize}`}>
                  {pageSize}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <div className="flex gap-2  w-[100px] items-center justify-center text-sm font-medium">
          Page {table.getState().pagination.pageIndex + 1} of{" "}
          {table.getPageCount()}
        </div>
        <div className="flex gap-4">
          <button
            className={buttonStyle + " px-3 py-1"}
            onClick={() => table.previousPage()}
            disabled={!table.getCanPreviousPage()}
          >
            Previous
          </button>
          <button
            className={buttonStyle + " px-3 py-1"}
            onClick={() => table.nextPage()}
            disabled={!table.getCanNextPage()}
          >
            Next
          </button>
        </div>
      </div>
    </div>
  );
}
