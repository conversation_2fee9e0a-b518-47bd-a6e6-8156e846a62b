// "use client";
// import { useState } from "react";
// import { useFormContext, useField<PERSON>rray, Controller } from "react-hook-form";
// import { Button } from "@/components/ui/button";
// import { Input } from "@/components/ui/input";
// import {
//   Select,
//   SelectContent,
//   SelectItem,
//   SelectTrigger,
//   SelectValue,
// } from "@/components/ui/select";
// import {
//   Card,
//   CardContent,
//   CardHeader,
//   CardTitle,
//   CardDescription,
//   CardFooter,
// } from "@/components/ui/card";
// import { Label } from "@/components/ui/label";
// import { Checkbox } from "@/components/ui/checkbox";
// import { PlusCircle, Trash2, GripVertical } from "lucide-react";
// import { Badge } from "@/components/ui/badge";
// import { Template, FormDataParameter } from "@/hooks/api/useDocument";
// import {
//   Accordion,
//   AccordionContent,
//   AccordionItem,
//   AccordionTrigger,
// } from "@/components/ui/accordion";
// import TextareaAutosize from "react-textarea-autosize";

// export function FormDataSection() {
//   const { control, watch, setValue, register } = useFormContext<Template>();
//   const isReport = watch("isReport");

//   // Set up field array for form data parameters
//   const { fields, append, remove, move } = useFieldArray({
//     control,
//     name: "formData.parameters",
//   });

//   const exportTypes = watch("formData.exportTypes") || [];

//   // Export types options
//   const availableExportTypes = ["pdf", "xlsx", "docx"];

//   // Handle adding a parameter
//   const addParameter = () => {
//     append({
//       type: "text",
//       label: "",
//       params: [],
//       required: false,
//     } as FormDataParameter);
//   };

//   // Handle toggling export type
//   const toggleExportType = (type: string) => {
//     const currentTypes = [...exportTypes];
//     const index = currentTypes.indexOf(type);

//     if (index === -1) {
//       // Add the type
//       setValue("formData.exportTypes", [...currentTypes, type], {
//         shouldDirty: true,
//       });
//     } else {
//       // Remove the type
//       currentTypes.splice(index, 1);
//       setValue("formData.exportTypes", currentTypes, { shouldDirty: true });
//     }
//   };

//   if (!isReport) return null;

//   return (
//     <div className="rounded-lg bg-white shadow">
//       <div className="border-b bg-gray-50 px-4 py-4">
//         <h2 className="text-lg font-medium">Report Configuration</h2>
//         <p className="text-sm text-gray-500">
//           Configure parameters and export options for this report
//         </p>
//       </div>

//       <div className="space-y-6 px-4 py-5">
//         {/* Report Type and Report Type ID */}
//         <div className="grid grid-cols-1 gap-4 sm:grid-cols-6">
//           <div className="sm:col-span-3">
//             <Label htmlFor="formData.type">Icon Type</Label>
//             <Controller
//               name="formData.type"
//               control={control}
//               defaultValue=""
//               render={({ field }) => (
//                 <Select
//                   onValueChange={field.onChange}
//                   defaultValue={field.value}
//                 >
//                   <SelectTrigger className="mt-1 w-full">
//                     <SelectValue placeholder="Select report type" />
//                   </SelectTrigger>
//                   <SelectContent>
//                     <SelectItem value="date">Date</SelectItem>
//                     <SelectItem value="dateRange">Date Range</SelectItem>
//                     <SelectItem value="custom">Custom</SelectItem>
//                   </SelectContent>
//                 </Select>
//               )}
//             />
//           </div>

//           {/* <div className="sm:col-span-3">
//             <Label htmlFor="formData.reportTypeId">Report Type ID</Label>
//             <Input
//               id="formData.reportTypeId"
//               {...register("formData.reportTypeId")}
//               className="mt-1 block w-full"
//               placeholder="Enter the report type identifier"
//             />
//           </div> */}
//         </div>

//         {/* Export Types */}
//         <div>
//           <Label className="mb-2 block">Export Types</Label>
//           <div className="flex flex-wrap gap-2">
//             {availableExportTypes.map((type) => (
//               <Badge
//                 key={type}
//                 className="cursor-pointer text-sm"
//                 variant={exportTypes.includes(type) ? "default" : "outline"}
//                 onClick={() => toggleExportType(type)}
//               >
//                 {type.toUpperCase()}
//               </Badge>
//             ))}
//           </div>
//         </div>

//         {/* Parameters Section */}
//         <div className="mt-8">
//           <div className="mb-4 flex items-center justify-between">
//             <Label className="text-base font-medium">Parameters</Label>
//             <Button
//               type="button"
//               variant="outline"
//               size="sm"
//               onClick={addParameter}
//               className="gap-1"
//             >
//               <PlusCircle className="h-4 w-4" />
//               Add Parameter
//             </Button>
//           </div>

//           {fields.length === 0 ? (
//             <div className="my-4 rounded-md border border-dashed border-gray-300 p-6 text-center">
//               <p className="text-sm text-gray-500">
//                 No parameters added yet. Add parameters to allow users to
//                 customize the report.
//               </p>
//             </div>
//           ) : (
//             <Accordion type="multiple" className="space-y-4">
//               {fields.map((field, index) => (
//                 <AccordionItem
//                   key={field.id}
//                   value={`param-${index}`}
//                   className="rounded-md border border-gray-200"
//                 >
//                   <AccordionTrigger className="px-4 hover:no-underline">
//                     <div className="flex w-full items-center justify-between">
//                       <div className="flex items-center gap-3">
//                         <GripVertical className="h-4 w-4 cursor-move text-gray-400" />
//                         <span>
//                           {watch(`formData.parameters.${index}.label`) ||
//                             `Parameter ${index + 1}`}
//                         </span>
//                       </div>
//                       <Badge className="mr-4">
//                         {watch(`formData.parameters.${index}.type`)}
//                       </Badge>
//                     </div>
//                   </AccordionTrigger>
//                   <AccordionContent className="px-4 pb-4">
//                     <div className="grid grid-cols-1 gap-4 sm:grid-cols-12">
//                       {/* Parameter Type */}
//                       <div className="sm:col-span-4">
//                         <Label htmlFor={`formData.parameters.${index}.type`}>
//                           Type
//                         </Label>
//                         <Controller
//                           name={`formData.parameters.${index}.type`}
//                           control={control}
//                           defaultValue={field.type}
//                           render={({ field: typeField }) => (
//                             <Select
//                               onValueChange={typeField.onChange}
//                               value={typeField.value}
//                             >
//                               <SelectTrigger className="mt-1 w-full">
//                                 <SelectValue placeholder="Select type" />
//                               </SelectTrigger>
//                               <SelectContent>
//                                 <SelectItem value="text">Text</SelectItem>
//                                 <SelectItem value="date">Date</SelectItem>
//                                 <SelectItem value="dateRange">
//                                   Date Range
//                                 </SelectItem>
//                               </SelectContent>
//                             </Select>
//                           )}
//                         />
//                       </div>

//                       {/* Parameter Label */}
//                       <div className="sm:col-span-8">
//                         <Label htmlFor={`formData.parameters.${index}.label`}>
//                           Label
//                         </Label>
//                         <Input
//                           id={`formData.parameters.${index}.label`}
//                           {...register(`formData.parameters.${index}.label`)}
//                           className="mt-1"
//                         />
//                       </div>

//                       {/* Required Checkbox */}
//                       <div className="sm:col-span-4">
//                         <div className="flex items-center space-x-2">
//                           <Controller
//                             name={`formData.parameters.${index}.required`}
//                             control={control}
//                             defaultValue={field.required}
//                             render={({ field: requiredField }) => (
//                               <Checkbox
//                                 id={`formData.parameters.${index}.required`}
//                                 checked={requiredField.value}
//                                 onCheckedChange={(checked) => {
//                                   requiredField.onChange(checked);
//                                 }}
//                               />
//                             )}
//                           />
//                           <Label
//                             htmlFor={`formData.parameters.${index}.required`}
//                             className="text-sm font-medium"
//                           >
//                             Required
//                           </Label>
//                         </div>
//                       </div>

//                       {/* Field Name */}
//                       <div className="sm:col-span-8">
//                         <Label
//                           htmlFor={`formData.parameters.${index}.fieldName`}
//                         >
//                           Field Name
//                         </Label>
//                         <Input
//                           id={`formData.parameters.${index}.fieldName`}
//                           {...register(
//                             `formData.parameters.${index}.fieldName`,
//                           )}
//                           className="mt-1"
//                           placeholder="Parameter field name in the report"
//                         />
//                       </div>

//                       {/* Predefined Params for Date Range */}
//                       {watch(`formData.parameters.${index}.type`) ===
//                         "dateRange" && (
//                         <div className="sm:col-span-12">
//                           <Label>Predefined Date Ranges</Label>
//                           <div className="mt-2 grid grid-cols-2 gap-2 sm:grid-cols-3">
//                             {[
//                               "today",
//                               "yesterday",
//                               "lastMonth",
//                               "currentMonth",
//                               "nextMonth",
//                             ].map((param) => (
//                               <div
//                                 key={param}
//                                 className="flex items-center space-x-2"
//                               >
//                                 <Controller
//                                   name={`formData.parameters.${index}.params`}
//                                   control={control}
//                                   defaultValue={field.params || []}
//                                   render={({ field: paramsField }) => {
//                                     const isSelected =
//                                       paramsField.value?.includes(param);
//                                     return (
//                                       <Checkbox
//                                         id={`${param}-${index}`}
//                                         checked={isSelected}
//                                         onCheckedChange={(checked) => {
//                                           const currentParams = [
//                                             ...(paramsField.value || []),
//                                           ];
//                                           if (checked) {
//                                             if (
//                                               !currentParams.includes(param)
//                                             ) {
//                                               currentParams.push(param);
//                                             }
//                                           } else {
//                                             const paramIndex =
//                                               currentParams.indexOf(param);
//                                             if (paramIndex !== -1) {
//                                               currentParams.splice(
//                                                 paramIndex,
//                                                 1,
//                                               );
//                                             }
//                                           }
//                                           paramsField.onChange(currentParams);
//                                         }}
//                                       />
//                                     );
//                                   }}
//                                 />
//                                 <Label
//                                   htmlFor={`${param}-${index}`}
//                                   className="text-sm capitalize"
//                                 >
//                                   {param.replace(/([A-Z])/g, " $1").trim()}
//                                 </Label>
//                               </div>
//                             ))}
//                           </div>
//                         </div>
//                       )}

//                       {/* Default Parameter */}
//                       {watch(`formData.parameters.${index}.type`) ===
//                         "dateRange" &&
//                         (watch(`formData.parameters.${index}.params`) ?? [])
//                           .length > 0 && (
//                           <div className="sm:col-span-6">
//                             <Label
//                               htmlFor={`formData.parameters.${index}.defaultParam`}
//                             >
//                               Default Value
//                             </Label>
//                             <Controller
//                               name={`formData.parameters.${index}.defaultParam`}
//                               control={control}
//                               defaultValue={field.defaultParam || ""}
//                               render={({ field: defaultField }) => (
//                                 <Select
//                                   onValueChange={defaultField.onChange}
//                                   value={defaultField.value}
//                                 >
//                                   <SelectTrigger className="mt-1 w-full">
//                                     <SelectValue placeholder="Select default" />
//                                   </SelectTrigger>
//                                   <SelectContent>
//                                     {watch(
//                                       `formData.parameters.${index}.params`,
//                                     )?.map((param: string) => (
//                                       <SelectItem key={param} value={param}>
//                                         {param
//                                           .replace(/([A-Z])/g, " $1")
//                                           .trim()}
//                                       </SelectItem>
//                                     ))}
//                                   </SelectContent>
//                                 </Select>
//                               )}
//                             />
//                           </div>
//                         )}

//                       {/* Validation (for text type) */}
//                       {watch(`formData.parameters.${index}.type`) ===
//                         "text" && (
//                         <div className="sm:col-span-12">
//                           <Label>Validation (Optional)</Label>
//                           <div className="mt-2 grid grid-cols-1 gap-4 sm:grid-cols-2">
//                             <div>
//                               <Label
//                                 htmlFor={`formData.parameters.${index}.validation.regex`}
//                                 className="text-xs"
//                               >
//                                 Regex Pattern
//                               </Label>
//                               <Input
//                                 id={`formData.parameters.${index}.validation.regex`}
//                                 {...register(
//                                   `formData.parameters.${index}.validation.regex`,
//                                 )}
//                                 placeholder="e.g. ^[A-Za-z0-9]+$"
//                                 className="mt-1"
//                               />
//                             </div>
//                             <div>
//                               <Label
//                                 htmlFor={`formData.parameters.${index}.validation.message`}
//                                 className="text-xs"
//                               >
//                                 Error Message
//                               </Label>
//                               <Input
//                                 id={`formData.parameters.${index}.validation.message`}
//                                 {...register(
//                                   `formData.parameters.${index}.validation.message`,
//                                 )}
//                                 placeholder="e.g. Only alphanumeric characters allowed"
//                                 className="mt-1"
//                               />
//                             </div>
//                           </div>
//                         </div>
//                       )}

//                       {/* Remove Button */}
//                       <div className="flex justify-end sm:col-span-12">
//                         <Button
//                           type="button"
//                           variant="destructive"
//                           size="sm"
//                           onClick={() => remove(index)}
//                           className="gap-1"
//                         >
//                           <Trash2 className="h-4 w-4" />
//                           Remove Parameter
//                         </Button>
//                       </div>
//                     </div>
//                   </AccordionContent>
//                 </AccordionItem>
//               ))}
//             </Accordion>
//           )}
//         </div>
//       </div>
//     </div>
//   );
// }
