// ImprovedDocumentFileSection.tsx
"use client";
import { useState, useEffect, useCallback } from "react";
import { useFormContext } from "react-hook-form";
import { Template } from "@/hooks/api/useDocument";
import { Button } from "@/components/ui/button";
import { useDownloadDocumentFile } from "@/hooks/api/useDocument";
import { Eye } from "lucide-react";
import { useDropzone } from "react-dropzone";

interface DocumentFileSectionProps {
  documentUUID?: string;
}

export const DocumentFileSection = ({ documentUUID }: DocumentFileSectionProps) => {
  const { register, watch, setValue, formState } = useFormContext<Template>();
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const watchFiletype = watch("fileType");
  const watchFile = watch("file");
  
  // Download document functionality
  const { data: fileBlob, isLoading: isDownloading } = useDownloadDocumentFile(documentUUID || "");
  
  useEffect(() => {
    // If we have a file from the API and no preview URL yet, create one
    if (fileBlob && !previewUrl && !watchFile) {
      const url = URL.createObjectURL(fileBlob);
      setPreviewUrl(url);
    }
    
    // Cleanup function to revoke object URLs when component unmounts
    return () => {
      if (previewUrl) {
        URL.revokeObjectURL(previewUrl);
      }
    };
  }, [fileBlob, previewUrl, watchFile]);

  const processFile = useCallback((file: File) => {
    // Create URL for preview
    const url = URL.createObjectURL(file);
    setPreviewUrl(url);
    
    // Update file in the form
    setValue("file", file, { shouldDirty: true });
    
    // When a new file is uploaded, remove the documentUUID
    if (documentUUID) {
      setValue("documentUUID", "", { shouldDirty: true });
    }
  }, [documentUUID, setValue]);

  const onDrop = useCallback((acceptedFiles: File[]) => {
    if (acceptedFiles.length > 0) {
      processFile(acceptedFiles[0]);
    }
  }, [processFile]);

  // Set up react-dropzone
  const { 
    getRootProps, 
    getInputProps, 
    isDragActive, 
    open 
  } = useDropzone({
    onDrop,
    maxFiles: 1,
    multiple: false,
    accept: watchFiletype ? { [watchFiletype]: [] } : undefined,
    // Allow users to accept any file when no filetype is selected
    noClick: watchFile !== null,
    // Disable click opening when a file is already selected
  });

  const handleRemoveFile = () => {
    // Clear the preview
    if (previewUrl) {
      URL.revokeObjectURL(previewUrl);
      setPreviewUrl(null);
    }
    
    // Clear the file in the form
    setValue("file", null, { shouldDirty: true });
    
    // Also clear the documentUUID if there was one
    if (documentUUID) {
      setValue("documentUUID", undefined, { shouldDirty: true });
    }
  };
  
  const handleViewFile = () => {
    if (previewUrl) {
      window.open(previewUrl, '_blank');
    }
  };

  return (
    <div className="rounded-lg bg-white shadow">
      <div className="border-b bg-gray-50 px-4 py-4">
        <h2 className="text-lg font-medium">Document File</h2>
      </div>
      <div className="px-4 py-5">
        <div 
          {...getRootProps()} 
          className={`flex justify-center rounded-md border-2 ${isDragActive ? 'border-blue-500 bg-blue-50' : 'border-dashed border-gray-300'} px-6 pb-6 pt-5 transition-colors duration-200`}
        >
          <input {...getInputProps()} />
          <div className="space-y-1 text-center">
            {previewUrl || watchFile || fileBlob ? (
              <div className="flex flex-col items-center">
                <p className="mt-2 text-sm text-gray-600">
                  {watchFile?.name || "Document file loaded"}
                  {!watchFile && documentUUID && " (from server)"}
                </p>
                <div className="mt-2 flex gap-2">
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={handleViewFile}
                    disabled={!previewUrl && !fileBlob}
                  >
                    <Eye className="mr-2 h-4 w-4" />
                    View
                  </Button>
                  <Button
                    type="button"
                    variant="destructive"
                    size="sm"
                    onClick={handleRemoveFile}
                  >
                    Remove file
                  </Button>
                </div>
                
                <div className="mt-4">
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={open}
                    className="text-sm font-medium text-blue-600 hover:text-blue-500"
                  >
                    Replace with different file
                  </Button>
                </div>
              </div>
            ) : (
              <>
                <svg
                  className="mx-auto h-12 w-12 text-gray-400"
                  stroke="currentColor"
                  fill="none"
                  viewBox="0 0 48 48"
                >
                  <path
                    d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
                    strokeWidth={2}
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
                <div className="flex text-sm text-gray-600 justify-center">
                  <p>
                    {isDragActive 
                      ? "Drop the file here..." 
                      : "Drag 'n' drop a file here, or click to select"}
                  </p>
                </div>
                <p className="mt-1 text-xs text-gray-500">
                  {watchFiletype
                    ? `${watchFiletype.toUpperCase()} up to 10MB`
                    : "Select a file type first"}
                </p>
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};