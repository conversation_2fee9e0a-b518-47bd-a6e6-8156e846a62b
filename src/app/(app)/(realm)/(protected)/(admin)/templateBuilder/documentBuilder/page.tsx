"use client";
import Loading from "@/app/(app)/loading";
import { useGetAllDocumentTemplates } from "@/hooks/api/useDocument";
import React from "react";
import DocumentTemplatesGrid from "./DocumentTemplatesGrid";

const DocumentBuilderPage = () => {
  const { data, isError, isLoading } = useGetAllDocumentTemplates();

  if (isLoading) return <Loading />;
  if (isError)
    return (
      <div className="flex h-screen items-center justify-center">
        <div className="text-center">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="mx-auto h-12 w-12 text-red-500"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
            />
          </svg>
          <h3 className="mt-2 text-lg font-medium text-gray-900">
            Error loading document templates
          </h3>
          <p className="mt-1 text-sm text-gray-500">
            Please try refreshing the page or contact support if the issue
            persists.
          </p>
          <button
            className="mt-4 rounded-lg bg-blue-500 px-4 py-2 text-white transition hover:bg-blue-600"
            onClick={() => window.location.reload()}
          >
            Refresh Page
          </button>
        </div>
      </div>
    );

  if (data) {
    return <DocumentTemplatesGrid data={data} />;
  }

  return null;
};

export default DocumentBuilderPage;
