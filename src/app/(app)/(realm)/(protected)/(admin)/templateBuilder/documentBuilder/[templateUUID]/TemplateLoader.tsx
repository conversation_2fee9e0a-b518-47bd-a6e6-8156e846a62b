import { notFound } from "next/navigation";
import { getToken } from "@/utils/keycloakUtils";
import { createServerRequests } from "@/utils/serverAgent";
import { Template } from "@/hooks/api/useDocument";
import DocumentEditor from "../DocumentEditor";

interface LoaderProps {
  templateUUID: string;
}

export default async function TemplateLoader({ templateUUID }: LoaderProps) {
  // 1. getToken() now works on server (reads your HTTP-only cookie) and client
  const token = getToken();
  if (!token) {
    return notFound();
  }

  // 2. build your server-only agent
  const requests = createServerRequests(token);

  // 3. fetch
  let template: Template;
  try {
    template = await requests.get<Template>(
      `/coordinator/templates/${templateUUID}`
    );
  } catch (err: any) {
    if (err?.response?.status === 404) {
      return notFound();
    }
    throw err;
  }

  // 4. hand off to your existing client editor
  return <DocumentEditor document={template} />;
}