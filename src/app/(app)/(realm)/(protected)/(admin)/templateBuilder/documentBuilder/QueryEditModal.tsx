// QueryEditModal.tsx
import { useEffect, useState } from "react";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  Di<PERSON>Title,
  DialogFooter,
} from "@/components/ui/dialog";
import { Plus, Trash2 } from "lucide-react";
import { Input } from "@/components/ui/input";
import MonacoEditor from "@monaco-editor/react";
import { Query } from "@/hooks/api/useDocument";

interface QueryEditModalProps {
  isOpen: boolean;
  onClose: () => void;
  query: Query | null;
  onSave: (query: Query) => void;
  queryIndex?: number;
}

export const QueryEditModal = ({
  isOpen,
  onClose,
  query,
  onSave,
  queryIndex,
}: QueryEditModalProps) => {
  const [editedQuery, setEditedQuery] = useState<Query>({
    queryKey: "",
    referenceKey: "",
    type: "SQL",
    parameters: [],
    sql: "",
  });

  useEffect(() => {
    if (query) {
      setEditedQuery(query);
    } else {
      setEditedQuery({
        queryKey: "",
        referenceKey: "",
        type: "SQL",
        parameters: [],
        sql: "",
      });
    }
  }, [query]);
  const [parameterInput, setParameterInput] = useState("");

  console.log(query);
  console.log(editedQuery);

  const handleSave = () => {
    onSave(editedQuery);
  };

  const handleAddParameter = () => {
    if (parameterInput.trim()) {
      setEditedQuery((prev) => ({
        ...prev,
        parameters: [...(prev.parameters || []), parameterInput.trim()],
      }));
      setParameterInput("");
    }
  };

  const handleRemoveParameter = (index: number) => {
    setEditedQuery((prev) => ({
      ...prev,
      parameters: prev.parameters?.filter((_, i) => i !== index) || [],
    }));
  };

  const handleTypeChange = (value: string) => {
    if (value === "SQL" || value === "SERVICE") {
      setEditedQuery((prev) => ({ ...prev, type: value }));
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-h-[90vh] max-w-4xl overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {queryIndex !== undefined ? "Edit Query" : "Add New Query"}
          </DialogTitle>
        </DialogHeader>

        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="mb-1 block text-sm font-medium">
                Query Key
              </label>
              <Input
                value={editedQuery.queryKey}
                onChange={(e) =>
                  setEditedQuery((prev) => ({
                    ...prev,
                    queryKey: e.target.value,
                  }))
                }
                placeholder="e.g., customersByMonth"
              />
            </div>
            <div>
              <label className="mb-1 block text-sm font-medium">
                Reference Key
              </label>
              <Input
                value={editedQuery.referenceKey}
                onChange={(e) =>
                  setEditedQuery((prev) => ({
                    ...prev,
                    referenceKey: e.target.value,
                  }))
                }
                placeholder="e.g., customers_by_month"
              />
            </div>
          </div>

          <div>
            <label className="mb-1 block text-sm font-medium">Type</label>
            <select
              value={editedQuery.type}
              onChange={(e) => handleTypeChange(e.target.value)}
              className="w-full rounded-md border border-gray-300 px-3 py-2 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
            >
              <option value="SQL">SQL</option>
              <option value="SERVICE">SERVICE</option>
            </select>
          </div>

          <div>
            <label className="mb-1 block text-sm font-medium">Parameters</label>
            <div className="mb-2 flex gap-2">
              <Input
                value={parameterInput}
                onChange={(e) => setParameterInput(e.target.value)}
                placeholder="Add parameter"
                onKeyDown={(e) => e.key === "Enter" && handleAddParameter()}
              />
              <Button onClick={handleAddParameter} size="sm" type="button">
                <Plus className="h-4 w-4" />
              </Button>
            </div>
            <div className="flex flex-wrap gap-2">
              {editedQuery.parameters?.map((param, index) => (
                <div
                  key={index}
                  className="flex items-center gap-1 rounded bg-gray-100 px-2 py-1"
                >
                  <span className="text-sm">{param}</span>
                  <Button
                    variant="ghost"
                    type="button"
                    size="sm"
                    className="h-5 w-5 p-0"
                    onClick={() => handleRemoveParameter(index)}
                  >
                    <Trash2 className="h-3 w-3" />
                  </Button>
                </div>
              ))}
            </div>
          </div>

          <div>
            <label className="mb-1 block text-sm font-medium">SQL Query</label>
            <div className="overflow-hidden rounded-md border">
              <MonacoEditor
                height="300px"
                language="sql"
                value={editedQuery.sql || ""}
                onChange={(value) =>
                  setEditedQuery((prev) => ({ ...prev, sql: value || null }))
                }
                options={{
                  minimap: { enabled: false },
                  scrollBeyondLastLine: false,
                  fontSize: 14,
                  lineNumbers: "on",
                  roundedSelection: false,
                  automaticLayout: true,
                }}
              />
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose} type="button">
            Cancel
          </Button>
          <Button onClick={handleSave} type="button">
            Save Query
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
