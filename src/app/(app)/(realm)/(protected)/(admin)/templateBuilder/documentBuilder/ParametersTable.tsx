// "use client";
// import { useFormContext } from "react-hook-form";
// import {
//   Table,
//   TableBody,
//   TableCell,
//   TableHead,
//   TableHeader,
//   TableRow,
// } from "@/components/ui/table";
// import { Badge } from "@/components/ui/badge";
// import { CheckCircle, XCircle } from "lucide-react";
// import { Template, FormDataParameter } from "@/hooks/api/useDocument";

// export function ParametersTable() {
//   const { watch } = useFormContext<Template>();
//   const parameters = watch("formData.parameters");

//   if (!parameters || parameters.length === 0) {
//     return (
//       <div className="rounded-md border border-dashed border-gray-300 p-6 text-center">
//         <p className="text-sm text-gray-500">
//           No parameters configured. Add parameters to allow users to customize the report.
//         </p>
//       </div>
//     );
//   }

//   return (
//     <div className="overflow-x-auto rounded-md border">
//       <Table>
//         <TableHeader>
//           <TableRow>
//             <TableHead className="w-[180px]">Label</TableHead>
//             <TableHead>Type</TableHead>
//             <TableHead>Field Name</TableHead>
//             <TableHead className="w-[100px]">Required</TableHead>
//             <TableHead className="w-[180px]">Options</TableHead>
//           </TableRow>
//         </TableHeader>
//         <TableBody>
//           {parameters.map((param: FormDataParameter, index) => (
//             <TableRow key={index}>
//               <TableCell className="font-medium">{param.label || `Parameter ${index + 1}`}</TableCell>
//               <TableCell>
//                 <Badge variant="outline">{param.type}</Badge>
//               </TableCell>
//               <TableCell>{param.fieldName || '-'}</TableCell>
//               <TableCell>
//                 {param.required ? (
//                   <CheckCircle className="h-4 w-4 text-green-500" />
//                 ) : (
//                   <XCircle className="h-4 w-4 text-gray-300" />
//                 )}
//               </TableCell>
//               <TableCell>
//                 {param.type === 'dateRange' && param.params && param.params.length > 0 ? (
//                   <div className="flex flex-wrap gap-1">
//                     {param.params.map((option: string) => (
//                       <Badge key={option} variant="secondary" className="text-xs">
//                         {option}
//                       </Badge>
//                     ))}
//                   </div>
//                 ) : param.type === 'text' && param.validation?.regex ? (
//                   <span className="text-xs">Has validation</span>
//                 ) : (
//                   <span className="text-xs text-gray-500">No options</span>
//                 )}
//               </TableCell>
//             </TableRow>
//           ))}
//         </TableBody>
//       </Table>
//     </div>
//   );
// }