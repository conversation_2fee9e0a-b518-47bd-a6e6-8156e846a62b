"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import {
  TbFileTypeDocx,
  TbFileTypeHtml,
  TbFileTypePdf,
  TbFileTypeTxt,
  TbFileTypeXls,
  TbPaperclip,
} from "react-icons/tb";
import { TemplatesResponse, Template } from "@/hooks/api/useDocument";
import { Plus } from "lucide-react";
import { Button } from "@/components/ui/button";

const DocumentTemplatesGrid = ({ data }: { data: TemplatesResponse }) => {
  const router = useRouter();
  const [searchTerm, setSearchTerm] = useState("");
  const [activeCategory, setActiveCategory] = useState<string | null>(null);

  // pull templates array off data
  const templates = data.templates;

  // Extract unique categories
  const categories = Array.from(new Set(templates.map((doc) => doc.category)));

  // Filter documents based on search and category
  const filteredDocuments = templates.filter((doc) => {
    const matchesSearch =
      doc.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (doc.description?.toLowerCase().includes(searchTerm.toLowerCase()) ??
        false);
    const matchesCategory = activeCategory
      ? doc.category === activeCategory
      : true;
    return matchesSearch && matchesCategory;
  });

  // Group documents by subCategory for the current view
  const documentsBySubcategory = filteredDocuments.reduce(
    (acc, doc) => {
      const key = doc.subCategory ?? "Uncategorized";
      if (!acc[key]) acc[key] = [];
      acc[key].push(doc);
      return acc;
    },
    {} as Record<string, Template[]>,
  );

  // Handle document click
  const handleDocumentClick = (templateUUID: string) => {
    router.push(`/templateBuilder/documentBuilder/${templateUUID}`);
  };

  // Get file icon based on filetype
  const getFileIcon = (fileType: string) => {
    switch (fileType?.toLowerCase()) {
      case "pdf":
        return <TbFileTypePdf />;
      case "docx":
      case "doc":
        return <TbFileTypeDocx />;
      case "xlsx":
      case "xls":
        return <TbFileTypeXls />;
      case "html":
        return <TbFileTypeHtml />;
      case "txt":
      case "text":
        return <TbFileTypeTxt />;
      default:
        return <TbPaperclip />;
    }
  };

  return (
    <div className="flex h-full w-full flex-col overflow-auto">
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8 flex items-center justify-between gap-10">
          <div>
            <h1 className="mb-2 text-3xl font-bold text-gray-800">
              Document Templates
            </h1>
            <p className="text-gray-600">
              Browse and manage your document templates
            </p>
          </div>
          <div className="flex gap-2">
            <Button
              variant="primary"
              size="sm"
              onClick={() =>
                router.push("/templateBuilder/documentBuilder/new")
              }
            >
              <Plus className="mr-2 h-4 w-4" />
              New Template
            </Button>
          </div>
        </div>

        {/* Search and filter */}
        <div className="mb-8 flex flex-col justify-between gap-4 md:flex-row">
          <div className="relative w-full md:w-1/3">
            <input
              type="text"
              placeholder="Search documents..."
              className="w-full rounded-lg border px-4 py-2 shadow-sm focus:border-blue-500 focus:ring-2 focus:ring-blue-500"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
            <div className="absolute right-3 top-2.5">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-5 w-5 text-gray-400"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                />
              </svg>
            </div>
          </div>

          {/* Category filter */}
          <div className="flex gap-2 overflow-x-auto pb-2">
            <button
              className={`rounded-lg px-4 py-2 text-sm font-medium transition ${
                activeCategory === null
                  ? "bg-blue-500 text-white"
                  : "bg-gray-100 text-gray-700 hover:bg-gray-200"
              }`}
              onClick={() => setActiveCategory(null)}
            >
              All
            </button>
            {categories.map((category) => (
              <button
                key={category}
                className={`whitespace-nowrap rounded-lg px-4 py-2 text-sm font-medium transition ${
                  activeCategory === category
                    ? "bg-blue-500 text-white"
                    : "bg-gray-100 text-gray-700 hover:bg-gray-200"
                }`}
                onClick={() => setActiveCategory(category ?? null)}
              >
                {category}
              </button>
            ))}
          </div>
        </div>

        {/* No results message */}
        {Object.keys(documentsBySubcategory).length === 0 && (
          <div className="py-12 text-center">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="mx-auto h-12 w-12 text-gray-400"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
            <h3 className="mt-2 text-sm font-medium text-gray-900">
              No documents found
            </h3>
            <p className="mt-1 text-sm text-gray-500">
              Try changing your search or filter criteria.
            </p>
          </div>
        )}

        {/* Documents by subcategory */}
        <div className="space-y-8">
          {Object.entries(documentsBySubcategory).map(([subCategory, docs]) => (
            <div
              key={subCategory}
              className="overflow-hidden rounded-lg border bg-white shadow"
            >
              <div className="border-b bg-gray-50 px-4 py-3">
                <h2 className="text-lg font-medium text-gray-900">
                  {subCategory}
                </h2>
              </div>
              <div className="grid grid-cols-1 gap-4 p-4 sm:grid-cols-2 lg:grid-cols-3">
                {docs.map((doc) => (
                  <div
                    key={doc.templateUUID}
                    onClick={() => handleDocumentClick(doc.templateUUID)}
                    className="cursor-pointer rounded-lg border bg-gray-50 p-4 transition-shadow hover:bg-white hover:shadow-md"
                  >
                    <div className="flex items-start gap-3">
                      {getFileIcon(doc?.fileType ?? "")}
                      <div className="min-w-0 flex-1">
                        <h3 className="truncate text-sm font-medium text-gray-900">
                          {doc.name}
                        </h3>
                        {doc.description && (
                          <p className="mt-1 line-clamp-2 text-xs text-gray-500">
                            {doc.description}
                          </p>
                        )}
                        <div className="mt-2 flex items-center text-xs text-gray-500">
                          {/* <span className="bg-blue-100 text-blue-800 px-2 py-0.5 rounded text-xs font-medium">
                            v{doc.activeVersion}
                          </span>
                          <span className="mx-2">•</span> */}
                          <span className="uppercase">{doc.fileType}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default DocumentTemplatesGrid;
