// "use client";
// import { useFormContext } from "react-hook-form";
// import { Template } from "@/hooks/api/useDocument";
// import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
// import { Badge } from "@/components/ui/badge";
// import { ParametersTable } from "./ParametersTable";

// export function ReportConfigSummary() {
//   const { watch } = useFormContext<Template>();
//   const isReport = watch("isReport");
//   const formData = watch("formData");
  
//   if (!isReport || !formData) {
//     return null;
//   }

//   return (
//     <Card className="mt-6">
//       <CardHeader>
//         <CardTitle>Report Configuration Summary</CardTitle>
//         <CardDescription>
//           Preview of how the report will be configured
//         </CardDescription>
//       </CardHeader>
//       <CardContent className="space-y-6">
//         <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
//           <div>
//             <h3 className="mb-2 text-sm font-medium">Report Type</h3>
//             <Badge variant="outline" className="text-sm">
//               {formData.type || "Not specified"}
//             </Badge>
//           </div>
//           <div>
//             <h3 className="mb-2 text-sm font-medium">Report Type ID</h3>
//             <span className="text-sm text-gray-700">
//               {formData.reportTypeId || "Not specified"}
//             </span>
//           </div>
//         </div>

//         <div>
//           <h3 className="mb-2 text-sm font-medium">Export Types</h3>
//           <div className="flex flex-wrap gap-2">
//             {formData.exportTypes && formData.exportTypes.length > 0 ? (
//               formData.exportTypes.map((type) => (
//                 <Badge key={type} className="text-xs uppercase">
//                   {type}
//                 </Badge>
//               ))
//             ) : (
//               <span className="text-sm text-gray-500">No export types specified</span>
//             )}
//           </div>
//         </div>

//         <div>
//           <h3 className="mb-2 text-sm font-medium">Parameters</h3>
//           <ParametersTable />
//         </div>
//       </CardContent>
//     </Card>
//   );
// }