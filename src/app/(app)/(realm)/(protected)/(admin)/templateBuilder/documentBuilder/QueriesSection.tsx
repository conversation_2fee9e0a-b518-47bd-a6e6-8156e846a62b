// QueriesSection.tsx
"use client";
import { useState } from "react";
import { useFormContext } from "react-hook-form";
import { Button } from "@/components/ui/button";
import { Plus, Pencil, Trash2 } from "lucide-react";
import { Template, Query } from "@/hooks/api/useDocument";
import { QueryEditModal } from "./QueryEditModal";

export function QueriesSection() {
  const { watch, getValues, setValue } = useFormContext<Template>();
  const [editingQuery, setEditingQuery] = useState<Query | null>(null);
  const [editingQueryIndex, setEditingQueryIndex] = useState<number | undefined>();
  const [isQueryModalOpen, setIsQueryModalOpen] = useState(false);

  const queries = watch("queries");

  const handleEditQuery = (query: Query, index: number) => {
    setEditingQuery(query);
    setEditingQueryIndex(index);
    setIsQueryModalOpen(true);
  };

  const handleAddQuery = () => {
    setEditingQuery(null);
    setEditingQueryIndex(undefined);
    setIsQueryModalOpen(true);
  };

  const handleSaveQuery = (query: Query) => {
    const currentQueries = getValues("queries") || [];

    if (editingQueryIndex !== undefined) {
      // Editing existing query
      const updatedQueries = [...currentQueries];
      updatedQueries[editingQueryIndex] = query;
      setValue("queries", updatedQueries);
    } else {
      // Adding new query
      setValue("queries", [...currentQueries, query]);
    }

    setIsQueryModalOpen(false);
    setEditingQuery(null);
    setEditingQueryIndex(undefined);
  };

  const handleRemoveQuery = (index: number) => {
    const currentQueries = getValues("queries") || [];
    const updatedQueries = currentQueries.filter((_, i) => i !== index);
    setValue("queries", updatedQueries);
  };

  return (
    <>
      <div className="rounded-lg bg-white shadow">
        <div className="flex items-center justify-between border-b bg-gray-50 px-4 py-4">
          <h2 className="text-lg font-medium">Report Queries</h2>
          <Button onClick={handleAddQuery} size="sm">
            <Plus className="mr-2 h-4 w-4" />
            Add Query
          </Button>
        </div>
        <div className="space-y-6 px-4 py-5 ">
          {queries?.length ? (
            queries.map((query, index) => (
              <div
                key={query.queryKey}
                className="border-b p-3 rounded last:border-0 hover:bg-blue-50"
              >
                <div className="mb-4 flex items-start justify-between">
                  <div>
                    <h3 className="text-base font-semibold text-gray-900">
                      {query.queryKey}
                    </h3>
                    <p className="text-sm text-gray-600">
                      Reference: {query.referenceKey}
                    </p>
                  </div>
                  <div className="flex gap-2">
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => handleEditQuery(query, index)}
                    >
                      <Pencil className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      type="button"
                      size="sm"
                      onClick={() => handleRemoveQuery(index)}
                      className="text-red-600 hover:text-red-700"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>

                <div className="space-y-3 ">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">
                      Type
                    </label>
                    <p className="text-sm text-gray-900">{query.type}</p>
                  </div>

                  {query.parameters?.length > 0 && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700">
                        Parameters
                      </label>
                      <p className="text-sm text-gray-900">
                        {query.parameters.join(", ")}
                      </p>
                    </div>
                  )}
                </div>
              </div>
            ))
          ) : (
            <div className="py-6 text-center text-gray-500">
              No queries added yet. Click the &quot;Add Query&quot; button
              to create one.
            </div>
          )}
        </div>
      </div>

      <QueryEditModal
        isOpen={isQueryModalOpen}
        onClose={() => setIsQueryModalOpen(false)}
        query={editingQuery}
        queryIndex={editingQueryIndex}
        onSave={handleSaveQuery}
      />
    </>
  );
}