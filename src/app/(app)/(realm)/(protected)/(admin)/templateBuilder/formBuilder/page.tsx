// FormBuilderWizardPage.tsx
"use client";
import React from "react";
import {
  FormBuilderWizardProvider,
  useFormBuilderWizard,
} from "./FormBuilderWizardContext";
import Tabs from "./main/Tabs";
import JsonWizard from "./builder/JsonWizard";
import FormList from "./settings/FormList";
import { useAtom } from "jotai";
import { userAtom } from "@/components/sidebar/main/User";

const FormBuilderWizardPage = () => {
  return (
    <FormBuilderWizardProvider>
      <FormBuilderWizardContent />
    </FormBuilderWizardProvider>
  );
};

const FormBuilderWizardContent = () => {
  const { tab, config } = useFormBuilderWizard();
  const [user] = useAtom(userAtom);

  return (
    <div className="flex h-full w-full flex-col overflow-hidden">
      <Tabs />
      {tab === "builder" && <JsonWizard />}
      {/* {tab === "preview" && <FormBuilder />} */}
      {tab === "settings" && <FormList />}
    </div>
  );
};

export default FormBuilderWizardPage;