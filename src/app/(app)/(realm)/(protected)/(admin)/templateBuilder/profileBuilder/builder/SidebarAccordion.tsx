import { useProfileBuilder } from "../ProfileBuilderWizardContext";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { useState } from "react";

import { usePathname, useSearchParams } from "next/navigation";
import Link from "next/link";
import { cn } from "@/lib/utils";

type FormListType = {
  category: string;
  types?: FormListType[];
  forms?: {
    permissions?: string[];
    form: string;
  }[];
};

type FormsType = {
  formName: string;
  fields: {
    title: {
      image: string;
      label: string;
    };
    active: {
      type: string;
      label: string;
    };
    button: {
      href: string;
      type: string;
      label: string;
    };
  };
  bulletPoints?: {
    items: {
      icon: string;
      label: string;
      info?: {
        items: string[];
        label: string;
      }[];
    }[];
  }[];
};

type ConfigOptionsType = {
  formList: FormListType[];
  forms: {
    [key: string]: FormsType;
  };
};

const AccordionComponent = ({ config }: { config: string }) => {
  const { configList } = useProfileBuilder();
  const searchParams = useSearchParams();
  const [openItems, setOpenItems] = useState<string[]>([]);

  if (!configList) {
    return null;
  }

  console.log(configList);

  return (
    <Accordion type="multiple" defaultValue={openItems}>
      <RenderItemsComponent configList={configList} config={config} />
    </Accordion>
  );
};

export default AccordionComponent;

const RenderItemsComponent = ({
  configList,
  config,
}: {
  configList: ConfigOptionsType;
  config: string;
}) => {
  const { formList, forms } = configList;
  let parent = "";

  return (
    <RenderItems
      forms={forms}
      config={config}
      formList={formList}
      parentKey={parent}
    />
  );
};

const RenderItems = ({
  forms,
  formList,
  parentKey = "",
  config,
}: {
  forms: { [key: string]: FormsType };
  formList: FormListType[];
  parentKey?: string;
  config: string;
}) => {
  const pathname = usePathname();
  const searchParam = useSearchParams().get(config);

  return (
    <>
      {formList?.map((item, index) => {
        const key = parentKey
          ? `${parentKey}.${item.category ?? ""}`
          : item.category ?? "";

        if (item.types && item.types.length > 0) {
          return (
            <AccordionItem key={index} value={key} className="border-b-0 pb-0">
              <AccordionTrigger className="pb-0 pt-2">
                {item.category}
              </AccordionTrigger>
              <AccordionContent className="border-l border-clerk-primary pb-0 pl-4">
                <RenderItems
                  forms={forms}
                  formList={item.types}
                  parentKey={key}
                  config={config}
                />
              </AccordionContent>
            </AccordionItem>
          );
        } else if (item.forms && item.forms.length > 0) {
          return (
            <AccordionItem key={index} value={key} className="border-b-0 pb-0">
              <AccordionTrigger className="pb-0 pt-2">
                {item.category}
              </AccordionTrigger>
              <AccordionContent className="border-l border-clerk-primary pb-0 pl-4">
                {item.forms.map((formItem, formIndex) => {
                  const formName = formItem.form;
                  return (
                    <div key={formIndex} className="pt-2">
                      <Link
                        href={`${pathname}?onlineProfile=${formName}`}
                        className={cn("text-blue-400",
                          searchParam === formName ? "text-blue-600 font-bold" : "font-base"
                        )}
                      >
                        {forms[formName]?.formName}
                      </Link>
                    </div>
                  );
                })}
              </AccordionContent>
            </AccordionItem>
          );
        } else {
          return (
            <AccordionItem key={index} value={key} className="border-b-0 pb-0">
              <AccordionTrigger className="pb-0 pt-2">
                {item.category}
              </AccordionTrigger>
            </AccordionItem>
          );
        }
      })}
    </>
  );
};
