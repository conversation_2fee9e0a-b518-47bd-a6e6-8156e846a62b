import { useProfileBuilder } from "../ProfileBuilderWizardContext";
import { Button } from "@/components/ui/button";
import MonacoEditor from "@monaco-editor/react";
import { useState } from "react";
import { useUpdateJSONStorage } from "@/hooks/api/useAdmin";
import { CgSpinner } from "react-icons/cg";
import { useAtom } from "jotai";
import { toastAtom } from "@/components/ui/toast/toast";
import AccordionComponent from "./SidebarAccordion";

export default function JsonWizard() {
  const { jsonListIsLoading, jsonListError, configList } = useProfileBuilder();

  if (jsonListIsLoading) {
    return <div className="animate-pulse p-6">Loading Form List...</div>;
  }

  if (jsonListError) {
    return <div className="p-6 text-red-600">Error Loading Form List...</div>;
  }

  if (!configList) {
    return <div className="p-6">No Data...</div>;
  }

  return (
    <div className="flex h-full w-full overflow-hidden ">
      <Sidebar />
      <MainContent />
    </div>
  );
}

const Sidebar = () => {
  const {
    config,
    configItem,
    configList,
    jsonListError,
    jsonListIsLoading,
    refetchJsonData,
  } = useProfileBuilder();

  const mutateConfig = useUpdateJSONStorage("onlineProfile", configItem);

  const [error, setError] = useState<string | null>(null);
  const [_, setToast] = useAtom(toastAtom);

  if (jsonListIsLoading) {
    return <div>Loading...</div>;
  }

  if (jsonListError) {
    return <div>Error...</div>;
  }

  console.log(configList);

  return (
    <aside className="flex h-full w-[300px] shrink-0 flex-col gap-4 overflow-auto border-r border-gray-200 p-6">

      <AccordionComponent config="onlineProfile" />

      <p className="border border-red-600 bg-red-50 p-3 text-red-700">
        <strong>Warning:</strong> Any changes made here will override the
        current form. Please make sure you have previewed your work before
        saving. If you make any changes in the production environment, it will
        immediately affect the form. Please be cautious before making any
        changes.
      </p>

      <Button
        onClick={() => {
          const data = JSON.stringify(config, null, 2);
          console.log(config, configItem);
          mutateConfig.mutate(config, {
            onSuccess: () => {
              setToast({
                status: "success",
                label: "Updated",
                message: `Online Forms have been successfully updated`,
              });
              refetchJsonData();
            },
            onError: (e: any) => {
              console.error(e);
              setError("Error saving config");
              setToast({
                status: "error",
                label: "Error",
                message: `Error saving config`,
              });
            },
          });
        }}
        disabled={mutateConfig.isLoading}
      >
        {mutateConfig.isLoading ? (
          <span className="flex items-center gap-2">
            <CgSpinner className="animate-spin" /> Saving...
          </span>
        ) : (
          "Save"
        )}
      </Button>
      {error && <p className="text-red-600">{error}</p>}
    </aside>
  );
};

const MainContent = () => {
  const { config, setConfig, configItem } = useProfileBuilder();
  const [error, setError] = useState<string | null>(null);

  const handleEditorChange = (value: string | undefined) => {
    if (!value) return;
    try {
      const parsedValue = JSON.parse(value);
      setConfig(parsedValue);
      setError(null);
    } catch (e) {
      setError("Invalid JSON format");
    }
  };

  if (config === null || config === undefined) {
    return (
      <div className="flex h-full w-full justify-center py-10 text-2xl text-gray-500">
        No Data...
      </div>
    );
  }

  return (
    <div className="flex-grow bg-[#1E1E1E]">
      <MonacoEditor
        key={configItem}
        height="100%"
        language="json"
        theme="vs-dark"
        value={JSON.stringify(config, null, 2)}
        onChange={handleEditorChange}
        options={{
          minimap: { enabled: false },
          fontSize: 16,
          selectOnLineNumbers: true,
          automaticLayout: true,
          scrollBeyondLastLine: false,
          wordWrap: "on",
          padding: { top: 20 }
        }}
      />
      {error && <p className="text-red-600 mt-2 px-6">{error}</p>}
    </div>
  );
};
