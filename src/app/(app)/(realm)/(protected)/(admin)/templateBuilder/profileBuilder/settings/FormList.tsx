import { useProfileBuilder } from "../ProfileBuilderWizardContext";
import { Button } from "@/components/ui/button";
import { toastAtom } from "@/components/ui/toast/toast";
import { useUpdateJSONStorage } from "@/hooks/api/useAdmin";
import MonacoEditor from "@monaco-editor/react";
import { useAtom } from "jotai";
import { useState } from "react";
import { CgSpinner } from "react-icons/cg";

export default function FormList() {
  const { jsonListIsLoading, jsonListError, configList } = useProfileBuilder();

  if (jsonListIsLoading) {
    return <div className="animate-pulse p-6">Loading Form List...</div>;
  }

  if (jsonListError) {
    return <div className="p-6 text-red-600">Error Loading Form List...</div>;
  }

  if (!configList) {
    return <div className="p-6">No Data...</div>;
  }

  return (
    <div className="flex h-full w-full overflow-hidden ">
      {/* Sidebar */}
      <Sidebar />

      {/* Main Content */}
      <MainContent />
    </div>
  );
}

const Sidebar = () => {
  const { jsonListError, jsonListIsLoading, configList, refetchJsonList } =
    useProfileBuilder();
  const mutateConfig = useUpdateJSONStorage("list", "onlineForms");
  const [error, setError] = useState<string | null>(null);
  const [_, setToast] = useAtom(toastAtom);

  if (jsonListIsLoading) {
    return <div>Loading...</div>;
  }

  if (jsonListError) {
    return <div>Error...</div>;
  }

  return (
    <aside className="flex h-full w-[300px] shrink-0 flex-col gap-4 overflow-auto border-r border-gray-200 p-6">
      <h1 className="text-2xl">Settings</h1>
      <p className="border border-red-600 bg-red-50 p-3 text-red-700">
        <strong>Warning:</strong> Any changes made here will override the
        current form. Please make sure you have previewed your work before
        saving. If you make any changes in the production environment, it will
        immediately affect the form. Please be cautious before making any
        changes.
      </p>

      <Button
        onClick={() => {
          console.log("Save");

          mutateConfig.mutate(configList, {
            onSuccess: () => {
              setToast({
                status: "success",
                label: "Updated",
                message: `Online Forms have been successfully updated`,
              });
              refetchJsonList();
            },
            onError: (e: any) => {
              console.error(e);
              setError("Error saving config");
            },
          });
        }}
        disabled={mutateConfig.isLoading}
      >
        {mutateConfig.isLoading ? (
          <span className="flex items-center gap-2">
            <CgSpinner className="animate-spin" /> Saving...
          </span>
        ) : (
          "Save"
        )}
      </Button>
      {error && <p className="text-red-600">{error}</p>}
    </aside>
  );
};

const MainContent = () => {
  const { configList, setConfigList } = useProfileBuilder();

  const handleEditorChange = (value: string | undefined) => {
    if (value) {
      console.log(value);
      try {
        const updatedConfig = JSON.parse(value);
        console.log(updatedConfig);
        setConfigList(updatedConfig);
      } catch (e) {
        console.error("Error parsing JSON:", e);
      }
    }
  };

  if (!configList) {
    return <div>No Data...</div>;
  }

  return (
    <div className="flex-grow">
      <MonacoEditor
        height="100%"
        language="json"
        theme="vs-dark"
        value={JSON.stringify(configList, null, 2)}
        onChange={handleEditorChange}
        options={{
          selectOnLineNumbers: true,
          automaticLayout: true,
        }}
      />
    </div>
  );
};
