"use client";
import React from "react";
import {
  ProfileBuilderProvider,
  useProfileBuilder, 
} from "./ProfileBuilderWizardContext";
import Tabs from "./main/Tabs";
import JsonWizard from "./builder/JsonWizard";
import FormList from "./settings/FormList";
import { useAtom } from "jotai";
import { userAtom } from "@/components/sidebar/main/User";

const ProfileBuilderPage = () => {
  return (
    <ProfileBuilderProvider>
      <ProfileBuilderContent />
    </ProfileBuilderProvider>
  );
};

const ProfileBuilderContent = () => {
  const { tab } = useProfileBuilder();
  const [user] = useAtom(userAtom);

  return (
    <div className="flex h-full w-full flex-col overflow-hidden">
      <Tabs />
      {tab === "profile" && <JsonWizard />}
      {tab === "settings" && <FormList />}
    </div>
  );
};

export default ProfileBuilderPage;