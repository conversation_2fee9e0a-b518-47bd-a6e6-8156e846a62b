"use client";
import {
  useGetJSONStorage,
  useGetJSONStorageHistory,
} from "@/hooks/api/useAdmin";
import { useSearchParams } from "next/navigation";
import React, {
  createContext,
  useContext,
  useState,
  ReactNode,
  useEffect,
} from "react";
import { isEqual } from "lodash";

type TabType = "rules" | "settings";

export type TabOption = {
  key: TabType;
  label: string;
};

type RuleType = {
  rule: string;
  label: string;
};

type RuleListType = {
  category: string;
  types?: RuleListType[];
  rules?: RuleType[];
};

type ConfigOptionsType = {
  list: RuleListType[];
};

type RuleBuilderWizardContextType = {
  tab: TabType;
  setTab: (tab: TabType) => void;
  config: any;
  setConfig: (context: any) => void;
  configItem: string | null;
  setConfigItem: (configItem: string | null) => void;

  // JSON Config
  jsonListData: ConfigOptionsType;
  jsonListIsLoading: boolean;
  jsonListIsFetching: boolean;
  jsonListError: any;
  refetchJsonList: () => void;

  // JSON Config
  jsonData: any;
  jsonIsLoading: boolean;
  jsonIsFetching: boolean;
  jsonError: any;
  refetchJsonData: () => void;
  configList: ConfigOptionsType | null;
  setConfigList: (configList: ConfigOptionsType | null) => void;

  // History
  jsonHistoryData: any;
  jsonHistoryIsLoading: boolean;
  jsonHistoryIsFetching: boolean;
  jsonHistoryError: any;
  refetchJsonHistory: () => void;
};

const RuleBuilderWizardContext = createContext<
  RuleBuilderWizardContextType | undefined
>(undefined);

export const RuleBuilderWizardProvider = ({
  children,
}: {
  children: ReactNode;
}) => {
  // Get URL Params
  const tabOption = useSearchParams().get("tab");
  const rulesOption = useSearchParams().get("rules");

  // States
  const [tab, setTab] = useState<TabType>((tabOption as TabType) || "rules");
  const [config, setConfig] = useState<any | null>(null);
  const [configItem, setConfigItem] = useState<string | null>(null);
  const [configList, setConfigList] = useState<ConfigOptionsType | null>(null);

  // API Calls
  const {
    data: jsonListData,
    isLoading: jsonListIsLoading,
    isFetching: jsonListIsFetching,
    error: jsonListError,
    refetch: refetchJsonList,
  } = useGetJSONStorage("list", "rules");

  console.log(rulesOption)

  const {
    data: jsonData,
    isLoading: jsonIsLoading,
    isFetching: jsonIsFetching,
    error: jsonError,
    refetch: refetchJsonData,
  } = useGetJSONStorage("rules", rulesOption);

  const {
    data: jsonHistoryData,
    isLoading: jsonHistoryIsLoading,
    isFetching: jsonHistoryIsFetching,
    error: jsonHistoryError,
    refetch: refetchJsonHistory,
  } = useGetJSONStorageHistory("rules", rulesOption);

  // UseEffects
  useEffect(() => {
    if (!isEqual(jsonListData, configList)) {
      setConfigList(jsonListData || []);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [jsonListData]);

  useEffect(() => {
    console.log(jsonData)
    if (jsonData) {
      console.log(jsonData);
      setConfig(jsonData);
    } else {
      if (rulesOption) {
        setConfig({});
      } else {
        setConfig(null);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [jsonData, rulesOption]);

  useEffect(() => {
    setConfigItem(rulesOption);
    refetchJsonData();
    refetchJsonHistory();
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [rulesOption]);

  useEffect(() => {
    if (tabOption) {
      setTab(tabOption as TabType);
    }
  }, [tabOption]);

  return (
    <RuleBuilderWizardContext.Provider
      value={{
        tab,
        setTab,
        config,
        setConfig,
        configList,
        setConfigList,
        configItem,
        setConfigItem,

        // GET JSON LIST Config
        jsonListData,
        jsonListIsLoading,
        jsonListIsFetching,
        jsonListError,
        refetchJsonList,

        // GET JSON Config
        jsonData,
        jsonIsLoading,
        jsonIsFetching,
        jsonError,
        refetchJsonData,

        // History
        jsonHistoryData,
        jsonHistoryIsLoading,
        jsonHistoryIsFetching,
        jsonHistoryError,
        refetchJsonHistory,
      }}
    >
      {children}
    </RuleBuilderWizardContext.Provider>
  );
};

export const useRuleBuilderWizard = () => {
  const context = useContext(RuleBuilderWizardContext);
  if (context === undefined) {
    throw new Error(
      "useRuleBuilderWizard must be used within a RuleBuilderWizardProvider",
    );
  }
  return context;
};
