import { useRuleBuilderWizard } from "../RuleBuilderWizardContext";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { useState } from "react";

import { usePathname, useSearchParams } from "next/navigation";
import Link from "next/link";
import { cn } from "@/lib/utils";

type RuleType = {
  rule: string;
  label: string;
};

type RuleListType = {
  category: string;
  types?: RuleListType[];
  rules?: RuleType[];
};

type ConfigOptionsType = {
  list: RuleListType[];
};

const AccordionComponent = ({ config }: { config: string }) => {
  const { configList } = useRuleBuilderWizard();
  const [openItems, setOpenItems] = useState<string[]>([]);

  if (!configList) {
    return null;
  }

  return (
    <Accordion type="multiple" defaultValue={openItems}>
      <RenderItemsComponent configList={configList} config={config} />
    </Accordion>
  );
};

export default AccordionComponent;

const RenderItemsComponent = ({
  configList,
  config,
}: {
  configList: ConfigOptionsType;
  config: string;
}) => {
  const { list } = configList;
  let parent = "";

  return (
    <RenderItems
      list={list}
      config={config}
      parentKey={parent}
    />
  );
};

const RenderItems = ({
  list,
  parentKey = "",
  config,
}: {
  list: RuleListType[];
  parentKey?: string;
  config: string;
}) => {
  const pathname = usePathname();
  const searchParam = useSearchParams().get(config);
  const { tab } = useRuleBuilderWizard();

  console.log(tab)

  return (
    <>
      {list?.map((item, index) => {
        const key = parentKey
          ? `${parentKey}.${item.category ?? ""}`
          : item.category ?? "";

        if (item.types && item.types.length > 0) {
          return (
            <AccordionItem key={index} value={key} className="border-b-0 pb-0">
              <AccordionTrigger className="pb-0 pt-2">
                {item.category}
              </AccordionTrigger>
              <AccordionContent className="border-l border-clerk-primary pb-0 pl-4">
                <RenderItems
                  list={item.types}
                  parentKey={key}
                  config={config}
                />
              </AccordionContent>
            </AccordionItem>
          );
        } else if (item.rules && item.rules.length > 0) {
          return (
            <AccordionItem key={index} value={key} className="border-b-0 pb-0">
              <AccordionTrigger className="pb-0 pt-2">
                {item.category}
              </AccordionTrigger>
              <AccordionContent className="border-l border-clerk-primary pb-0 pl-4">
                {item.rules.map((ruleItem, ruleIndex) => {
                  const ruleName = ruleItem.rule;
                  return (
                    <div key={ruleIndex} className="pt-2">
                      <Link
                        href={`${pathname}?tab=${tab}&rules=${ruleName}`}
                        className={cn(
                          "text-blue-400",
                          searchParam === ruleName ? "text-blue-600 font-bold" : "font-base"
                        )}
                      >
                        {ruleItem.label}
                      </Link>
                    </div>
                  );
                })}
              </AccordionContent>
            </AccordionItem>
          );
        } else {
          return (
            <AccordionItem key={index} value={key} className="border-b-0 pb-0">
              <AccordionTrigger className="pb-0 pt-2">
                {item.category}
              </AccordionTrigger>
            </AccordionItem>
          );
        }
      })}
    </>
  );
};
