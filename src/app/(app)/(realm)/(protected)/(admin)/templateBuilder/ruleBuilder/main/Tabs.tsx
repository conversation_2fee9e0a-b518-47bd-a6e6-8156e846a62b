// Tabs.tsx
import React from "react";
import { cn } from "@/lib/utils";
import { FiSettings } from "react-icons/fi";
import { usePathname, useRouter } from "next/navigation";
import { TabOption } from "../RuleBuilderWizardContext";
import { useRuleBuilderWizard } from "../RuleBuilderWizardContext";


const tabOptions: TabOption[] = [
  {
    key: "rules",
    label: "Rules",
  }
];

const Tabs = () => {
  const { tab, setTab } = useRuleBuilderWizard();
  const router = useRouter();
  const pathname = usePathname();
  return (
    <div className="flex h-fit shrink-0 items-center gap-10 overflow-x-auto bg-white px-6 w-full">
      {tabOptions.map((t: TabOption) => {
        const active = t.key === tab;
        return (
          <button
            key={t.key}
            onClick={() => {
              router.push(pathname + `?tab=` + t.key)
              setTab(t.key)}
            }
            className={cn(
              "relative px-2 py-2 text-stone-500 translate-all",
              active && "text-blue-500 font-medium",
            )}
          >
            {t.label}
            {active && <div className="absolute bottom-0 left-0 w-full border border-b border-blue-500" />}
          </button>
        );
      })}
      <button
        className="ml-auto"
        onClick={() => {
          router.push(pathname + `?tab=settings`)
          setTab("settings")}
        }
      >
        <FiSettings />
      </button>
    </div>
  );
};

export default Tabs;