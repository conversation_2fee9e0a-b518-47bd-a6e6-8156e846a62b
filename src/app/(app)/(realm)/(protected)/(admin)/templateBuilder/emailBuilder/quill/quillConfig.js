let Quill;
if (typeof window !== 'undefined') {
  Quill = require('react-quill').Quill;

  // Add sizes to whitelist and register them
  const Size = Quill.import("formats/size");
  Size.whitelist = ["extra-small", "small", "medium", "large"];
  Quill.register(Size, true);

  // Add fonts to whitelist and register them
  const Font = Quill.import("formats/font");
  Font.whitelist = [
    "arial",
    "comic-sans",
    "courier-new",
    "georgia",
    "helvetica",
    "lucida"
  ];
  Quill.register(Font, true);
}


export const modules = {
  toolbar: {
    container: "#toolbar",
    handlers: {
     undo: (value) => {
        const quill = value.quill || value;
        if (quill && quill.history) {
          quill.history.undo();
        }
      },
      redo: (value) => {
        const quill = value.quill || value;
        if (quill && quill.history) {
          quill.history.redo();
        }
      }
    }
  },
  history: {
    delay: 500,
    maxStack: 100,
    userOnly: true
  }
};

export const formats = [
  "header",
  "font",
  "size",
  "bold",
  "italic",
  "underline",
  "align",
  "strike",
  "script",
  "blockquote",
  "background",
  "list",
  "bullet",
  "indent",
  "link",
  "image",
  "color",
  "code-block"
];
