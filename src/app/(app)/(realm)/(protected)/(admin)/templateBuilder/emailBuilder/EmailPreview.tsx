import React, { useEffect, useState } from "react";
import Footer from "./Footer";
import Header from "./Header";

type Sizes = {
  [key: string]: string;
};

const sizes: Sizes = {
  mobile: "w-[320px]",
  tablet: "w-[480px]",
  laptop: "w-[640px]",
  desktop: "w-[1024px]",
  largeDesktop: "w-[1280px]",
};

const EmailPreview = ({
  body,
  variables,
  showVariables = true,
}: {
  body: string;
  variables: any;
  showVariables?: boolean;
}) => {
  const renderBody = () => {
    if (showVariables) return body;

    let renderedBody = body;
    for (let key in variables) {
      const regex = new RegExp(`{${key}}`, "g");
      renderedBody = renderedBody.replace(regex, variables[key]);
    }

    return renderedBody;
  };

  const [renderedBody, setRenderedBody] = useState(renderBody());
  const [selectedSize, setSelectedSize] = useState<string>("full");

  useEffect(() => {
    setRenderedBody(renderBody());
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [body, showVariables]);

  return (
    <div 
      className="
        flex flex-col w-full h-full rounded gap-4  items-center
        overflow-hidden
      "
    >
      <div className="flex gap-2 w-full justify-center">
        {Object.keys(sizes).map((key, i) => (
          <button
            key={i}
            className={`${
              selectedSize === key ? "bg-slate-700" : "bg-slate-500"
            } text-white rounded p-2`}
            onClick={() => setSelectedSize(key)}
          >
            {key}
          </button>
        ))}
      </div>
      <div className={`${sizes[selectedSize]} overflow-auto max-w-full`}>
        <Header />
        <div
          style={{
            padding: "60px 20px",
          }}
          dangerouslySetInnerHTML={{ __html: renderedBody }}
        />
        <Footer />
      </div>
    </div>
  );
};

export default EmailPreview;
