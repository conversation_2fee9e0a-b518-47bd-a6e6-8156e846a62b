import React, { useEffect, useState } from 'react'
import ReactDOMServer from 'react-dom/server';
import EmailPreview from '../EmailPreview';
import Toggle from '@/components/ui/buttons/Toggle';



const DownloadEmail = ({
  body,
  variables,
  templateName,
  showVariables = true,
  setShowVariables
}: {
  body: string;
  variables: any;
  templateName: string;
  showVariables?: boolean;
  setShowVariables: (showVariables: boolean) => void;
}) => {

  const getComponentAsHTML = () => {
    return ReactDOMServer.renderToString(
      <EmailPreview body={body} variables={variables} showVariables={showVariables} />
    );
  };
  
  const handleDownload = () => {

    const componentHTML = `<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
      <html xmlns="http://www.w3.org/1999/xhtml">

        <head>
          <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
          <meta http-equiv="X-UA-Compatible" content="IE=edge" />
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>${templateName}</title>
          <style>
            p {
                margin: 0;
            }
          </style>
        </head>

        <body 
          style="max-width: 600px; margin: 0 auto; font-family: Arial, Helvetica, sans-serif;"
        >
          ${getComponentAsHTML()}
        </body>
      </html>
    `;
  
    const blob = new Blob([componentHTML], { type: "text/html" });
    const url = URL.createObjectURL(blob);
  
    const a = document.createElement("a");
    a.href = url;
    a.download = `${templateName || "email_template"}.html`;
    a.click();
  
    URL.revokeObjectURL(url); // Free up memory
  };

  return (
    <div className="flex justify-between gap-2">
    <button className="bg-neutral-200 rounded w-fit px-4 py-2 text-sm">
        Clear
      </button>
      <div className='flex items-center'>
        <Toggle
          label="Show Variables"
          state={showVariables}
          setState={setShowVariables}
        />
      </div>
      <button
        className="bg-blue-400 rounded w-fit px-4 py-2 text-sm"
        onClick={handleDownload}
      >
        Download
      </button>
    </div>
  )
}

export default DownloadEmail