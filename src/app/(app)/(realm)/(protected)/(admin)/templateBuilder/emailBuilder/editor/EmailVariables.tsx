import Modal from '@/components/modal/Modal'
import React, { useState } from 'react'

type Sizes = {
  [key: string]: string;
};

const EmailVariables = ({
  variables,
  setVariables
}:{
  variables: Sizes,
  setVariables: (value: Sizes) => void
}) => {
  const [isOpen, setIsOpen] = useState(false)
  return (
    <>
    <button
      className='bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700'
    >
      {isOpen ? 'Close' : 'Variables'}
    </button>
    <Modal
      isOpen={isOpen}
      onClose={()=>{setIsOpen(false)}}
      title='Variables'
    >
      {/* <textarea
        value={variables}
        onChange={(e) => setVariables(e.target.value)}
      /> */}
      <div>coming soon</div>
    </Modal>
    </>
  )
}

export default EmailVariables