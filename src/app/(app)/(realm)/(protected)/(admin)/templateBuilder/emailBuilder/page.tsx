"use client";
import React, { useEffect, useState } from "react";
import EmailTemplateEditor from "./editor/EmailTemplate";
import EmailPreview from "./EmailPreview";
import DownloadEmail from "./editor/DownloadEmail";
import TemplateName from "./editor/TemplateName";
import EmailVariables from "./editor/EmailVariables";
import TabsBar from "@/components/ui/Tabs/TabsBar";

type Sizes = {
  [key: string]: string;
};

const starterVariables: Sizes = {
  OwnerFirstName: "John",
  OwnerLastName: "Doe",
  LicenseType: "Dog License",
  DogName: "Fido",
  LicenseNo: "123456356",
  LicenseExpirationDate: "11/20/2024",
  LicenseIssueDate: "11/20/2023",
  ClerkEmail: "<EMAIL>",
  ClerkPhoneNumber: "+****************",
  ClerkSignature: "Your Signature",
  ClerkName: "<PERSON>",
  ClerkTitle: "City Clerk",
  CityClerkOfficeName: "City Clerk’s Office",
};


const EmailBuilder = () => {
  const [templateName, setTemplateName] = useState<string>("");
  const [showVariables, setShowVariables] = useState(false);
  const [body, setBody] = useState<string>("");
  const [variables, setVariables] = useState<Sizes>(starterVariables);
  const [activeTab, setActiveTab] = useState<string | null>('editor');

  const options:{
    section: string;
    value: string;
  }[] = [
    {
      section: 'Editor',
      value: 'editor'
    },
    {
      section: 'Preview',
      value: 'preview'
    }
  ]

  useEffect(() => {
    if (typeof window !== "undefined") {
      const savedBody = localStorage.getItem("emailBody");
      if (savedBody) {
        setBody(savedBody);
      }
    }
  }, []);

  useEffect(() => {
    localStorage.setItem("emailBody", body);
  }, [body]);


  return (
    <div className="flex flex-col gap-10 p-6 h-full overflow-hidden">
      {/* Tabs Bar */}
      <TabsBar
        options={options}
        activeRadio={activeTab}
        setActiveRadio={setActiveTab}
        buttonLabel='Download Email'
        buttonOnClick={() => {
          console.log('download email')
        }}
      />
      <div className="flex h-full gap-10 overflow-auto">
        { activeTab === 'editor' &&
          <div className="w-full h-full border rounded p-2 max-w-xl flex flex-col gap-6 overflow-auto">
            <TemplateName
              templateName={templateName}
              setTemplateName={setTemplateName}
            />
            <EmailVariables 
              variables={variables}
              setVariables={setVariables}
            />
            <EmailTemplateEditor 
              body={body} 
              setBody={setBody} 
            />
            <DownloadEmail
              templateName={templateName}
              variables={variables}
              body={body}
              showVariables={showVariables}
              setShowVariables={setShowVariables}
            />
          </div>
        }

        { activeTab === 'preview' &&
          <EmailPreview 
            variables={variables} 
            body={body} 
            showVariables={showVariables}
          />
        }
      </div>
    </div>
  );
};

export default EmailBuilder;
