"use client";

import React, { useState } from "react";
import { useForm<PERSON>ontext, useWatch } from "react-hook-form";
import { Card, CardContent } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Trash, Pencil } from "lucide-react";
import { useFeeContext } from "../FeeContext";
import {
  Command,
  CommandDialog,
  CommandGroup,
  CommandInput,
  CommandItem,
} from "@/components/ui/command";
import { ScrollArea } from "@/components/ui/scroll-area";
import FeeSetFeeForm from "./FeeSetFeeForm";
import { EditFeeCodeProp } from "@/components/fees/modal/FeeTypes";

export default function FeeCodesSection() {
  const { control, setValue } = useFormContext();
  const { feeCodes } = useFeeContext();

  const assignedCodes =
    (useWatch({ name: "feeCodes", control }) as EditFeeCodeProp[]) || [];

  const [open, setOpen] = useState(false);
  const [searchFilter, setSearchFilter] = useState("");
  const [editFee, setEditFee] = useState<EditFeeCodeProp | null>(null);

  const unassignedCodes:EditFeeCodeProp[] = feeCodes.filter(
    (code) => !assignedCodes.some((assigned) => assigned.key === code.key),
  );

  // Tokenized search filtering
  const filteredUnassignedCodes = unassignedCodes.filter((fee) => {
    if (!searchFilter) return true;

    const searchLower = searchFilter.toLowerCase().trim();
    const tokens = searchLower.split(" ").filter((token) => token !== "");

    if (tokens.length === 0) return true;

    const keyWords = fee.key?.toLowerCase() ?? "";
    const feeNameWords = fee.feeName?.toLowerCase() ?? "";
    const haystack = `${keyWords} ${feeNameWords}`;

    return tokens.every((token) => haystack.includes(token));
  });

  // Group them for display
  const groupedFees = filteredUnassignedCodes.reduce<
    Record<string, EditFeeCodeProp[]>
  >((acc, fee) => {
    if (!fee.key) return acc;
    const groupKey = fee.key.includes("-") ? fee.key.split("-")[0] : fee.key;
    if (!acc[groupKey]) acc[groupKey] = [];
    acc[groupKey].push(fee);
    return acc;
  }, {});

  const sortedGroupKeys = Object.keys(groupedFees).sort();

  // Add code to assignedCodes
  function handleAddCode(codeKey: string) {
    const feeObj = feeCodes.find((fee) => fee.key === codeKey)
    if (!feeObj) return;
    // Push the full object, not just the key
    setValue("feeCodes", [...assignedCodes, feeObj]);
    setOpen(false);
  }

  // Remove code from assignedCodes
  function handleRemoveCode(codeKey: string) {
    setValue(
      "feeCodes",
      assignedCodes.filter((fee) => fee.key !== codeKey),
    );
  }

  // Start editing a code (pull it from assignedCodes, not the global list)
  function handleEditCode(codeKey: string) {
    const fee = assignedCodes.find((fc) => fc.key === codeKey);
    if (fee) {
      setEditFee(fee);
    }
  }

  // Save the edited fee in assignedCodes
  function handleSaveEditedFee(updatedFee: EditFeeCodeProp) {
    console.log(updatedFee);
    setValue(
      "feeCodes",
      assignedCodes.map((fee) =>
        fee.key === updatedFee.key ? updatedFee : fee,
      ),
    );
    setEditFee(null);
  }

  // Utility to convert a number to USD currency
  const convertToDollars = (amount: number) =>
    amount.toLocaleString("en-US", {
      style: "currency",
      currency: "USD",
    });

  return (
    <Card>
      <CardContent className="space-y-4 pt-4">
        <div className="flex flex-col gap-2">
          <Label className="text-sm font-semibold">
            Fee Codes in this Group
          </Label>

          {/* Assigned Fee Codes List */}
          {assignedCodes.length > 0 ? (
            <ul className="space-y-2">
              {assignedCodes.map((feeObj: EditFeeCodeProp) => (
                <li
                  key={feeObj.key}
                  className="bg-background hover:bg-muted flex items-center justify-between rounded-lg border p-3 shadow-sm transition-all duration-200 ease-in-out"
                >
                  <div className="flex flex-col gap-1">
                    <p className="text-primary font-semibold">
                      {feeObj.feeName}
                    </p>
                    <p className="text-muted-foreground text-xs italic">
                      {feeObj.key}
                    </p>
                    {feeObj.amount != null && (
                      <p className="text-foreground text-sm">
                        Amount: {convertToDollars(feeObj.amount)}
                      </p>
                    )}
                    {feeObj.recurringAmount != null && (
                      <p className="text-foreground text-sm">
                        Recurring: {convertToDollars(feeObj.recurringAmount)}
                      </p>
                    )}
                  </div>

                  <div className="flex items-center gap-2">
                    <Button
                      variant="ghost"
                      size="icon"
                      type="button"
                      onClick={() => handleEditCode(feeObj.key)}
                      className="hover:bg-blue-100 dark:hover:bg-blue-900"
                    >
                      <Pencil className="h-5 w-5 text-blue-500" />
                    </Button>
                    <Button
                      type="button"
                      variant="ghost"
                      size="icon"
                      onClick={() => handleRemoveCode(feeObj.key)}
                      className="hover:bg-red-100 dark:hover:bg-red-900"
                    >
                      <Trash className="h-5 w-5 text-red-500" />
                    </Button>
                  </div>
                </li>
              ))}
            </ul>
          ) : (
            <p className="text-muted-foreground text-sm">
              No fee codes assigned yet.
            </p>
          )}

          {/* Add Fee Code Button */}
          <Button type="button" variant="outline" onClick={() => setOpen(true)}>
            Add Fee Code
          </Button>

          {/* Command Dropdown for Fee Code Selection */}
          <CommandDialog open={open} onOpenChange={setOpen}>
            <Command>
              <CommandInput
                placeholder="Search fee codes..."
                value={searchFilter}
                onValueChange={setSearchFilter}
              />
              <ScrollArea className="max-h-72 overflow-auto">
                {sortedGroupKeys.length > 0 ? (
                  sortedGroupKeys.map((group) => (
                    <CommandGroup key={group} heading={group}>
                      {groupedFees[group]?.length > 0 ? (
                        groupedFees[group].map((code) => (
                          <CommandItem
                            key={code.key}
                            value={`${code.key} ${code.feeName}`}
                            onSelect={() => handleAddCode(code.key)}
                            className="flex w-full cursor-pointer flex-col items-start py-2"
                          >
                            <p>{code.feeName}</p>
                            <p className="text-xs italic text-neutral-700">
                              {code.key}
                            </p>
                          </CommandItem>
                        ))
                      ) : (
                        <p className="text-muted-foreground p-2 text-center">
                          No items found in this group.
                        </p>
                      )}
                    </CommandGroup>
                  ))
                ) : (
                  <p className="text-muted-foreground p-2 text-center">
                    No matching results.
                  </p>
                )}
              </ScrollArea>
            </Command>
          </CommandDialog>
        </div>
      </CardContent>

      {/* Edit Fee Form */}
      {editFee && (
        <FeeSetFeeForm
          fee={editFee}
          open={!!editFee}
          onOpenChange={(dialogOpen) => {
            if (!dialogOpen) {
              setEditFee(null);
            }
          }}
          onSubmit={handleSaveEditedFee}
          onCancel={() => setEditFee(null)}
        />
      )}
    </Card>
  );
}
