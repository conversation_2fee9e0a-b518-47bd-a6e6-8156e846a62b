"use client";
import Loading from "@/app/(app)/loading";
import { useFeeContext, FeeCodeFinal } from "./FeeContext";
import { FeeForm } from "./FeeForm";
import FeeSidebar from "./FeeSidebar";

export default function FeeManager() {
  const { updateFeeCode, selectedFee, setSelectedFee, isLoadingFeeCodes } =
    useFeeContext();

  function handleFeeSubmit(fee: FeeCodeFinal) {
    updateFeeCode(fee);
    setSelectedFee(null);
  }

  if (isLoadingFeeCodes) {
    return (
      <div className="flex h-full w-full items-center justify-center">
        <Loading fixed={false} />
      </div>
    );
  }

  return (
    <div className="flex h-full overflow-hidden">
      <FeeSidebar />
      <FeeForm
        fee={selectedFee}
        onSubmit={handleFeeSubmit}
        onCancel={() => setSelectedFee(null)}
      />
    </div>
  );
}
