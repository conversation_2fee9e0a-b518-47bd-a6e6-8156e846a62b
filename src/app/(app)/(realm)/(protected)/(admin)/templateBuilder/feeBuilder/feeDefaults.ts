// feeDefaults.ts
import { addDays, addWeeks, addMonths, addYears } from "date-fns";
import { EditFeeCodeProp } from "@/components/fees/modal/FeeTypes";

export type OffsetUnit = "days" | "weeks" | "months" | "years";

export interface DatePropInput {
  type: "relative" | "fixed";
  date: string;
  offset: number;
  offsetBy: "days" | "weeks" | "months";
}

export interface FeeFormDefaults extends EditFeeCodeProp {
  // Define any additional form-only fields here if needed.
}

export const defaultFeeFormDefaults: FeeFormDefaults = {
  key: "",
  feeName: "",
  amount: 0,
  operation: "MANUAL",
  isRecurring: false,
  recurringAmount: 0,
  startDate: {
    type: "relative",
    date: "today",
    offset: 0,
    offsetBy: "days",
  },
  endDate: {
    type: "relative",
    date: "today",
    offset: 0,
    offsetBy: "days",
  },
  recurrence: {
    recurrenceType: "weekly",
    customCron: "",
    dayOfMonth: undefined,
    monthOfYear: undefined,
    selectedDays: [],
    time: {
      hours: 0,
      minutes: 0,
      seconds: 0,
    },
  },
  entityTypes: [],
};

// Export the offset functions if needed
export const offsetFunctions: Record<OffsetUnit, (date: Date, amount: number) => Date> = {
  days: addDays,
  weeks: addWeeks,
  months: addMonths,
  years: addYears,
};
