// useOffsetExample.ts
import { format } from "date-fns";
import { offsetFunctions, OffsetUnit } from "./feeDefaults";

interface OffsetParams {
  baseDate: Date;
  offset: number;
  unit: OffsetUnit;
}

interface UseOffsetExampleParams {
  dateType: "relative" | "fixed";
  offset: number;
  offsetBy: OffsetUnit;
  // for fixed dates, you can pass in a function that returns the current date value from the form
  getDateValue: () => string;
}

export const useOffsetExample = ({
  dateType,
  offset,
  offsetBy,
  getDateValue,
}: UseOffsetExampleParams) => {
  const toLocalDate = (dateString: string): Date => {
    const [year, month, day] = dateString.split("-").map(Number);
    return new Date(year, month - 1, day);
  };

  const getOffsetParams = (): OffsetParams | null => {
    if (dateType === "relative") {
      return {
        baseDate: new Date(),
        offset,
        unit: offsetBy,
      };
    } else {
      const baseDate = toLocalDate(getDateValue());
      return {
        baseDate,
        offset,
        unit: offsetBy,
      };
    }
  };

  const exampleOffset = (): string | null => {
    const params = getOffsetParams();
    if (!params) return null;
    const { baseDate, offset, unit } = params;
    const addFn = offsetFunctions[unit];
    if (!addFn) return null;
    const offsetDate = addFn(baseDate, offset);
    return isNaN(offsetDate.getTime()) ? null : format(offsetDate, "MM/dd/yyyy");
  };

  return { exampleOffset };
};
