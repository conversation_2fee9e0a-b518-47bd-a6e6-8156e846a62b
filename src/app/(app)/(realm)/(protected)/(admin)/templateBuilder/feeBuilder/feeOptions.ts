export interface FeeOptionsType {
  value: string;
  label: string;
}

export const relativeOptions: FeeOptionsType[] = [
  {
    value: "relative",
    label: "Starting From Today",
  },
  {
    value: "fixed",
    label: "Exact Date",
  },
]  

export const relativeDateOptions: FeeOptionsType[] = [
  {
    value: "today",
    label: "Today",
  }
];

export const offsetByOptions: FeeOptionsType[] = [
  {
    value: "days",
    label: "Days",
  },
  {
    value: "weeks",
    label: "Weeks",
  },
  {
    value: "months",
    label: "Months",
  },
];

export const operationOptions: FeeOptionsType[] = [
  {
    value: "MANUAL",
    label: "MANUAL",
  },
  {
    value: "FLAT",
    label: "FLAT",
  },
  {
    value: "PERCENTAGE",
    label: "PERCENTAGE",
  },
];

export const entityTypeOptions: FeeOptionsType[] = [
  {
    value: "dog",
    label: "Dog",
  },
  {
    value: "individual",
    label: "Individual",
  },
  {
    value: "address",
    label: "Address",
  },
  {
    value: "organization",
    label: "Organization",
  },
  {
    value: "license",
    label: "License",
  },
  {
    value: "permit",
    label: "Permit",
  },
];

export const recurrenceTypeOptions: FeeOptionsType[] = [
  {
    value: "daily",
    label: "Daily",
  },
  {
    value: "weekly",
    label: "Weekly",
  },
  {
    value: "monthly",
    label: "Monthly",
  },
  {
    value: "yearly",
    label: "Yearly",
  },
  {
    value: "custom",
    label: "Custom Cron Expression",
  },
];

export const monthOptions: FeeOptionsType[] = [
  {
    value: "1",
    label: "January",
  },
  {
    value: "2",
    label: "February",
  },
  {
    value: "3",
    label: "March",
  },
  {
    value: "4",
    label: "April",
  },
  {
    value: "5",
    label: "May",
  },
  {
    value: "6",
    label: "June",
  },
  {
    value: "7",
    label: "July",
  },
  {
    value: "8",
    label: "August",
  },
  {
    value: "9",
    label: "September",
  },
  {
    value: "10",
    label: "October",
  },
  {
    value: "11",
    label: "November",
  },
  {
    value: "12",
    label: "December",
  },
];

export const dayOptions: FeeOptionsType[] = [
  { label: "Sun", value: "Sun" },
  { label: "Mon", value: "Mon" },
  { label: "Tue", value: "Tue" },
  { label: "Wed", value: "Wed" },
  { label: "Thu", value: "Thu" },
  { label: "Fri", value: "Fri" },
  { label: "Sat", value: "Sat" },
]