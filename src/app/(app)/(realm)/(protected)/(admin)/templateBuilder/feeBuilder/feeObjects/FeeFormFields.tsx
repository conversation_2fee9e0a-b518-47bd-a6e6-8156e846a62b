"use client";

import { Controller, useFormContext } from "react-hook-form";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
} from "@/components/ui/select";
import { ToggleGroup, ToggleGroupItem } from "@/components/ui/toggle-group";
import { cn } from "@/lib/utils";
import { FeeOptionsType } from "../feeOptions";
import { Switch } from "@/components/ui/switch";

interface FormFieldProps {
  id: string;
  label: string;
  type?: string;
  placeholder: string;
  requiredMessage: string;
  isDisabled?: boolean;
}

const Row = ({ children }: { children: React.ReactNode }) => (
  <div className="flex flex-col gap-1">{children}</div>
);

export function getNestedValue(obj: any, path: string): any {
  return path.split('.').reduce((acc, key) => acc?.[key], obj);
}

const ErrorMessage = ({
  errors,
  id,
}: {
  errors: Record<string, any>;
  id: string;
}) => {
  const error = getNestedValue(errors, id);
  if (!error) return null;

  return (
    <p className="text-sm text-red-500">{error.message as string}</p>
  );
};

export const FormField = ({
  id,
  label,
  type = "text",
  placeholder,
  requiredMessage,
  isDisabled = false,
}: FormFieldProps) => {
  const {
    register,
    formState: { errors },
  } = useFormContext();

  return (
    <Row>
      <Label htmlFor={id}>{label}</Label>
      <Input
        id={id}
        type={type}
        {...register(id, {
          required: requiredMessage,
          ...(type === "number" && { valueAsNumber: true }),
        })}
        className="max-w-lg"
        placeholder={placeholder}
        disabled={isDisabled}
      />
      <ErrorMessage errors={errors} id={id} />
    </Row>
  );
};

export const SelectField = ({
  id,
  label,
  options,
  requiredMessage,
}: {
  id: string;
  label: string;
  options: FeeOptionsType[];
  requiredMessage: string;
}) => {
  const {
    control,
    formState: { errors },
  } = useFormContext();

  return (
    <Row>
      <Label htmlFor={id}>{label}</Label>
      <Controller
        name={id}
        control={control}
        rules={{ required: requiredMessage }}
        render={({ field }) => (
          <Select onValueChange={field.onChange} value={field.value}>
            <SelectTrigger className="max-w-lg">
              {options.find((opt) => opt.value === field.value)?.label ||
                `Select ${label}`}
            </SelectTrigger>
            <SelectContent>
              {options.map((opt) => (
                <SelectItem key={opt.value} value={opt.value}>
                  {opt.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        )}
      />
      <ErrorMessage errors={errors} id={id} />
    </Row>
  );
};

export const ToggleGroupField = ({
  id,
  label,
  options,
  requiredMessage,
}: {
  id: string;
  label: string;
  options: FeeOptionsType[];
  requiredMessage: string;
}) => {
  const {
    control,
    formState: { errors },
  } = useFormContext();

  return (
    <Row>
      <Label>{label}</Label>
      <Controller
        control={control}
        name={id}
        rules={{
          validate: (value) => (value && value.length > 0) || requiredMessage,
        }}
        render={({ field: { value, onChange } }) => (
          <ToggleGroup
            type="multiple"
            value={value || []}
            onValueChange={onChange}
            className="justify-start px-0 flex flex-row gap-2"
          >
            {options.map((option) => (
              <ToggleGroupItem
                key={option.value}
                value={option.value}
                aria-label={option.label}
                className={cn(
                  "w-fit rounded border p-2",
                  value && value.includes(option.value)
                    ? "!bg-blue-600 !text-white"
                    : "bg-gray-200 text-gray-700",
                )}
              >
                {option.label}
              </ToggleGroupItem>
            ))}
          </ToggleGroup>
        )}
      />
      <ErrorMessage errors={errors} id={id} />
    </Row>
  );
};

export const SwitchField = ({ id, label }: { id: string; label: string }) => {
  const {
    control,
    formState: { errors },
  } = useFormContext();

  return (
    <Row>
      <div className="flex items-center gap-4">
        <Controller
          control={control}
          name={id}
          render={({ field }) => (
            <Switch
              id={id}
              checked={field.value}
              onCheckedChange={field.onChange}
            />
          )}
        />
        <Label htmlFor={id}>{label}</Label>
      </div>
      <ErrorMessage errors={errors} id={id} />
    </Row>
  );
};
