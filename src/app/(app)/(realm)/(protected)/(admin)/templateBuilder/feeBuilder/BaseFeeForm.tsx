// BaseFeeForm.tsx
"use client";

import { useEffect, useState } from "react";
import { useForm, FormProvider } from "react-hook-form";
import { defaultFeeFormDefaults, FeeFormDefaults } from "./feeDefaults";
import { But<PERSON> } from "@/components/ui/button";
import { FormField, SelectField, SwitchField, ToggleGroupField } from "./feeObjects/FeeFormFields";
import { FeeTabs } from "./FeeTabs";
import {
  offsetByOptions,
  relativeDateOptions,
  entityTypeOptions,
  recurrenceTypeOptions,
  operationOptions,
  monthOptions,
  relativeOptions,
} from "./feeOptions";
import { addDays, addWeeks, addMonths, addYears, format } from "date-fns";

interface BaseFeeFormProps {
  fee?: FeeFormDefaults | null;
  onSubmit: (fee: FeeFormDefaults) => void;
  onCancel?: () => void;
  footerActions?: React.ReactNode;
  containerClassName?: string;
}

export function BaseFeeForm({ fee, onSubmit, onCancel, footerActions, containerClassName }: BaseFeeFormProps) {
  const [hasEndDate, setHasEndDate] = useState(false);

  const methods = useForm<FeeFormDefaults>({
    defaultValues: fee || defaultFeeFormDefaults,
  });

  const isRecurring = methods.watch("isRecurring");
  const recurrenceType = methods.watch("recurrence.recurrenceType");
  const startDateType = methods.watch("startDate.type");
  const endDateType = methods.watch("endDate.type");
  const startDateOffsetBy = methods.watch("startDate.offsetBy");
  const endDateOffsetBy = methods.watch("endDate.offsetBy");
  const startDateOffset = methods.watch("startDate.offset");
  const endDateOffset = methods.watch("endDate.offset");

  // Calculate example offset date using inline logic.
  const toLocalDate = (dateString: string): Date => {
    const [year, month, day] = dateString.split("-").map(Number);
    return new Date(year, month - 1, day);
  };

  const offsetFunctions = {
    days: addDays,
    weeks: addWeeks,
    months: addMonths,
    years: addYears,
  };

  const getOffsetParams = (type: "start" | "end") => {
    if (type === "start") {
      if (startDateType === "relative") {
        return {
          baseDate: new Date(),
          offset: startDateOffset,
          unit: startDateOffsetBy,
        };
      } else {
        const baseDate = toLocalDate(methods.watch("startDate.date"));
        return { baseDate, offset: startDateOffset, unit: startDateOffsetBy };
      }
    } else {
      if (endDateType === "relative") {
        return { baseDate: new Date(), offset: endDateOffset, unit: endDateOffsetBy };
      } else {
        const baseDate = toLocalDate(methods.watch("endDate.date"));
        return { baseDate, offset: endDateOffset, unit: endDateOffsetBy };
      }
    }
  };

  const exampleOffset = (type: "start" | "end"): string | null => {
    const params = getOffsetParams(type);
    if (!params) return null;
    const { baseDate, offset, unit } = params;
    const addFn = offsetFunctions[unit];
    const offsetDate = addFn(baseDate, offset);
    return isNaN(offsetDate.getTime()) ? null : format(offsetDate, "MM/dd/yyyy");
  };

  useEffect(() => {
    const safeDefaults = {
      ...defaultFeeFormDefaults,
      ...fee,
      startDate: { ...defaultFeeFormDefaults.startDate, ...(fee?.startDate || {}) },
      endDate: { ...defaultFeeFormDefaults.endDate, ...(fee?.endDate || {}) },
      recurrence: { ...defaultFeeFormDefaults.recurrence, ...(fee?.recurrence || {}) },
    };

    methods.reset(safeDefaults);
    setHasEndDate(!!(fee && fee.endDate));
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [fee, methods.reset]);

  const onFormSubmit = (data: FeeFormDefaults) => {
    // Build the final fee data (adjust as needed).
    const finalFee: FeeFormDefaults = {
      key: data.key,
      feeName: data.feeName,
      amount: data.amount,
      operation: data.operation,
      entityTypes: data.entityTypes,
      ...((data.isRecurring && {
        isRecurring: data.isRecurring,
        recurringAmount: data.recurringAmount,
        startDate: data.startDate,
        ...(hasEndDate && { endDate: data.endDate }),
        ...(data.occurrenceCount !== undefined && { occurrenceCount: data.occurrenceCount }),
        recurrence: data.recurrence,
      }) ||
        {}),
    };
    onSubmit(finalFee);
  };

  // Render the main form layout with the FeeTabs component.
  const detailsTab = (
    <>
      <FormField id="key" label="Fee Code" placeholder="Enter fee code key" requiredMessage="Fee Code is required" isDisabled={!!fee} />
      <FormField id="feeName" label="Fee Name" placeholder="Enter fee name" requiredMessage="Fee Name is required" />
      <FormField
        id="amount"
        label={methods.watch("operation") === "PERCENTAGE" ? "Percentage" : "Amount"}
        type="number"
        placeholder="Enter fee amount"
        requiredMessage="Amount is required"
      />
      <SelectField id="operation" label="Operation" options={operationOptions} requiredMessage="Operation is required" />
      <ToggleGroupField id="entityTypes" label="Entity Types" options={entityTypeOptions} requiredMessage="At least one entity type must be selected" />
    </>
  );

  const recurringTab = (
    <>
      <SwitchField id="isRecurring" label="Has Recurring Fee" />
      {isRecurring && (
        <>
          <h3 className="font-semibold">Recurring Information</h3>
          <div className="space-y-4 rounded border border-blue-300 p-4">
            <FormField id="recurringAmount" label="Recurring Amount" type="number" placeholder="Enter recurring amount" requiredMessage="Recurring amount is required" />
            <SelectField id="recurrence.recurrenceType" label="Recurrence Type" options={recurrenceTypeOptions} requiredMessage="Recurrence type is required" />

            {recurrenceType === "weekly" && (
              <ToggleGroupField
                id="recurrence.selectedDays"
                label="Selected Days"
                options={[
                  { label: "Sun", value: "Sun" },
                  { label: "Mon", value: "Mon" },
                  { label: "Tue", value: "Tue" },
                  { label: "Wed", value: "Wed" },
                  { label: "Thu", value: "Thu" },
                  { label: "Fri", value: "Fri" },
                  { label: "Sat", value: "Sat" },
                ]}
                requiredMessage="At least one day must be selected"
              />
            )}

            {(recurrenceType === "monthly" || recurrenceType === "yearly") && (
              <SelectField
                id="recurrence.dayOfMonth"
                label="Day of Month"
                options={Array.from({ length: 31 }, (_, i) => {
                  const day = String(i + 1);
                  return { label: day, value: day };
                })}
                requiredMessage="Day of month is required"
              />
            )}

            {recurrenceType === "yearly" && (
              <SelectField id="recurrence.monthOfYear" label="Month" options={monthOptions} requiredMessage="Month is required" />
            )}

            {recurrenceType === "custom" && (
              <FormField id="recurrence.customCron" label="Custom Cron Expression" placeholder="Enter custom cron expression" requiredMessage="Cron expression is required" />
            )}
          </div>

          {/* Start Date */}
          <div className="space-y-4 rounded border border-blue-300 p-4">
            <h3 className="font-semibold">Start Date</h3>
            <SelectField id="startDate.type" label="Type" options={relativeOptions} requiredMessage="Type is required" />
            {startDateType === "relative" ? (
              <SelectField id="startDate.date" label="Value" options={relativeDateOptions} requiredMessage="Relative date is required" />
            ) : (
              <FormField id="startDate.date" type="date" label="Value" placeholder="Select date" requiredMessage="Date is required" />
            )}
            <div className="flex items-center gap-2">
              <FormField id="startDate.offset" label="Offset" type="number" placeholder="Enter offset" requiredMessage="Offset is required" />
              <SelectField id="startDate.offsetBy" label="Offset By" options={offsetByOptions} requiredMessage="Offset by is required" />
            </div>
            <div className="text-sm">Example: {exampleOffset("start")}</div>
          </div>

          {/* End Date */}
          <div className="space-y-2">
            <div className="flex items-center gap-4">
              <input type="checkbox" id="hasEndDate" checked={hasEndDate} onChange={(e) => setHasEndDate(e.target.checked)} />
              <label htmlFor="hasEndDate">Has End Date</label>
            </div>
            {hasEndDate && (
              <div className="space-y-4 rounded border border-blue-300 p-4">
                <h3 className="font-semibold">End Date</h3>
                <SelectField id="endDate.type" label="Type" options={relativeOptions} requiredMessage="Type is required" />
                {endDateType === "relative" ? (
                  <SelectField id="endDate.date" label="Value" options={relativeDateOptions} requiredMessage="Relative date is required" />
                ) : (
                  <FormField id="endDate.date" type="date" label="Value" placeholder="Select date" requiredMessage="Date is required" />
                )}
                <FormField id="endDate.offset" label="Offset" type="number" placeholder="Enter offset" requiredMessage="Offset is required" />
                <SelectField id="endDate.offsetBy" label="Offset By" options={offsetByOptions} requiredMessage="Offset by is required" />
              </div>
            )}
          </div>
        </>
      )}
    </>
  );

  return (
    <FormProvider {...methods}>
      <form onSubmit={methods.handleSubmit(onFormSubmit)} className={`flex flex-col space-y-8 p-4 ${containerClassName || ""}`}>
        <h2 className="text-2xl font-bold">{fee ? "Edit Fee" : "Create New Fee"}</h2>
        <FeeTabs details={detailsTab} recurring={recurringTab} />
        <div className="flex items-center gap-2">
          {onCancel && (
            <Button type="button" onClick={onCancel} variant="outline">
              Cancel
            </Button>
          )}
          <Button disabled={methods.formState.isSubmitting} type="submit">
            {fee ? "Update Fee" : "Create Fee"}
          </Button>
          {footerActions}
        </div>
      </form>
    </FormProvider>
  );
}
