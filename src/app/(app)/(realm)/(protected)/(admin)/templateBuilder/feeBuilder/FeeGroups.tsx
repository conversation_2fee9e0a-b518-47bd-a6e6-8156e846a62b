import React from "react";
import { useFeeContext } from "./FeeContext";
import Loading from "@/app/(app)/loading";
import FeeSetSidebar from "./feeSet/FeeSetSidebar";
import FeeGroupManager from "./feeSet/FeeGroupManager";
import { useAtom } from "jotai";
import { toastAtom } from "@/components/ui/toast/toast";
import { EditFeeSetProp } from "@/components/fees/modal/FeeTypes";

export default function FeeSet() {
  const {
    feeSets,
    isLoadingFeeGroups,
    selectedFeeSet,
    setSelectedFeeSet,
    updateFeeSetMutation,
    refetchFeeGroups,
  } = useFeeContext();

  const [_, setToast] = useAtom(toastAtom);

  console.log(feeSets);

  function handleFeeSubmit(data: EditFeeSetProp) {
    // Check if there's already a fee set with the same groupName
    const existingFeeSetIndex = feeSets.findIndex(
      (feeSet) => feeSet.groupName === data.groupName,
    );

    if (existingFeeSetIndex !== -1) {
      // If found, replace the old fee set with the updated one
      const updatedFeeSets = [...feeSets];
      updatedFeeSets[existingFeeSetIndex] = data;
      // updateFeeSets(updatedFeeSets);
      updateFeeSetMutation.mutate(updatedFeeSets, {
        onSuccess: () => {
          refetchFeeGroups();
          setToast({
            label: "Fee Set",
            message: "Fee set created successfully",
            status: "success",
          });
        },
        onError: (error: any) => {
          console.error("Error updating fee set:", error);
          setToast({
            label: "Fee Set",
            message: "Error updating fee set",
            status: "error",
          });
        },
      });
    } else {
      // If not found, add the new fee set
      updateFeeSetMutation.mutate([...feeSets, data], {
        onSuccess: () => {
          refetchFeeGroups();
          setToast({
            label: "Fee Set",
            message: "Fee set created successfully",
            status: "success",
          });
        },
        onError: (error: any) => {
          console.error("Error updating fee set:", error);
          setToast({
            label: "Fee Set",
            message: "Error updating fee set",
            status: "error",
          });
        },
      });
    }

    // Reset any UI state if needed (removed for now --Sean B)
    // setSelectedFeeSet(null);
  }

  if (isLoadingFeeGroups) {
    return (
      <div className="flex h-full w-full items-center justify-center">
        <Loading fixed={false} />
      </div>
    );
  }

  return (
    <div className="flex h-full overflow-hidden">
      <FeeSetSidebar />
      <FeeGroupManager
        onSubmit={handleFeeSubmit}
        onCancel={() => setSelectedFeeSet(null)}
        selectedFeeSet={selectedFeeSet}
      />
    </div>
  );
}
