"use client";

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

import { useEffect, useState } from "react";
import { useForm, FormProvider } from "react-hook-form";
import { Label } from "@/components/ui/label";
import { Card, CardContent } from "@/components/ui/card";
import { Switch } from "@/components/ui/switch";

import {
  offsetByOptions,
  relativeDateOptions,
  entityTypeOptions,
  recurrenceTypeOptions,
  operationOptions,
  monthOptions,
  relativeOptions,
} from "../feeOptions";

import { FeeCodeFinal } from "../FeeContext";
import {
  FormField,
  SelectField,
  SwitchField,
  ToggleGroupField,
} from "../feeObjects/FeeFormFields";
import { XIcon } from "lucide-react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { EditFeeCodeProp } from "@/components/fees/modal/FeeTypes";
import { addDays, addMonths, addWeeks, addYears, format } from "date-fns";

export type DatePropInput = {
  type: "relative" | "fixed";
  date: string;
  offset: number;
  offsetBy: "days" | "weeks" | "months";
};

interface FeeFormProps {
  /** Fee to Edit (null if creating new) */
  fee?: EditFeeCodeProp | null;

  /** Whether the alert dialog is open or not */
  open: boolean;

  /**
   * Called when the dialog wants to change its open state
   * e.g. when the user closes the dialog
   */
  onOpenChange: (open: boolean) => void;

  /** Handler when user submits the fee form */
  onSubmit: (fee: EditFeeCodeProp) => void;

  /** Handler when user cancels (optional) */
  onCancel?: () => void;
}

export interface FeeFormDefaults extends EditFeeCodeProp {}

const defaultFeeFormDefaults: FeeFormDefaults = {
  key: "",
  feeName: "",
  amount: 0,
  operation: "MANUAL",
  isRecurring: false,
  recurringAmount: 0,
  startDate: {
    type: "relative",
    date: "today",
    offset: 0,
    offsetBy: "days",
  },
  endDate: {
    type: "relative",
    date: "today",
    offset: 0,
    offsetBy: "days",
  },
  recurrence: {
    recurrenceType: "weekly",
    customCron: "",
    dayOfMonth: undefined,
    monthOfYear: undefined,
    selectedDays: [],
    time: {
      hours: 0,
      minutes: 0,
      seconds: 0,
    },
  },
  entityTypes: [],
};

export default function FeeSetFeeForm({
  fee,
  open,
  onOpenChange,
  onSubmit,
  onCancel,
}: FeeFormProps) {
  const [hasEndDate, setHasEndDate] = useState(false);

  const methods = useForm<FeeFormDefaults>({
    defaultValues: fee || defaultFeeFormDefaults,
  });

  // Watch necessary fields – note the nested recurrence fields.
  const isRecurring = methods.watch("isRecurring");
  const recurrenceType = methods.watch("recurrence.recurrenceType");
  const startDateType = methods.watch("startDate.type");
  const endDateType = methods.watch("endDate.type");
  const startDateOffsetBy = methods.watch("startDate.offsetBy");
  const endDateOffsetBy = methods.watch("endDate.offsetBy");
  const startDateOffset = methods.watch("startDate.offset");
  const endDateOffset = methods.watch("endDate.offset");

  useEffect(() => {
    methods.reset(fee || defaultFeeFormDefaults);
    setHasEndDate(!!(fee && fee.endDate));
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [fee, methods.reset]);

  const onFormSubmit = (data: FeeFormDefaults) => {
    const finalFee: EditFeeCodeProp = {
      key: data.key,
      feeName: data.feeName,
      amount: data.amount,
      operation: data.operation,
      entityTypes: data.entityTypes,
    };

    if (data.isRecurring) {
      finalFee.recurringAmount = data.recurringAmount;
      finalFee.startDate = data.startDate;
      if (hasEndDate && data.endDate) {
        finalFee.endDate = data.endDate;
      }
      if (data.occurrenceCount !== undefined) {
        finalFee.occurrenceCount = data.occurrenceCount;
      }
      // Add the nested recurrence object only for recurring fees.
      if (data.recurrence) {
        finalFee.recurrence = {
          recurrenceType: data.recurrence.recurrenceType,
          customCron: data.recurrence.customCron,
          dayOfMonth: data.recurrence.dayOfMonth,
          monthOfYear: data.recurrence.monthOfYear,
          selectedDays: data.recurrence.selectedDays,
          time: data.recurrence.time,
        };
      }
    }

    onSubmit(finalFee);
  };

  type OffsetUnit = "days" | "weeks" | "months" | "years";
  
    interface OffsetParams {
      baseDate: Date;
      offset: number;
      unit: OffsetUnit;
    }
    
    const offsetFunctions: Record<OffsetUnit, (date: Date, amount: number) => Date> = {
      days: addDays,
      weeks: addWeeks,
      months: addMonths,
      years: addYears
    };
  
    const toLocalDate = (dateString: string): Date => {
      const [year, month, day] = dateString.split("-").map(Number);
      return new Date(year, month - 1, day);
    };
    
    const getOffsetParams = (type: "start" | "end"): OffsetParams | null => {
      if (type === "start") {
        if (startDateType === "relative") {
          return {
            baseDate: new Date(),
            offset: startDateOffset,
            unit: startDateOffsetBy, 
          };
        } else {
          const baseDate = toLocalDate(methods.watch("startDate.date"));
          return {
            baseDate,
            offset: startDateOffset, 
            unit: startDateOffsetBy,
          };
        }
      }
    
      if (type === "end") {
        if (endDateType === "relative") {
          return {
            baseDate: new Date(),
            offset: endDateOffset,
            unit: endDateOffsetBy,
          };
        } else {
          const baseDate = toLocalDate(methods.watch("endDate.date"));
          return {
            baseDate,
            offset: endDateOffset, 
            unit: endDateOffsetBy,
          };
        }
      }
      
      return null;
    };
    
    const exampleOffset = (type: "start" | "end"): string | null => {
      const params = getOffsetParams(type);
      if (!params) return null;
    
      const { baseDate, offset, unit } = params;
      const addFn = offsetFunctions[unit];
      if (!addFn) return null;
    
      const offsetDate = addFn(baseDate, offset);
      if (isNaN(offsetDate.getTime())) return null;
    
      return format(offsetDate, "MM/dd/yyyy");
    };

  

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent className="flex max-h-[90vh] w-full max-w-xl flex-col gap-2 overflow-hidden p-0">
        <AlertDialogHeader>
          <AlertDialogTitle className="flex w-full items-center justify-between p-4">
            {fee ? "Edit Fee" : "Create New Fee"}
            <button
              onClick={() => onOpenChange(false)}
              aria-label="Close Dialog"
              className="rounded-full p-2 hover:bg-gray-200"
            >
              <XIcon size={24} />
            </button>
          </AlertDialogTitle>
        </AlertDialogHeader>

        <FormProvider {...methods}>
          {/* Body scroll area */}
          <div className="flex h-full w-full flex-col space-y-8 overflow-y-auto overflow-x-hidden p-4">
            <Tabs defaultValue="details" className="">
              <TabsList className="grid w-full grid-cols-2 bg-white shadow">
                <TabsTrigger
                  value="details"
                  className="rounded data-[state=active]:bg-clerk-primary data-[state=active]:text-white"
                >
                  Details
                </TabsTrigger>
                <TabsTrigger
                  value="recurring"
                  className="rounded data-[state=active]:bg-clerk-primary data-[state=active]:text-white"
                >
                  Recurring
                </TabsTrigger>
              </TabsList>

              <TabsContent value="details">
                <Card>
                  <CardContent className="space-y-4 pt-4">
                    <FormField
                      id="key"
                      label="Fee Code"
                      placeholder="Enter fee code key"
                      requiredMessage="Fee Code is required"
                      isDisabled={!!fee}
                    />

                    <FormField
                      id="feeName"
                      label="Fee Name"
                      placeholder="Enter fee name"
                      requiredMessage="Fee Name is required"
                    />

                    <FormField
                      id="amount"
                      label="Amount"
                      type="number"
                      placeholder="Enter fee amount"
                      requiredMessage="Amount is required"
                    />

                    <SelectField
                      id="operation"
                      label="Operation"
                      options={operationOptions}
                      requiredMessage="Operation is required"
                    />

                    <ToggleGroupField
                      id="entityTypes"
                      label="Entity Types"
                      options={entityTypeOptions}
                      requiredMessage="At least one entity type must be selected"
                    />
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="recurring">
                <Card>
                  <CardContent className="flex flex-col pt-4">
                    <div className="w-full space-y-10">
                      <SwitchField id="isRecurring" label="Has Recurring Fee" />
                      {isRecurring && (
                        <>
                          <div className="w-full space-y-4">
                            <h3 className="font-semibold">
                              Recurring Information
                            </h3>
                            <div className="space-y-4 rounded border border-blue-300 p-4">
                              <FormField
                                id="recurringAmount"
                                label="Recurring Amount"
                                type="number"
                                placeholder="Enter recurring amount"
                                requiredMessage="Recurring amount is required"
                              />
                              <SelectField
                                id="recurrence.recurrenceType"
                                label="Recurrence Type"
                                options={recurrenceTypeOptions}
                                requiredMessage="Recurrence type is required"
                              />

                              {recurrenceType === "weekly" && (
                                <ToggleGroupField
                                  id="recurrence.selectedDays"
                                  label="Selected Days"
                                  options={[
                                    { label: "Sun", value: "Sun" },
                                    { label: "Mon", value: "Mon" },
                                    { label: "Tue", value: "Tue" },
                                    { label: "Wed", value: "Wed" },
                                    { label: "Thu", value: "Thu" },
                                    { label: "Fri", value: "Fri" },
                                    { label: "Sat", value: "Sat" },
                                  ]}
                                  requiredMessage="At least one day must be selected"
                                />
                              )}

                              {(recurrenceType === "monthly" ||
                                recurrenceType === "yearly") && (
                                <SelectField
                                  id="recurrence.dayOfMonth"
                                  label="Day of Month"
                                  options={Array.from(
                                    { length: 31 },
                                    (_, i) => {
                                      const day = String(i + 1);
                                      return { label: day, value: day };
                                    },
                                  )}
                                  requiredMessage="Day of month is required"
                                />
                              )}

                              {recurrenceType === "yearly" && (
                                <SelectField
                                  id="recurrence.monthOfYear"
                                  label="Month"
                                  options={monthOptions}
                                  requiredMessage="Month is required"
                                />
                              )}

                              {recurrenceType === "custom" && (
                                <FormField
                                  id="recurrence.customCron"
                                  label="Custom Cron Expression"
                                  placeholder="Enter custom cron expression"
                                  requiredMessage="Cron expression is required"
                                />
                              )}
                            </div>
                          </div>

                          {/* Start Date */}
                          <div className="w-full space-y-4">
                            <h3 className="font-semibold">Start Date</h3>
                            <div className="space-y-4 rounded border border-blue-300 p-4">
                              <SelectField
                                id="startDate.type"
                                label="Type"
                                options={relativeOptions}
                                requiredMessage="Type is required"
                              />

                              {startDateType === "relative" ? (
                                <SelectField
                                  id="startDate.date"
                                  label="Value"
                                  options={relativeDateOptions}
                                  requiredMessage="Relative date is required"
                                />
                              ) : (
                                <FormField
                                  id="startDate.date"
                                  type="date"
                                  label="Value"
                                  placeholder="Select date"
                                  requiredMessage="Date is required"
                                />
                              )}
                              <div className="flex items-center justify-center gap-2">
                                <FormField
                                  id="startDate.offset"
                                  label="Offset"
                                  type="number"
                                  placeholder="Enter offset"
                                  requiredMessage="Offset is required"
                                />

                                <SelectField
                                  id="startDate.offsetBy"
                                  label="Offset By"
                                  options={offsetByOptions}
                                  requiredMessage="Offset by is required"
                                />
                              </div>
                            </div>
                          </div>

                          <div className="w-full space-y-2">
                            <div className="flex items-center gap-4">
                              <Switch
                                id="hasEndDate"
                                checked={hasEndDate}
                                onCheckedChange={setHasEndDate}
                              />
                              <Label htmlFor="hasEndDate">Has End Date</Label>
                            </div>

                            {hasEndDate && (
                              <>
                                <h3 className="font-semibold">End Date</h3>
                                <div className="space-y-4 rounded border border-blue-300 p-4">
                                  <SelectField
                                    id="endDate.type"
                                    label="Type"
                                    options={relativeOptions}
                                    requiredMessage="Type is required"
                                  />

                                  {endDateType === "relative" ? (
                                    <SelectField
                                      id="endDate.date"
                                      label="Value"
                                      options={relativeDateOptions}
                                      requiredMessage="Relative date is required"
                                    />
                                  ) : (
                                    <FormField
                                      id="endDate.date"
                                      type="date"
                                      label="Value"
                                      placeholder="Select date"
                                      requiredMessage="Date is required"
                                    />
                                  )}

                                  <FormField
                                    id="endDate.offset"
                                    label="Offset"
                                    type="number"
                                    placeholder="Enter offset"
                                    requiredMessage="Offset is required"
                                  />

                                  <SelectField
                                    id="endDate.offsetBy"
                                    label="Offset By"
                                    options={offsetByOptions}
                                    requiredMessage="Offset by is required"
                                  />
                                </div>
                              </>
                            )}
                          </div>
                        </>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>

          {/* Footer: Actions */}
          <AlertDialogFooter className="flex items-center gap-2 p-4">
            {onCancel && (
              <AlertDialogCancel onClick={onCancel}>Cancel</AlertDialogCancel>
            )}
            <AlertDialogAction onClick={methods.handleSubmit(onFormSubmit)}>
              {fee ? "Update Fee" : "Create Fee"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </FormProvider>
      </AlertDialogContent>
    </AlertDialog>
  );
}
