"use client";

import React, { useState, useMemo } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useFeeContext } from "../FeeContext";

// ShadCN UI imports
import {
  Popover,
  PopoverTrigger,
  PopoverContent,
} from "@/components/ui/popover";
import {
  Command,
  CommandList,
  CommandItem,
  CommandInput,
} from "@/components/ui/command";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { ScrollArea } from "@/components/ui/scroll-area";

export default function FeeSetSidebar() {
  const { feeSets, setSelectedFeeSet, selectedFeeSet } = useFeeContext();

  // Text search state (for FeeSets)
  const [searchFilter, setSearchFilter] = useState("");

  // For multi-select dropdown
  const [isPopoverOpen, setIsPopoverOpen] = useState(false);

  // Selected entity types (for filtering FeeSets)
  const [selectedEntityTypes, setSelectedEntityTypes] = useState<string[]>([]);

  // 1) Gather all distinct entity types from feeSets
  const allEntityTypes = useMemo(() => {
    const set = new Set<string>();
    feeSets.forEach((fs) => {
      fs.entityTypes.forEach((et) => set.add(et));
    });
    return Array.from(set).sort();
  }, [feeSets]);

  // 2) Filter FeeSets by text search
  const textFilteredFeeSets = useMemo(() => {
    const lower = searchFilter.toLowerCase().trim();
    if (!lower) return feeSets;

    const tokens = lower.split(" ").filter(Boolean);

    return feeSets.filter((feeSet) => {
      const haystack = (feeSet.groupName + " " + feeSet.label).toLowerCase();
      return tokens.every((token) => haystack.includes(token));
    });
  }, [feeSets, searchFilter]);

  // 3) Filter by selected entity types (OR-logic)
  const finalFeeSets = useMemo(() => {
    if (selectedEntityTypes.length === 0) {
      return textFilteredFeeSets;
    }
    return textFilteredFeeSets.filter((fs) =>
      fs.entityTypes.some((et) => selectedEntityTypes.includes(et)),
    );
  }, [textFilteredFeeSets, selectedEntityTypes]);

  // Toggle an entity type in the selection
  function toggleEntityType(et: string) {
    if (selectedEntityTypes.includes(et)) {
      // Remove
      setSelectedEntityTypes((prev) => prev.filter((x) => x !== et));
    } else {
      // Add
      setSelectedEntityTypes((prev) => [...prev, et]);
    }
  }

  return (
    <aside className="flex h-full w-[300px] shrink-0 flex-col overflow-y-auto border-r p-4">
      <div className="mb-4 flex items-center justify-between">
        <h2 className="text-xl font-bold">Fee Sets</h2>
        <Button size="sm" onClick={() => setSelectedFeeSet(null)}>
          New Fee Set
        </Button>
      </div>

      {/* Text Search for FeeSets */}
      <Input
        type="text"
        placeholder="Search fee sets..."
        className="mb-4 w-full"
        value={searchFilter}
        onChange={(e) => setSearchFilter(e.target.value)}
      />

      {/* Multi-select dropdown for entity types */}
      <div className="mb-4">
        <Label className="mb-2 block text-sm font-semibold">
          Filter by Entity Types
        </Label>
        <Popover open={isPopoverOpen} onOpenChange={setIsPopoverOpen}>
          <PopoverTrigger asChild>
            <Button variant="primary" className="w-full justify-between">
              {selectedEntityTypes.length > 0
                ? `Selected (${selectedEntityTypes.length})`
                : "Select Entity Types"}
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-60 p-0">
            <Command>
              <CommandInput placeholder="Search entity types..." />
              <ScrollArea className="h-60">
                <CommandList>
                  {allEntityTypes.map((et) => {
                    const checked = selectedEntityTypes.includes(et);
                    return (
                      <CommandItem
                        key={et}
                        onSelect={() => toggleEntityType(et)}
                        className="flex cursor-pointer items-center gap-2 hover:bg-gray-100"
                      >
                        <Checkbox
                          checked={checked}
                          onCheckedChange={() => toggleEntityType(et)}
                        />
                        <span>{et}</span>
                      </CommandItem>
                    );
                  })}
                </CommandList>
              </ScrollArea>
            </Command>
          </PopoverContent>
        </Popover>
      </div>

      {/* Final FeeSets List */}
      <ScrollArea className="flex-1">
        {finalFeeSets.length === 0 ? (
          <p className="text-muted-foreground text-sm">No fee sets found.</p>
        ) : (
          <ul className="space-y-1">
            {finalFeeSets.map((feeSet) => (
              <li
                key={feeSet.label}
                className={`cursor-pointer rounded p-2 transition-colors  ${
                  selectedFeeSet && selectedFeeSet.label === feeSet.label
                    ? "bg-neutral-200"
                    : "hover:bg-gray-200"
                }`}
                onClick={() => setSelectedFeeSet(feeSet)}
              >
                <p className="font-semibold">{feeSet.label}</p>
                <p className="text-muted-foreground text-xs italic">
                  {feeSet.groupName}
                </p>
              </li>
            ))}
          </ul>
        )}
      </ScrollArea>
    </aside>
  );
}
