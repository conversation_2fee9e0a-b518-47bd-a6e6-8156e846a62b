"use client";

import { useEffect, useState } from "react";
import { useForm, FormProvider } from "react-hook-form";
import { But<PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Card, CardContent } from "@/components/ui/card";
import { Switch } from "@/components/ui/switch";
import { DialogFooter } from "@/components/ui/dialog";
import {
  offsetByOptions,
  relativeDateOptions,
  entityTypeOptions,
  recurrenceTypeOptions,
  operationOptions,
  monthOptions,
  relativeOptions,
} from "./feeOptions";
import {
  FormField,
  SelectField,
  SwitchField,
  ToggleGroupField,
} from "./feeObjects/FeeFormFields";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { cn } from "@/lib/utils";
import { EditFeeCodeProp } from "@/components/fees/modal/FeeTypes";
import { addDays, addMonths, addWeeks, addYears, format } from "date-fns";

export type DatePropInput = {
  type: "relative" | "fixed";
  date: string;
  offset: number;
  offsetBy: "days" | "weeks" | "months";
};

interface FeeFormProps {
  fee?: EditFeeCodeProp | null;
  onSubmit: (fee: EditFeeCodeProp) => void;
  onCancel?: () => void;
  className?: string;
}

// Extend the base type so the form can include nested recurrence fields.
export interface FeeFormDefaults extends EditFeeCodeProp {}

const defaultFeeFormDefaults: FeeFormDefaults = {
  key: "",
  feeName: "",
  amount: 0,
  operation: "MANUAL",
  isRecurring: false,
  recurringAmount: 0,
  startDate: {
    type: "relative",
    date: "today",
    offset: 0,
    offsetBy: "days",
  },
  endDate: {
    type: "relative",
    date: "today",
    offset: 0,
    offsetBy: "days",
  },
  recurrence: {
    recurrenceType: "daily",
    customCron: "",
    dayOfMonth: undefined,
    monthOfYear: undefined,
    selectedDays: [],
    time: {
      hours: 0,
      minutes: 0,
      seconds: 0,
    },
  },
  entityTypes: [],
};

export function FeeForm({ fee, onSubmit, onCancel, className }: FeeFormProps) {
  console.log(fee);

  const [hasEndDate, setHasEndDate] = useState(false);

  const methods = useForm<FeeFormDefaults>({
    defaultValues: fee || defaultFeeFormDefaults,
  });

  const isRecurring = methods.watch("isRecurring");
  const recurrenceType = methods.watch("recurrence.recurrenceType");
  const startDateType = methods.watch("startDate.type");
  const endDateType = methods.watch("endDate.type");
  const startDateOffsetBy = methods.watch("startDate.offsetBy");
  const endDateOffsetBy = methods.watch("endDate.offsetBy");
  const startDateOffset = methods.watch("startDate.offset");
  const endDateOffset = methods.watch("endDate.offset");

  type OffsetUnit = "days" | "weeks" | "months" | "years";

  interface OffsetParams {
    baseDate: Date;
    offset: number;
    unit: OffsetUnit;
  }

  const offsetFunctions: Record<
    OffsetUnit,
    (date: Date, amount: number) => Date
  > = {
    days: addDays,
    weeks: addWeeks,
    months: addMonths,
    years: addYears,
  };

  const toLocalDate = (dateString: string): Date => {
    const [year, month, day] = dateString.split("-").map(Number);
    return new Date(year, month - 1, day);
  };

  const getOffsetParams = (type: "start" | "end"): OffsetParams | null => {
    if (type === "start") {
      if (startDateType === "relative") {
        return {
          baseDate: new Date(),
          offset: startDateOffset,
          unit: startDateOffsetBy,
        };
      } else {
        const baseDate = toLocalDate(methods.watch("startDate.date"));
        return {
          baseDate,
          offset: startDateOffset,
          unit: startDateOffsetBy,
        };
      }
    }

    if (type === "end") {
      if (endDateType === "relative") {
        return {
          baseDate: new Date(),
          offset: endDateOffset,
          unit: endDateOffsetBy,
        };
      } else {
        const baseDate = toLocalDate(methods.watch("endDate.date"));
        return {
          baseDate,
          offset: endDateOffset,
          unit: endDateOffsetBy,
        };
      }
    }

    return null;
  };

  const exampleOffset = (type: "start" | "end"): string | null => {
    const params = getOffsetParams(type);
    if (!params) return null;

    const { baseDate, offset, unit } = params;
    const addFn = offsetFunctions[unit];
    if (!addFn) return null;

    const offsetDate = addFn(baseDate, offset);
    if (isNaN(offsetDate.getTime())) return null;

    return format(offsetDate, "MM/dd/yyyy");
  };

  useEffect(() => {
    const safeDefaults = {
      ...defaultFeeFormDefaults,
      ...fee,
      startDate: {
        ...defaultFeeFormDefaults.startDate,
        ...(fee?.startDate || {}),
      },
      endDate: {
        ...defaultFeeFormDefaults.endDate,
        ...(fee?.endDate || {}),
      },
      recurrence: {
        ...defaultFeeFormDefaults.recurrence,
        ...(fee?.recurrence || {}),
      },
    };

    methods.reset(safeDefaults);
    setHasEndDate(!!(fee && fee.endDate));
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [fee, methods.reset]);

  const onFormSubmit = (data: FeeFormDefaults) => {
    console.log(data);
    const finalFee: EditFeeCodeProp = {
      key: data.key,
      feeName: data.feeName,
      amount: data.amount,
      operation: data.operation,
      entityTypes: data.entityTypes,
    };

    if (data.isRecurring) {
      finalFee.isRecurring = data.isRecurring;
      finalFee.recurringAmount = data.recurringAmount;
      finalFee.startDate = data.startDate;
      if (hasEndDate && data.endDate) {
        finalFee.endDate = data.endDate;
      }
      if (data.occurrenceCount !== undefined) {
        finalFee.occurrenceCount = data.occurrenceCount;
      }

      if (data.recurrence) {
        finalFee.recurrence = {
          recurrenceType: data.recurrence.recurrenceType,
          customCron: data.recurrence.customCron,
          dayOfMonth: data.recurrence.dayOfMonth,
          monthOfYear: data.recurrence.monthOfYear,
          selectedDays: data.recurrence.selectedDays,
          time: data.recurrence.time,
        };
      }
    }

    console.log(finalFee);

    onSubmit(finalFee);
  };

  const operation = methods.watch("operation");

  return (
    <FormProvider {...methods}>
      <form
        onSubmit={methods.handleSubmit(onFormSubmit)}
        className={cn(
          "flex h-full w-full flex-col space-y-8 overflow-y-auto overflow-x-hidden p-4",
          className,
        )}
      >
        <h2 className="text-2xl font-bold">
          {fee ? "Edit Fee" : "Create New Fee"}
        </h2>

        <Tabs defaultValue="details">
          <TabsList className="grid w-full grid-cols-2 bg-white shadow">
            <TabsTrigger
              value="details"
              className="rounded data-[state=active]:bg-clerk-primary data-[state=active]:text-white"
            >
              Details
            </TabsTrigger>
            <TabsTrigger
              value="recurring"
              className="rounded data-[state=active]:bg-clerk-primary data-[state=active]:text-white"
            >
              Recurring
            </TabsTrigger>
          </TabsList>

          <TabsContent value="details">
            <Card>
              <CardContent className="space-y-4 pt-4">
                <FormField
                  id="key"
                  label="Fee Code"
                  placeholder="Enter fee code key"
                  requiredMessage="Fee Code is required"
                  isDisabled={!!fee}
                />

                <FormField
                  id="feeName"
                  label="Fee Name"
                  placeholder="Enter fee name"
                  requiredMessage="Fee Name is required"
                />

                <FormField
                  id="amount"
                  label={operation === "PERCENTAGE" ? "Percentage" : "Amount"}
                  type="number"
                  placeholder="Enter fee amount"
                  requiredMessage="Amount is required"
                />

                <SelectField
                  id="operation"
                  label="Operation"
                  options={operationOptions}
                  requiredMessage="Operation is required"
                />

                <ToggleGroupField
                  id="entityTypes"
                  label="Entity Types"
                  options={entityTypeOptions}
                  requiredMessage="At least one entity type must be selected"
                />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="recurring">
            <Card>
              <CardContent className="flex flex-col pt-4">
                <div className="w-full space-y-10">
                  <SwitchField id="isRecurring" label="Has Recurring Fee" />
                  {isRecurring && (
                    <>
                      <div className="w-full space-y-4">
                        <h3 className="font-semibold">Recurring Information</h3>
                        <div className="space-y-4 rounded border border-blue-300 p-4">
                          <FormField
                            id="recurringAmount"
                            label="Recurring Amount"
                            type="number"
                            placeholder="Enter recurring amount"
                            requiredMessage="Recurring amount is required"
                          />
                          <SelectField
                            id="recurrence.recurrenceType"
                            label="Recurrence Type"
                            options={recurrenceTypeOptions}
                            requiredMessage="Recurrence type is required"
                          />

                          {recurrenceType === "weekly" && (
                            <ToggleGroupField
                              id="recurrence.selectedDays"
                              label="Selected Days"
                              options={[
                                { label: "Sun", value: "Sun" },
                                { label: "Mon", value: "Mon" },
                                { label: "Tue", value: "Tue" },
                                { label: "Wed", value: "Wed" },
                                { label: "Thu", value: "Thu" },
                                { label: "Fri", value: "Fri" },
                                { label: "Sat", value: "Sat" },
                              ]}
                              requiredMessage="At least one day must be selected"
                            />
                          )}

                          {(recurrenceType === "monthly" ||
                            recurrenceType === "yearly") && (
                            <SelectField
                              id="recurrence.dayOfMonth"
                              label="Day of Month"
                              options={Array.from({ length: 31 }, (_, i) => {
                                const day = String(i + 1);
                                return { label: day, value: day };
                              })}
                              requiredMessage="Day of month is required"
                            />
                          )}

                          {recurrenceType === "yearly" && (
                            <SelectField
                              id="recurrence.monthOfYear"
                              label="Month"
                              options={monthOptions}
                              requiredMessage="Month is required"
                            />
                          )}

                          {recurrenceType === "custom" && (
                            <FormField
                              id="recurrence.customCron"
                              label="Custom Cron Expression"
                              placeholder="Enter custom cron expression"
                              requiredMessage="Cron expression is required"
                            />
                          )}
                        </div>
                      </div>

                      {/* Start Date */}
                      <div className="w-full space-y-4">
                        <h3 className="font-semibold">Start Date</h3>
                        <div className="space-y-4 rounded border border-blue-300 p-4">
                          <SelectField
                            id="startDate.type"
                            label="Type"
                            options={relativeOptions}
                            requiredMessage="Type is required"
                          />
                          {startDateType === "relative" ? (
                            <SelectField
                              id="startDate.date"
                              label="Value"
                              options={relativeDateOptions}
                              requiredMessage="Relative date is required"
                            />
                          ) : (
                            <FormField
                              id="startDate.date"
                              type="date"
                              label="Value"
                              placeholder="Select date"
                              requiredMessage="Date is required"
                            />
                          )}
                          <div className="flex items-center gap-2">
                            <FormField
                              id="startDate.offset"
                              label="Offset"
                              type="number"
                              placeholder="Enter offset"
                              requiredMessage="Offset is required"
                            />
                            <SelectField
                              id="startDate.offsetBy"
                              label="Offset By"
                              options={offsetByOptions}
                              requiredMessage="Offset by is required"
                            />
                          </div>
                          <div className="text-sm">
                            Example: {exampleOffset("start")}
                          </div>
                        </div>
                      </div>

                      <div className="w-full space-y-2">
                        <div className="flex items-center gap-4">
                          <Switch
                            id="hasEndDate"
                            checked={hasEndDate}
                            onCheckedChange={setHasEndDate}
                          />
                          <Label htmlFor="hasEndDate">Has End Date</Label>
                        </div>
                        {hasEndDate && (
                          <>
                            <h3 className="font-semibold">End Date</h3>
                            <div className="space-y-4 rounded border border-blue-300 p-4">
                              <SelectField
                                id="endDate.type"
                                label="Type"
                                options={relativeOptions}
                                requiredMessage="Type is required"
                              />
                              {endDateType === "relative" ? (
                                <SelectField
                                  id="endDate.date"
                                  label="Value"
                                  options={relativeDateOptions}
                                  requiredMessage="Relative date is required"
                                />
                              ) : (
                                <FormField
                                  id="endDate.date"
                                  type="date"
                                  label="Value"
                                  placeholder="Select date"
                                  requiredMessage="Date is required"
                                />
                              )}
                              <div className="flex items-center gap-2">
                                <FormField
                                  id="endDate.offset"
                                  label="Offset"
                                  type="number"
                                  placeholder="Enter offset"
                                  requiredMessage="Offset is required"
                                />
                                <SelectField
                                  id="endDate.offsetBy"
                                  label="Offset By"
                                  options={offsetByOptions}
                                  requiredMessage="Offset by is required"
                                />
                              </div>
                              <div className="text-sm">
                                Example: {exampleOffset("end")}
                              </div>
                            </div>
                          </>
                        )}
                      </div>
                    </>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        <DialogFooter className="mt-4 flex items-center gap-2">
          {onCancel && (
            <Button type="button" onClick={onCancel} variant="outline">
              Cancel
            </Button>
          )}
          <Button
            // Is submitting
            disabled={methods.formState.isSubmitting}
            type="submit"
          >
            {fee ? "Update Fee" : "Create Fee"}
          </Button>
        </DialogFooter>
      </form>
    </FormProvider>
  );
}
