import { Button } from "@/components/ui/button";
import React, { useState } from "react";
import { useFeeContext } from "./FeeContext";
import { Input } from "@/components/ui/input";
import { EditFeeCodeProp } from "@/components/fees/modal/FeeTypes";


export default function FeeSidebar() {
  const { feeCodes, setSelectedFee, selectedFee } =
    useFeeContext();
  const [searchFilter, setSearchFilter] = useState("");

  const searchFilterLower = searchFilter.toLowerCase();

  const filteredFeeCodes = feeCodes.filter((fee) => {
    if (!fee.key) return false;
    const haystack = `${fee.key.toLowerCase()} ${fee.feeName.toLowerCase()}`;
    const tokens = searchFilterLower.split(" ").filter((token) => token !== "");
    if (tokens.length === 0) return true;
    return tokens.every((token) => haystack.includes(token));
  });

  const groupedFees = filteredFeeCodes.reduce<Record<string, EditFeeCodeProp[]>>(
    (acc, fee) => {
      const groupKey = fee.key.split("-")[0];
      if (!acc[groupKey]) {
        acc[groupKey] = [];
      }
      acc[groupKey].push(fee);
      return acc;
    },
    {},
  );

  const sortedGroupKeys = Object.keys(groupedFees).sort();

  return (
    <aside className="flex h-full w-[300px] shrink-0 flex-col overflow-y-auto border-r p-4">
      <div className="mb-4 flex items-center justify-between">
        <h2 className="text-xl font-bold">Fees</h2>
        <Button size={"sm"} onClick={() => setSelectedFee(null)}>
          New Fee
        </Button>
      </div>
      <Input
        type="text"
        placeholder="Search fees"
        className="mb-8 w-full rounded border p-2"
        value={searchFilter}
        onChange={(e) => setSearchFilter(e.target.value)}
      />
      <div className="space-y-4">
        {sortedGroupKeys.map((group) => (
          <div key={group}>
            <div className="border-b py-1 text-sm font-bold uppercase">
              {group}
            </div>
            <ul className="space-y-1">
              {groupedFees[group].map((fee) => (
                <li
                  key={fee.key}
                  className={`cursor-pointer rounded p-2 ${
                    selectedFee && selectedFee.key === fee.key
                      ? "bg-gray-200"
                      : "hover:bg-gray-100"
                  }`}
                  onClick={() => {
                    const newFee = fee
                    setSelectedFee(newFee);
                  }}
                >
                  {fee.feeName}
                </li>
              ))}
            </ul>
          </div>
        ))}
      </div>
    </aside>
  );
}
