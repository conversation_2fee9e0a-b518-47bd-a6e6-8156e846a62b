"use client";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  useGetSettingsByType,
  useUpdateSettingsType,
} from "@/hooks/api/useAdmin";
import { useParams, usePathname } from "next/navigation";
import React, { useEffect, useState } from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { useForm } from "react-hook-form";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import Link from "next/link";
import { FiTrash } from "react-icons/fi";

export default function EntityTypePage() {
  const pathname = usePathname();
  const params = useParams();
  const category = params.category as string;
  const type  = params.type as string;

  const {
    register,
    handleSubmit,
    reset,
  } = useForm();

  // Tanstack
  const {
    data,
    isLoading,
    isFetching,
    refetch,
  } = useGetSettingsByType(category, type);
  const updateCategory = useUpdateSettingsType(category, type);
  const [isOptionOpen, setIsOptionOpen] = useState(false);

  console.log(data)

  const [categories, setCategories] = useState<any[]>([]);
  const [newlyAdded, setNewlyAdded] = useState<any | null>(null);

  useEffect(() => {
    if (data) {
      // sort by key
      const sorted = data.sort((a: any, b: any) => a.key.localeCompare(b.key));

      setCategories(sorted);
    }
  }, [data]);

  if (isLoading) {
    return <div>Loading...</div>;
  }

  return (
    <div className="w-full p-6">
      <div className="mb-4 flex w-full items-center justify-between gap-2">
        <h1 className="text-2xl text-neutral-700">List of Options</h1>
        <Dialog
          open={isOptionOpen}
          onOpenChange={(isOpen) => {
            setIsOptionOpen(isOpen);
            reset();
          }}
        >
          <DialogTrigger disabled={isOptionOpen || isFetching}>
            <Button>Add Options</Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>New Option</DialogTitle>
              <DialogDescription>
                <form
                  onSubmit={handleSubmit((data) => {
                    console.log(data);
                    const option = data.option;

                    const optionKey = option
                      .trim()
                      .replace(/\s/g, "_")
                      .toLowerCase();
                    console.log(optionKey);

                    const newOptions = [
                      ...categories,
                      {
                        key: optionKey,
                        name: option,
                      },
                    ].sort((a: any, b: any) => a.key.localeCompare(b.key));

                    setNewlyAdded(optionKey)
                    setCategories(newOptions);

                    updateCategory.mutate(newOptions, {
                      onSuccess: () => {
                        console.log("Success");
                        setIsOptionOpen(false);
                        reset();
                        refetch();
                      },
                      onError: (error) => {
                        console.log(error);
                      },
                    });
                  })}
                  className="flex flex-col gap-4"
                >
                  <Label htmlFor="option" className="mt-8">
                    Option Name
                  </Label>
                  <Input
                    type="text"
                    {...register("option", { required: true })}
                    className=""
                  />
                  <div className="mt-4">
                    <Button type="submit">Add Option</Button>
                  </div>
                </form>
              </DialogDescription>
            </DialogHeader>
          </DialogContent>
        </Dialog>
      </div>
      <ul className="flex flex-col gap-2">
        {categories?.length > 0 &&
          categories?.map((item: any) => {
            const newItem = item.key === newlyAdded;
            return (
            <li key={item.key} className="flex gap-2 items-center">
              <Button
                size="icon"
                variant={"destructive"}
                className="w-6 h-6"
                onClick={() => {
                  const newOptions = categories.filter(
                    (option: any) => option.key !== item.key
                  );
                  setCategories(newOptions);
                  updateCategory.mutate(newOptions, {
                    onSuccess: () => {
                      console.log("Success");
                      refetch();
                    },
                    onError: (error) => {
                      console.log(error);
                    },
                  });
                }}
              >
                <FiTrash />
              </Button>
              <Link
                href={`${pathname}/${item.key}`}
                className="cursor-pointer hover:text-blue-600 text-xl font-semibold group"
              >
                {item.name} <span className="text-neutral-700 text-xs group-hover:text-blue-600">({item.key})</span>
                {newItem && <span className="px-1 py-0.5 bg-green-600 text-white text-xs rounded ml-2">New</span>}
              </Link>
            </li>
          )})}
      </ul>
    </div>
  );
}
