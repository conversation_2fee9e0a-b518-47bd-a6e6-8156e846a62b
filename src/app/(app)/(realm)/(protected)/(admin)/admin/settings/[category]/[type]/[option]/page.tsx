"use client";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  useGetSettingsByOption,
  useGetSettingsByType,
  useUpdateSettingsOption,
} from "@/hooks/api/useAdmin";
import { useParams } from "next/navigation";
import React, { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { FiTrash } from "react-icons/fi";

export default function OptionPage() {
  const params = useParams();
  const category = params.category as string;
  const type = params.type as string;
  const option = params.option as string;

  const {
    data: options,
    isLoading,
    isFetching,
    refetch,
  } = useGetSettingsByOption(category, type, option);

  const { data: optionData } = useGetSettingsByType(category, type);
  const updateOptions = useUpdateSettingsOption(category, type, option);

  const { register, handleSubmit, reset, watch, setValue } = useForm();

  const [filteredOptions, setFilteredOptions] = useState([]);
  const [recentlyAdded, setRecentlyAdded] = useState("");

  const onSubmit = (data: any) => {
    const newOption = data.option;

    const newOptions = [
      ...options,
      {
        value: newOption,
        label: newOption,
      },
    ].sort((a, b) => a.label.localeCompare(b.label));

    updateOptions.mutate(newOptions, {
      onSuccess: () => {
        reset();
        refetch();
        setRecentlyAdded(newOption);
        setValue("option", "");

        // Scroll to the new option id
        // const element = document.getElementById(newOption);
        // if (element) {
        //   element.scrollIntoView();
        // }
      },
      onError: (error: any) => {
        console.log(error);
      },
    });
  };

  const watchOption = watch("option", "");

  useEffect(() => {
    if (options) {
      const filteredOptions = options.filter((o: { value: string }) =>
        o.value.toLowerCase().includes(watchOption.toLowerCase()),
      );
      setFilteredOptions(filteredOptions);
    }
  }, [options, watchOption]);

  if (isLoading) {
    return <div>Loading...</div>;
  }

  const currentOptionName = optionData?.find(
    (o: { key: string }) => o.key === option,
  )?.name;

  return (
    <div className="flex h-full w-full flex-col overflow-y-auto overflow-x-hidden p-6">
      <h1 className="text-2xl text-neutral-700">Option: {currentOptionName}</h1>
      <form
        onSubmit={handleSubmit(onSubmit)}
        className="flex items-center gap-2"
      >
        <Input
          {...register("option", { required: true })}
          placeholder="New Option"
          className="w-[180px]"
          disabled={isFetching}
        />
        <Button
          variant={"primary"}
          disabled={isFetching || filteredOptions.length > 0}
          type="submit"
        >
          Add
        </Button>
      </form>

      <div className="my-4 flex flex-col gap-4 rounded border bg-white p-2 shadow">
        {filteredOptions.length === 0 && <div>No Options</div>}
        {filteredOptions.length > 0 &&
          filteredOptions.map((option: { value: string; label: string }) => {
            const recent = recentlyAdded === option.value;
            return (
              <div
                key={option.value}
                className="flex items-center gap-2 text-xl"
              >
                <Button
                  className="h-6 w-6"
                  type="button"
                  variant={"destructive"}
                  size={"icon"}
                  disabled={isFetching}
                  onClick={() => {
                    const newOptions = options.filter(
                      (o: { value: string }) => o.value !== option.value,
                    );
                    updateOptions.mutate(newOptions, {
                      onSuccess: () => {
                        setRecentlyAdded("");
                        refetch();
                      },
                      onError: (error: any) => {
                        console.log(error);
                      },
                    });
                  }}
                >
                  <FiTrash />
                </Button>
                <p id={option.value}>{option.label}<span className="ml-2 text-xs">({option.value})</span></p>
                {recent && (
                  <div className="flex items-center gap-1">
                    <small className={"rounded bg-green-500 text-white px-1 py-0.5 text-sm"}>
                      New
                    </small>
                  </div>
                )}
              </div>
            );
          })}
      </div>
    </div>
  );
}