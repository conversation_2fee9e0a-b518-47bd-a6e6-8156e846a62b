"use client";
import React, { useEffect, useState } from "react";
import MonacoEditor from "@monaco-editor/react";
import { useGetJSONStorage, useUpdateJSONStorage } from "@/hooks/api/useAdmin";
import { toastAtom } from "@/components/ui/toast/toast";
import { useAtom } from "jotai";
import { cn } from "@/lib/utils";

const tabs = [
  {
    key: "individual",
    label: "Individual",
  },
  {
    key: "associations",
    label: "Associations", 
  },
  {
    key: "admin",
    label: "Admin",
  },
];

export default function SidebarPage() {
  const [config, setConfig] = useState<null | [] | any[]>(null);
  const [currentTab, setCurrentTab] = useState("individual");
  const [_, setToast] = useAtom(toastAtom);

  const {
    data: jsonListData,
    isLoading: jsonListIsLoading,
    isFetching: jsonListIsFetching,
    error: jsonListError,
    refetch: refetchJsonList,
  } = useGetJSONStorage("sidebar", currentTab);
  const mutateConfig = useUpdateJSONStorage("sidebar", currentTab);

  useEffect(() => {
    if (jsonListData) {
      setConfig(jsonListData);
    } else {
      setConfig([]);
    }
  }, [jsonListData]);

  if (jsonListIsLoading || jsonListIsFetching) {
    return <div>Loading...</div>;
  }

  if (jsonListError) {
    return <div>Error...</div>;
  }
  

  return (
    <div className="flex h-full w-full flex-row overflow-auto ">
      <div className="flex h-full w-[300px] flex-col overflow-y-auto overflow-x-hidden">
        <div className="flex flex-col gap-2 p-2">
          {tabs.map((tab) => (
            <button
              key={tab.key}
              className={cn(
                `w-full rounded p-2`,
                currentTab === tab.key && "bg-clerk-primary text-white",
              )}
              onClick={() => setCurrentTab(tab.key)}
            >
              {tab.label}
            </button>
          ))}
        </div>
        <div className="mt-auto w-full p-2">
          <button
            className="w-full rounded bg-green-700 p-2 text-white"
            onClick={() => {
              mutateConfig.mutate(config, {
                onSuccess: () => {
                  refetchJsonList();
                },
                onError: (e: any) => {
                  console.error(e);
                  setToast({
                    status: "error",
                    label: "Error",
                    message: `Error saving config`,
                  });
                },
              });
            }}
          >
            Save
          </button>
        </div>
      </div>
      <div className="flex h-full w-full flex-col overflow-hidden">
        <MonacoEditor
          key={currentTab}
          height="100%"
          language="json"
          theme="vs-dark"
          value={JSON.stringify(config, null, 2)}
          onChange={(data: any) => {
            setConfig(JSON.parse(data as string));
          }}
          options={{
            selectOnLineNumbers: true,
            automaticLayout: true,
          }}
        />
      </div>
    </div>
  );
}
