"use client";
import { cn } from "@/lib/utils";
import Link from "next/link";
import { useParams } from "next/navigation";
import React from "react";

const links = [
  {
    key: "dog",
    label: "<PERSON>",
  },
  {
    key: "individual",
    label: "Individual",
  },
  {
    key: "parcel",
    label: "Parcel",
  },
];

export default function EntitySettingsLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const params = useParams();
  const category = params.category as string;
  const type = params.type as string ?? null;
  return (
    <div className="flex h-full w-full overflow-hidden">
      <EntitySettingsSidebar category={category} type={type} />
      {children}
    </div>
  );
}

const EntitySettingsSidebar = ({
  category,
  type
}: {
  category: string;
  type: string | null;
}) => {
  return (
    <aside className="flex w-[240px] shrink-0 flex-col gap-2 overflow-y-auto overflow-x-hidden border-r bg-white p-4 py-6 shadow">
      <div className="px-2 text-2xl font-semibold tracking-tight">
        Entity Settings
      </div>
      <ul>
        {links.map((link) => {
          const active = type === link.key;
          console.log(category);
          return (
            <Link
              key={link.key}
              href={`/admin/settings/${category}/${link.key}`}
            >
              <li
                className={cn(
                  "cursor-pointer rounded-md p-2",
                  active
                    ? "bg-blue-50 font-semibold text-blue-700"
                    : "hover:bg-gray-100",
                )}
              >
                {link.label}
              </li>
            </Link>
          );
        })}
      </ul>
    </aside>
  );
};
