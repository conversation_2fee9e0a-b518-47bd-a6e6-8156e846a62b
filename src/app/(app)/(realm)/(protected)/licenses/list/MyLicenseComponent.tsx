"use client";
import Loading from "@/app/(app)/loading";
import ErrorPage from "@/components/error/ErrorPage";
import LicenseComponent from "@/components/profile/LicenseComponent";
import { useGetEntity } from "@/hooks/api/useProfiles";
import { useMyProfile } from "@/hooks/providers/useMyProfile";
import React from "react";

export default function MyLicenseComponent() {
  const { profile } = useMyProfile();
  console.log("profile", profile);
  const individualId = profile?.individual?.entityId;

  const { data, isLoading, isError } = useGetEntity("individual", individualId);

  if (isLoading) {
    return <Loading text="Loading Licenses" fixed={false} />;
  }
  if (isError) {
    return <ErrorPage message="Error Loading Licenses" />;
  }
  
  if (data)
    return (
      <div className="py-10 flex flex-col overflow-auto px-3 lg:px-6 bg-gradient-to-br from-gray-50 to-neutral-50">
        <LicenseComponent entity={data} entityType={'individual'} />
      </div>
    );

  return <div>No Licenses Available</div>;
}
