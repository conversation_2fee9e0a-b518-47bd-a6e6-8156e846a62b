"use client";
import { Licenses } from "@/components/application/ApplicationCard";
// import PetLicenseDisclaimerMessage from "@/components/banners/PetLicenseDisclaimerMessage";
import AddPetLicenseRequest from "@/components/resident/AddPetLicenseRequest";
import PageContainer from "@/components/ui/Page/PageContainer";
import { useMyProfile } from "@/hooks/providers/useMyProfile";
import React from "react";

export default function NewLicensePage() {
  const { profile } = useMyProfile();
  const activeProfile = profile["individual"]?.active ?? false;

  return (
    <div className="flex flex-col overflow-y-auto md:p-6">
      <div className="flex flex-col gap-6 md:container md:mx-auto">
        {/* <PetLicenseDisclaimerMessage /> */}
        <div className="flex w-full flex-col gap-10 2xl:flex-row">
          <PageContainer className="w-full py-20 md:py-6">
            <div className="mb-10">
              <h1 className="-translate-x-1 text-5xl">Licenses</h1>
              <h2 className="text-neutral-700">
                List of licenses you can apply for. Click on the license to
                apply.
              </h2>
            </div>
            {activeProfile && <Licenses />}
          </PageContainer>
          <PageContainer className="py-20 md:py-6 lg:max-w-sm w-full">
            <AddPetLicenseRequest
              title="Not seeing your license?"
              description="Request to add your existing pet license to your account."
            />
          </PageContainer>
        </div>
      </div>
    </div>
  );
}
