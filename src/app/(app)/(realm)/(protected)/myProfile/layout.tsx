"use client";
import MainEntityLayout from "../profile/[entityType]/[entityId]/MainEntityLayout";
import { EntityProvider, useEntity } from "@/hooks/providers/useEntity";
import Loading from "@/app/(app)/loading";
import ErrorPage from "@/components/error/ErrorPage";
import { useMyProfile } from "@/hooks/providers/useMyProfile";

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const { profile } = useMyProfile();
  return (
    <EntityProvider
      id={profile.individual.entityId as string}
      type="individual"
    >
      <RenderProfile>{children}</RenderProfile>
    </EntityProvider>
  );
}

const RenderProfile = ({ children }: { children: React.ReactNode }) => {
  const { entityId, entityType, entity, entityIsError, entityIsLoading } =
    useEntity();

  if (entityIsLoading)
    return <Loading text={"Loading Profile"} fixed={false} />;
  if (entityIsError) return <ErrorPage message={"Error Loading Profile"} />;

  const mainLayouts = ["individual", "dog"];

  if (entity) {
    if (mainLayouts.includes(entityType)) {
      return (
        <MainEntityLayout
          entity={entity}
          entityId={entityId}
          entityType={entityType}
        >
          {children}
        </MainEntityLayout>
      );
    }
  }

  return null;
};
