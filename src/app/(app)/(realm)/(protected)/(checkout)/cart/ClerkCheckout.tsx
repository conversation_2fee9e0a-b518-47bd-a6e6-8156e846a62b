"use client";
import BreadCrumb from "@/components/ui/breadcrumbs/BreadCrumb";
import { useMyCart } from "@/hooks/useMyCart";
import { FormProvider, useForm } from "react-hook-form";
import CartForm from "./CartForm";
import CartSummary from "./CartSummary";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { useGetPayeeInfo, useSubmitOrder, useGetActiveCart } from "@/hooks/api/useCart";
import LoadingSpinner from "@/components/ui/LoadingSpinner";
import PaymentFormBuilderFactory from "@/components/builders/PaymentFormBuilder/PaymentFormBuilderFactory";

type Order = {
  cartId: string;
  items: any;
  payee: any;
  paymentProvider?: string;
  paymentType: string;
  paymentReference: string;
  transactonDate?: string | Date;
  status?: string;
  amount: number;
  orderId?: string;
  adjustmentReason?: string;
  adjustedAmount?: number;
};

const ClerkCheckout = () => {
  const router = useRouter();
  const [total, setTotal] = useState(0);
  const [processingPayment, setProcessingPayment] = useState(false);
  const { cartSummary, cartLoading, cartIsFetching, paymentMethod, setPaymentMethod } = useMyCart();
  const methods = useForm({
    mode: "onChange",
  });
  const { setValue } = methods;

  const cartId = cartSummary?.cartId ?? null;
  const { data } = useGetPayeeInfo(cartId as string);

  useEffect(() => {
    if (data) {
      for (const key in data) {
        setValue(key, data[key]);
      }
    }
  }, [data, setValue]);


  const submitOrderMutation = useSubmitOrder();

  const LoadingModal = () => {
    return (
      <div className="fixed top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 max-w-full w-auto rounded border border-black bg-white p-4 shadow-xl text-center">
        <h2 className="font-bold text-4xl">Loading...</h2>
        <div className="w-full text-center flex justify-center">
          <LoadingSpinner />
        </div>
        <p>Processing Payment please wait</p>
      </div>
    );
  };

  useEffect(() => {
    if (cartSummary?.total) {
      setTotal(cartSummary.total);
    }
  }, [cartSummary]);

  useEffect(() => {
    setPaymentMethod("");
    // eslint-disable-next-line react-hooks/exhaustive-deps
  },[]);

  const onSubmit = async (data: any) => {
    setProcessingPayment(true);
    const {
      paymentReference,
      adjustmentReason,
      adjustedAmount,
      ...restOfData
    } = data;
    const cartItems = cartSummary?.items.map((item: any) => {
      return {
        cartItemId: item.cartItemId,
        itemId: item.itemId,
        itemType: item.itemType,
      };
    });

    let order: Order = {
      cartId: cartSummary?.cartId as string,
      amount: total,
      paymentType: paymentMethod as string,
      paymentReference,
      adjustmentReason,
      adjustedAmount,
      payee: restOfData,
      items: cartItems,
    };

    submitOrderMutation.mutate(order, {
      onSuccess: (data: any) => {
        const balance = data.balance;
        if (balance > 0) {
          console.log("balance is greater than 0");
        } else {
          router.push(`/orderSuccess?orderId=${data.orderId}`);
        }
        setProcessingPayment(false);
      },
      onError: (error) => {
        router.push(
          `/cart?error=${error.response.data.message}&status=${error.response.status.toString()}`,
        );
        setProcessingPayment(false);
      },
    });
  };

  const { handleSubmit } = methods;

  if (processingPayment) return <LoadingModal />;

  return (
    <div className="mx-auto flex h-full w-full flex-col gap-6 overflow-auto bg-neutral-100 p-3 lg:p-6">
      <div className="lg:container lg:mx-auto">
        <BreadCrumb />
        <FormProvider {...methods}>
          <form
            className="flex flex-col gap-10 xl:flex-row "
            onSubmit={handleSubmit(onSubmit)}
          >
            <div className="flex flex-col gap-10">
              <CartForm />
              <PaymentFormBuilderFactory setValue={setValue} />
            </div>
            <CartSummary
              cart={cartSummary as any}
              loading={cartLoading || cartIsFetching || processingPayment}
            />
          </form>
        </FormProvider>
      </div>
    </div>
  );
};

export default ClerkCheckout;
