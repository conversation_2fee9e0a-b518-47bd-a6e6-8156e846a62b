export const cartForm = {
  user: [
    {
      label: "First Name",
      fieldName: "firstName",
      type: "text",
      defaultValue: "",
      size: "lg",
      required: { value: true, message: 'First Name Required' },
      min: null,
      max: null,
      pattern: null,
      info: null,
    },
    {
      label: "Last Name",
      fieldName: "lastName",
      type: "text",
      defaultValue: "",
      size: "lg",
      required: { value: true, message: 'Last Name Required' },
      min: null,
      max: null,
      pattern: null,
      info: null,
    },
    {
      label: "Organization/Business Name",
      fieldName: "businessName",
      type: "text",
      defaultValue: "",
      size: "full",
      required: { value: false},
      min: null,
      max: null,
      pattern: null,
      info: null,
    },
    {
      label: "Email",
      fieldName: "email",
      type: "email",
      defaultValue: "",
      size: "lg",
      required: { value: false, message: '' },
      min: null,
      max: null,
      pattern: null,
      info: null,
    },
    {
      label: "Phone",
      fieldName: "phone",
      type: "tel",
      defaultValue: "",
      size: "lg",
      required: { value: true, message: '' },
      min: null,
      max: null,
      pattern: null,
      info: null,
    },
  ],
  mailing: [
    {
      label: "Address",
      fieldName: "mailingAddress",
      type: "text",
      defaultValue: "",
      size: "xl",
      required: { value: true, message: '' },
      google: true,
    },
    {
      label: "Address 2",
      fieldName: "mailingAddress2",
      type: "text",
      defaultValue: "",
      size: "md",
      required: { value: false},
      google: true,
    },
    {
      label: "City",
      fieldName: "mailingCity",
      type: "text",
      defaultValue: "",
      size: "md",
      required: { value: true, message: '' },
      google: true,
    },
    {
      label: "State",
      fieldName: "mailingState",
      type: "select",
      defaultValue: "",
      size: "md",
      options: [
        { label: "Select a State", value: "" },
        { label: "Alabama", value: "AL" },
        { label: "Alaska", value: "AK" },
        { label: "Arizona", value: "AZ" },
        { label: "Arkansas", value: "AR" },
        { label: "California", value: "CA" },
        { label: "Colorado", value: "CO" },
        { label: "Connecticut", value: "CT" },
        { label: "Delaware", value: "DE" },
        { label: "District Of Columbia", value: "DC" },
        { label: "Florida", value: "FL" },
        { label: "Georgia", value: "GA" },
        { label: "Hawaii", value: "HI" },
        { label: "Idaho", value: "ID" },
        { label: "Illinois", value: "IL" },
        { label: "Indiana", value: "IN" },
        { label: "Iowa", value: "IA" },
        { label: "Kansas", value: "KS" },
        { label: "Kentucky", value: "KY" },
        { label: "Louisiana", value: "LA" },
        { label: "Maine", value: "ME" },
        { label: "Maryland", value: "MD" },
        { label: "Massachusetts", value: "MA" },
        { label: "Michigan", value: "MI" },
        { label: "Minnesota", value: "MN" },
        { label: "Mississippi", value: "MS" },
        { label: "Missouri", value: "MO" },
        { label: "Montana", value: "MT" },
        { label: "Nebraska", value: "NE" },
        { label: "Nevada", value: "NV" },
        { label: "New Hampshire", value: "NH" },
        { label: "New Jersey", value: "NJ" },
        { label: "New Mexico", value: "NM" },
        { label: "New York", value: "NY" },
        { label: "North Carolina", value: "NC" },
        { label: "North Dakota", value: "ND" },
        { label: "Ohio", value: "OH" },
        { label: "Oklahoma", value: "OK" },
        { label: "Oregon", value: "OR" },
        { label: "Pennsylvania", value: "PA" },
        { label: "Rhode Island", value: "RI" },
        { label: "South Carolina", value: "SC" },
        { label: "South Dakota", value: "SD" },
        { label: "Tennessee", value: "TN" },
        { label: "Texas", value: "TX" },
        { label: "Utah", value: "UT" },
        { label: "Vermont", value: "VT" },
        { label: "Virginia", value: "VA" },
        { label: "Washington", value: "WA" },
        { label: "West Virginia", value: "WV" },
        { label: "Wisconsin", value: "WI" },
        { label: "Wyoming", value: "WY" }
      ],
      required: { value: true, message: '' },
      google: true,
    },
    {
      label: "Zip Code",
      fieldName: "mailingZipCode",
      type: "text",
      defaultValue: "",
      size: "md",
      required: { value: true, message: '' },
      google: true,
    },
  ],
  billing: [
    {
      label: "Address",
      fieldName: "billingAddress",
      type: "text",
      defaultValue: "",
      size: "xl",
      required: { value: true, message: '' },
      google: true,
    },
    {
      label: "Address 2",
      fieldName: "billingAddress2",
      type: "text",
      defaultValue: "",
      size: "md",
      required: { value: false},
      google: true,
    },
    {
      label: "City",
      fieldName: "billingCity",
      type: "text",
      defaultValue: "",
      size: "md",
      required: { value: true, message: '' },
      google: true,
    },
    {
      label: "State",
      fieldName: "billingState",
      type: "select",
      defaultValue: "",
      size: "md",
      options: [
        { label: "Select a State", value: "" },
        { label: "Alabama", value: "AL" },
        { label: "Alaska", value: "AK" },
        { label: "Arizona", value: "AZ" },
        { label: "Arkansas", value: "AR" },
        { label: "California", value: "CA" },
        { label: "Colorado", value: "CO" },
        { label: "Connecticut", value: "CT" },
        { label: "Delaware", value: "DE" },
        { label: "District Of Columbia", value: "DC" },
        { label: "Florida", value: "FL" },
        { label: "Georgia", value: "GA" },
        { label: "Hawaii", value: "HI" },
        { label: "Idaho", value: "ID" },
        { label: "Illinois", value: "IL" },
        { label: "Indiana", value: "IN" },
        { label: "Iowa", value: "IA" },
        { label: "Kansas", value: "KS" },
        { label: "Kentucky", value: "KY" },
        { label: "Louisiana", value: "LA" },
        { label: "Maine", value: "ME" },
        { label: "Maryland", value: "MD" },
        { label: "Massachusetts", value: "MA" },
        { label: "Michigan", value: "MI" },
        { label: "Minnesota", value: "MN" },
        { label: "Mississippi", value: "MS" },
        { label: "Missouri", value: "MO" },
        { label: "Montana", value: "MT" },
        { label: "Nebraska", value: "NE" },
        { label: "Nevada", value: "NV" },
        { label: "New Hampshire", value: "NH" },
        { label: "New Jersey", value: "NJ" },
        { label: "New Mexico", value: "NM" },
        { label: "New York", value: "NY" },
        { label: "North Carolina", value: "NC" },
        { label: "North Dakota", value: "ND" },
        { label: "Ohio", value: "OH" },
        { label: "Oklahoma", value: "OK" },
        { label: "Oregon", value: "OR" },
        { label: "Pennsylvania", value: "PA" },
        { label: "Rhode Island", value: "RI" },
        { label: "South Carolina", value: "SC" },
        { label: "South Dakota", value: "SD" },
        { label: "Tennessee", value: "TN" },
        { label: "Texas", value: "TX" },
        { label: "Utah", value: "UT" },
        { label: "Vermont", value: "VT" },
        { label: "Virginia", value: "VA" },
        { label: "Washington", value: "WA" },
        { label: "West Virginia", value: "WV" },
        { label: "Wisconsin", value: "WI" },
        { label: "Wyoming", value: "WY" }
      ],
      required: { value: true, message: '' },
      google: true,
    },
    {
      label: "Zip Code",
      fieldName: "billingZipCode",
      type: "text",
      defaultValue: "",
      size: "md",
      required: { value: true, message: '' },
      google: true,
    },
  ],
};
