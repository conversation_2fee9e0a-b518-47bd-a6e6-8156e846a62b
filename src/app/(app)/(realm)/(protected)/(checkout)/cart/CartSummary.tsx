"use client";
import React, { useState } from "react";
import PageContainer from "@/components/ui/Page/PageContainer";
import CartItems from "./CartItems";
import CartFees from "./CartFees";
import { ActiveCartItems } from "@/types/CartType";
import { useSearchParams } from "next/navigation";
import { Controller, useFormContext } from "react-hook-form";
import Button from "@/components/ui/buttons/Button";
import Modal from "@/components/modal/Modal";
import { ErrorMessage } from "@hookform/error-message";
import PaymentRedirect from "./PaymentRedirect";
import { useMyCart } from "@/hooks/useMyCart";

const formatter = new Intl.NumberFormat("en-US", {
  style: "currency",
  currency: "USD",
});

const EmptyCart = () => (
  <div className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded relative mt-4">
    <strong className="font-bold">Cart is Empty</strong>
    <br />
    <span className="block sm:inline">Add items to your cart to continue.</span>
  </div>
);

const CartSummary = ({
  cart,
  loading,
}: {
  cart: ActiveCartItems;
  loading: boolean;
}) => {
  const {
    control,
    setValue,
    clearErrors,
    watch,
    formState: { errors },
  } = useFormContext();
  const searchParams = useSearchParams();
  const error = searchParams.get("error");
  const status = searchParams.get("status");
  const [isOpen, setIsOpen] = useState(false);
  const [adjustedAmount, setAdjustedAmount] = useState(0);
  const [tempAdjustedAmount, setTempAdjustedAmount] = useState(0);
  const adjustmentReasonValue = watch("adjustmentReason");
  const { paymentMethod } = useMyCart();
  const paymentMode = watch("paymentMode");

  if (loading) {
    return (
      <PageContainer className="flex h-fit w-full shrink-0 flex-col gap-2 xl:max-w-md">
        <div className="h-8 w-48 animate-pulse rounded bg-gray-200" />
        <div className="space-y-4">
          {[1, 2, 3].map((i) => (
            <div key={i} className="h-24 animate-pulse rounded bg-gray-200" />
          ))}
        </div>
        <div className="mt-4 h-12 animate-pulse rounded bg-gray-200" />
      </PageContainer>
    );
  }

  if (!cart) return null;

  const cartEmpty = cart.items.length === 0;
  const isPaymentMethodSelected = Boolean(paymentMethod);
  const isDisabled = cartEmpty || !isPaymentMethodSelected;

  return (
    <PageContainer className="flex h-fit w-full shrink-0 flex-col gap-2 xl:max-w-md">
      <h1 className="text-xl font-bold">Cart Summary</h1>

      {cartEmpty ? <EmptyCart /> : <CartItems items={cart.items} />}
      <CartFees
        cart={cart}
        reason={adjustmentReasonValue}
        adjustment={adjustedAmount}
      />

      {error && (
        <div className="relative rounded border border-red-400 bg-red-100 px-4 py-3 text-red-700">
          <strong className="font-bold">
            {status === "500" ? "Server Error" : "Error"}
          </strong>
          <br />
          <span className="block whitespace-break-spaces break-words sm:inline">
            {status === "500"
              ? "Issue with server. Please contact your adminstrator."
              : error}
          </span>
        </div>
      )}

      {/* Form state errors */}
      {Object.keys(errors).length > 0 && (
        <div className="relative rounded border border-red-400 bg-red-100 px-4 py-3 text-red-700">
          {Object.entries(errors).map(([key, errorDetail], index) => (
            <div key={index}>
              <span className="block whitespace-break-spaces break-words sm:inline">
                {errorDetail?.message as any}
              </span>
            </div>
          ))}
        </div>
      )}

      {paymentMode === "online" ? (
        <PaymentRedirect
          cartId={cart.cartId}
          amount={cart.total}
          disabled={isDisabled}
        />
      ) : (
        <button
          type="submit"
          className={`
          mt-10 w-full rounded py-2 font-semibold text-white
          ${isDisabled || paymentMode !== "offline" 
            ? "bg-gray-400 cursor-not-allowed" 
            : "bg-blue-600 hover:bg-blue-700"}
        `}
          disabled={isDisabled || paymentMode !== "offline"}
        >
          <span>
            {loading
              ? "Processing Payment..."
              : `Submit Payment (${formatter.format(
                  cart.total - adjustedAmount,
                )})`}
          </span>
        </button>
      )}

      <Modal
        isOpen={isOpen}
        onClose={() => {
          setTempAdjustedAmount(0);
          setValue("adjustedAmount", "");
          setValue("adjustmentReason", "");
          clearErrors("adjustedAmount");
          setIsOpen(false);
        }}
        title="Price Adjustment"
        className="max-w-md"
      >
        <div className="mt-4">
          Adjusted Amount
          <span className="ml-2 text-sm italic text-red-500">*Required</span>
        </div>
        <Controller
          name="adjustedAmount"
          control={control}
          defaultValue=""
          rules={{
            min: {
              value: 0,
              message: "Adjusted amount must be greater than 0",
            },
            max: {
              value: cart.total,
              message: `Adjusted amount cannot exceed the current total of ${formatter.format(
                cart.total,
              )}`,
            },
          }}
          render={({ field }) => (
            <input
              {...field}
              type="number"
              className="w-full rounded border p-2"
              onChange={(e) => {
                const inputValue = Number(e.target.value);
                setTempAdjustedAmount(
                  inputValue > cart.total ? cart.total : inputValue,
                );
                field.onChange(e);
              }}
            />
          )}
        />
        <ErrorMessage
          errors={errors}
          name="adjustedAmount"
          render={({ message }) => (
            <p className="text-sm text-red-500 ">{message}</p>
          )}
        />
        {/* Comment box */}
        <div className="mt-4">
          <label htmlFor="adjustmentReason" className="mb-2 block">
            Reason:{" "}
            <span className="ml-2 text-sm italic text-red-500">*Required</span>
          </label>
          <Controller
            name="adjustmentReason"
            control={control}
            defaultValue=""
            render={({ field }) => (
              <textarea
                {...field}
                id="adjustmentReason"
                rows={3}
                className="w-full rounded border p-2"
                placeholder="Add your reason about the adjustment here."
              ></textarea>
            )}
          />
          <ErrorMessage
            errors={errors}
            name="adjustmentReason"
            render={({ message }) => (
              <p className="text-sm text-red-500 ">{message}</p>
            )}
          />
        </div>
        <h3 className="mt-4 text-right font-semibold">
          Current Total: {formatter.format(cart.total)}
        </h3>
        <h3 className="mt-4 text-right font-semibold">
          Adjusted Amount: {formatter.format(tempAdjustedAmount)}
        </h3>
        <div className="mt-2 text-right text-xl  font-bold">
          <span className="font-semibold">New Total:</span>{" "}
          {formatter.format(cart.total - tempAdjustedAmount)}
        </div>{" "}
        {/* Displaying the new total */}
        <div className="mt-6 flex justify-end gap-4">
          <Button
            variant="secondary"
            outline
            onClick={() => {
              setIsOpen(false);
            }}
            type="button"
          >
            Cancel
          </Button>
          <Button
            type="button"
            onClick={() => {
              setAdjustedAmount(tempAdjustedAmount);
              setIsOpen(false);
            }}
            disabled={
              !!(
                errors.adjustedAmount ||
                errors.adjustmentReason ||
                tempAdjustedAmount === 0 ||
                !adjustmentReasonValue
              )
            }
          >
            Apply
          </Button>
        </div>{" "}
        {/* Displaying the new total */}
      </Modal>
    </PageContainer>
  );
};

export default CartSummary;
