import { Button } from "@/components/ui/button";
import { loadStripe } from "@stripe/stripe-js";
import { cn } from "@/lib/utils";
import React, { useEffect, useRef, useState } from "react";
import { CheckoutResponse, useGetResidentToken } from "@/hooks/api/useCart";
import { useGetJSONStorage } from "@/hooks/api/useAdmin";

function PaymentRedirect({
  cartId,
  amount,
  disabled,
}: {
  cartId: string;
  amount: number;
  disabled?: boolean;
}) {
  const mode =
    process.env.NEXT_PUBLIC_APP_ENV === "production" ? "production" : "sandbox";
  const [loading, setLoading] = useState(false);
  const formRef = useRef<HTMLFormElement>(null);
  const getResidentToken = useGetResidentToken();

  const { data: paymentRedirectType, isLoading } = useGetJSONStorage(
    "payment",
    "paymentRedirectType",
  );

  const handleStripeRedirect = async (clientSecret: string, key: string) => {
    const stripe = await loadStripe(key);
    if (stripe) {
      console.log(stripe);
      const { error } = await stripe.redirectToCheckout({
        sessionId: clientSecret,
      });
      if (error) {
        console.error("Stripe redirection error:", error.message);
      }
    } else {
      console.error("Stripe.js not loaded");
    }
  };

  const handlePayment = async (event: React.MouseEvent) => {
    event.preventDefault();
    setLoading(true);

    getResidentToken.mutate(
      { cartId, amount },
      {
        onSuccess: (data: CheckoutResponse) => {
          console.log(data);
          const { token, provider } = data;
          
          // Handle the redirection directly here instead of setting state first
          if (paymentRedirectType?.HOSTED_PAYMENT?.includes(provider) && token) {
            // For hosted payment providers, set the token and submit the form
            if (formRef.current) {
              const tokenInput = formRef.current.querySelector('input[type="hidden"]');
              if (tokenInput) {
                (tokenInput as HTMLInputElement).value = token;
                const inputName = paymentRedirectType[provider]?.inputToken;
                if(!inputName) {
                  throw new Error(`inputToken not found for provider: ${provider}`);
                }
                (tokenInput as HTMLInputElement).name = inputName;
              }
              formRef.current.action = paymentRedirectType[provider]?.[mode] || '';
              formRef.current.id = paymentRedirectType[provider]?.id || '';
              formRef.current.name = paymentRedirectType[provider]?.id || '';
              formRef.current.submit();
            }
          } else if (paymentRedirectType?.STRIPE_PAYMENT?.includes(provider) && token) {
            // For Stripe, redirect immediately
            handleStripeRedirect(token, data?.providerProperties?.key || "");
          }
        },
        onError: () => {
          setLoading(false);
        },
        onSettled: () => {
          setLoading(false);
        },
      },
    );
  };

  return (
    <div>
      {/* Hidden Form for Hosted Payment Providers */}
      <form
        method="post"
        action=""
        id=""
        name=""
        style={{ display: "none" }}
        ref={formRef}
      >
        <input
          type="hidden"
          name=""
          value=""
        />
      </form>

      {/* Payment Button */}
      <div className="mt-4 flex h-fit w-full justify-end">
        <Button
          onClick={handlePayment}
          variant={disabled || loading || isLoading ? "disabled" : "primary"}
          className={cn(
            "text-wrap text-white ",
            disabled || loading || isLoading
              ? "cursor-not-allowed text-black"
              : "cursor-pointer",
          )}
          disabled={disabled || loading || isLoading}
        >
          {loading ? "Processing..." : "Continue to Payment Page"}
        </Button>
      </div>
    </div>
  );
}

export default PaymentRedirect;