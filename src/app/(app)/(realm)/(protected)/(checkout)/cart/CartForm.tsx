"use client";

import { useState } from "react";
import PageContainer from "@/components/ui/Page/PageContainer";
import FormInputs from "@/components/forms/create/Inputs";
import Toggle from "@/components/ui/buttons/Toggle";
import { cartForm } from "./CartFormItems";

const CartForm = () => {
  const [billing, setBilling] = useState<boolean>(true);

  return (
    <div className="flex flex-col gap-4">
      <PageContainer className="w-full">
        <div className="flex flex-col gap-4">
          <div>
            <FormSectionLabel>Payee Information</FormSectionLabel>
            <FormSection>
              <FormInputs inputs={cartForm.user} />
            </FormSection>
          </div>

          <div>
            <FormSectionLabel>Mailing Address</FormSectionLabel>
            <FormSection>
              <FormInputs inputs={cartForm.mailing} />
            </FormSection>
          </div>

          {/* Is the same as address */}
          <div className="w-full flex gap-2 px-2 mb-10">
            <Toggle
              state={billing}
              setState={setBilling}
              label="Billing Address is the same as Mailing Address"
            />
          </div>

          {!billing && (
            <div>
              <FormSectionLabel>Billing Address</FormSectionLabel>
              <FormSection>
                <FormInputs inputs={cartForm.billing} />
              </FormSection>
            </div>
          )}
        </div>
      </PageContainer>
    </div>
  );
};

export default CartForm;

const FormSection = ({ children }: { children: React.ReactNode }) => {
  return <div className="flex flex-wrap mb-4">{children}</div>;
};

const FormSectionLabel = ({ children }: { children: React.ReactNode }) => {
  return <h3 className="text-lg font-bold ">{children}</h3>;
};
