"use client";
import LoadingSpinner from "@/components/ui/LoadingSpinner";
import { useMyCart } from "@/hooks/useMyCart";
import ResidentCartSummary from "./ResidentCartSummary";
import Loading from "@/app/(app)/loading";


export default function ResidentPage() {
  const { cartSummary, cartLoading,  } = useMyCart();


  if (cartLoading) return <Loading text="Getting Cart" />;
  if (!cartSummary) return <Loading text="No Cart Found" />;
  
  return (
    <div className="md:mx-auto flex h-full w-full flex-col gap-6 overflow-auto bg-neutral-100 md:p-6">
      <div className="md:container md:mx-auto">
        <div className="flex flex-col gap-10 xl:flex-row ">
          <ResidentCartSummary cart={cartSummary} />
        </div>
      </div>
    </div>
  );
}
