import { Button } from "@/components/ui/button";
import { useGetResidentToken } from "@/hooks/api/useCart";
import { cn } from "@/lib/utils";
import React, { useRef, useState, useEffect } from "react";

const AuthorizeNetPostUrl = {
  production: "https://accept.authorize.net/payment/payment",
  sandbox: "https://test.authorize.net/payment/payment",
};

function AuthorizeNetRedirect({
  cartId,
  amount,
  disabled,
}: {
  cartId: string;
  amount: number;
  disabled?: boolean;
}) {
  const mode = process.env.NEXT_PUBLIC_APP_ENV === "production" ? "production" : "sandbox";
  const [token, setToken] = useState("");
  const formRef = useRef<HTMLFormElement>(null);
  const getResidentToken = useGetResidentToken();

  useEffect(() => {
    if (token) {
      formRef.current?.submit();
    }
  }, [token]);

  const openPopup = async (event: React.MouseEvent) => {
    event.preventDefault();
    getResidentToken.mutate(
      { cartId, amount },
      {
        onSuccess: (data: { token: string }) => {
          setToken(data.token);
        },
      },
    );
  };

  return (
    <div>
      <form 
        method="post"
        action={AuthorizeNetPostUrl[mode]}
        id="formAuthorizeNetRedirect"
        name="formAuthorizeNetRedirect"
        style={{ display: "none" }}
        ref={formRef}
      >
        <input type="hidden" name="token" value={token} />
      </form>

      <div className="mt-4 flex w-full justify-end h-fit">
        <Button
          onClick={openPopup}
          variant={disabled ? "disabled" : "primary"}
          className={cn(
            "text-wrap text-white ",
            disabled ? "cursor-not-allowed text-black" : "cursor-pointer",
          )}
          disabled={disabled}
        >
          Continue to Payment Page
        </Button>
      </div>
    </div>
  );
}

export default AuthorizeNetRedirect;
