import React from "react";
import type { ActiveCartItems } from "@/types/CartType";
import { Separator } from "@/components/ui/separator";
import { DollarSign, Receipt } from "lucide-react";

const formatter = new Intl.NumberFormat("en-US", {
  style: "currency",
  currency: "USD",
});

const CartFees = ({
  cart,
  reason,
  adjustment,
}: {
  cart: ActiveCartItems;
  reason: string;
  adjustment: number;
}) => {
  return (
    <div className="rounded-lg border bg-card shadow-sm p-4 mt-2">
      <div className="flex items-center gap-2 mb-3 pb-1 border-b">
        <h3 className="font-medium">Order Summary</h3>
      </div>

      <div className="space-y-2">
        <div className="flex justify-between items-center text-sm">
          <span className="text-muted-foreground">Subtotal</span>
          <span>{formatter.format(cart.subtotal)}</span>
        </div>

        {cart.summary &&
          cart.summary.map((fee, index) => (
            <div key={index} className="flex justify-between items-center text-sm">
              <span className="text-muted-foreground">{fee.label}</span>
              <span>{formatter.format(fee.amount)}</span>
            </div>
          ))}

        {adjustment > 0 && (
          <div className="flex justify-between items-center text-sm text-emerald-600">
            <div className="flex items-center gap-1">
              <span>Discount</span>
              {reason && (
                <span className="text-xs text-muted-foreground ml-1">
                  ({reason})
                </span>
              )}
            </div>
            <span>-{formatter.format(adjustment)}</span>
          </div>
        )}

        <Separator className="my-2" />

        {/* Total */}
        <div className="flex justify-between items-center pt-1">
          <span className="font-semibold flex items-center gap-1">
            <DollarSign className="h-4 w-4" />
            Total
          </span>
          <span className="text-lg font-bold">
            {formatter.format(cart.total - adjustment)}
          </span>
        </div>
      </div>
    </div>
  );
};

export default CartFees;