import PageContainer from "@/components/ui/Page/PageContainer";
import CartItems from "./CartItems";
import ResidentCartFees from "./ResidentCartFees";
import { ActiveCartItems } from "@/types/CartType";
import { useRouter, useSearchParams } from "next/navigation";
// import AuthorizeNetPopup from "./AuthorizeNetPopup";
import AuthorizeNetRedirect from "./AuthrorizeNetRedirect";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { useNoPayment } from "@/hooks/api/useCart";
import LoadingSpinner from "@/components/ui/LoadingSpinner";
import PaymentRedirect from "./PaymentRedirect";

// const formatter = new Intl.NumberFormat("en-US", {
//   style: "currency",
//   currency: "USD",
// });

const EmptyCart = () => (
  <div className="relative mt-4 rounded border border-yellow-400 bg-yellow-100 px-4 py-3 text-yellow-700">
    <strong className="font-bold">Cart is Empty</strong>
    <br />
    <span className="block sm:inline">Add items to your cart to continue.</span>
  </div>
);

const ResidentCartSummary = ({ cart }: { cart: ActiveCartItems }) => {
  const searchParams = useSearchParams();
  const error = searchParams.get("error");
  const status = searchParams.get("status");

  // No items in cart
  const buttonDisabled = cart?.items?.length === 0;

  // When cart items total is 0
  const cartTotalIsZero = cart?.total <= 0.0;

  // Cart id
  const cartId = cart?.cartId;

  // No Payment mutation
  const noPayment: any = useNoPayment(cartId);
  if (!cart) return null;

  return (
    <PageContainer className=" h-full w-full flex-none shrink-0 rounded-none md:h-fit md:rounded">
      <h1 className="mb-6 text-2xl font-bold">Checkout</h1>
      <h2 className="text-lg font-semibold text-neutral-700">Cart Summary</h2>

      {cart.items?.length === 0 ? (
        <EmptyCart />
      ) : (
        <CartItems items={cart.items} />
      )}

      <div className="flex justify-end px-5">
        <div className="w-full max-w-[240px]">
          <ResidentCartFees cart={cart} />
        </div>
      </div>

      {error && (
        <div className="relative rounded border border-red-400 bg-red-100 px-5 py-3 text-center text-red-700">
          <strong className="font-bold">
            {status === "500" ? "Server Error" : "Error"}
          </strong>
          <br />
          <span className="block sm:inline">{error}</span>
        </div>
      )}

      {cartTotalIsZero ? (
        <div className="mt-4 flex w-full justify-end">
          <Button
            onClick={() => noPayment.mutate()}
            variant={"primary"}
            className={cn(
              "text-wrap text-white",
              buttonDisabled ? "cursor-not-allowed" : "cursor-pointer",
            )}
            disabled={buttonDisabled}
          >
            {noPayment.isLoading ? (
              <div className="flex items-center space-x-2">
                <LoadingSpinner className="size-4" />
                <span>Processing...</span>
              </div>
            ) : (
              "Process Order"
            )}
          </Button>
        </div>
      ) : (
        <PaymentRedirect
          cartId={cart.cartId}
          amount={cart.total}
          disabled={buttonDisabled}
        />
      )}
    </PageContainer>
  );
};

export default ResidentCartSummary;
