"use client";
import SummaryIcon from "@/components/cart/SummaryIcon";
import { ActiveCartItem } from "@/types/CartType";
import Link from "next/link";
import { cn } from "@/lib/utils";
import {
  <PERSON>,
  CardContent,
  CardHeader,
  CardT<PERSON>le,
  CardFooter,
} from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { FiTrash2, FiInfo, FiEdit } from "react-icons/fi";
import { useMyCart } from "@/hooks/useMyCart";
import { convertToDollar } from "@/components/fees/modal/feeHelper";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/Tooltip";
import EditTotal from "@/components/cart/editCartItem/EditTotal";
import { useMyProfile } from "@/hooks/providers/useMyProfile";

const formatter = new Intl.NumberFormat("en-US", {
  style: "currency",
  currency: "USD",
});

const CartItems = ({ items }: { items: ActiveCartItem[] }) => {
  if (!items?.length) return null;

  return (
    <Card className="mx-auto w-full overflow-hidden border shadow-sm transition-all hover:shadow-md">
      {/* Status indicator strip */}
      <div className="bg-primary h-1 w-full" />

      <CardHeader className="pb-2">
        <div className="flex flex-col justify-between gap-2 md:flex-row md:items-center">
          <div>
            <CardTitle className="flex items-center gap-3 text-xl font-semibold">
              Cart Items
              <span className="flex h-5 w-fit min-w-5 items-center justify-center rounded-full bg-neutral-200 px-1 text-xs font-semibold text-black">
                {items.length}
              </span>
            </CardTitle>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {items.map((item) => (
          <CartItemCard key={item.cartItemId} item={item} />
        ))}
      </CardContent>
    </Card>
  );
};

const CartItemCard = ({ item }: { item: ActiveCartItem }) => {
  const hasFees = item.fees && item.fees.length > 0;

  // Split the primary display into type and name
  const displayParts = item.primaryDisplay.split(' - ');
  const itemType = displayParts[0];
  const itemName = displayParts.slice(1).join(' - ');

  return (
    <div className="w-full">
      <div className="flex w-full gap-3">
        <div className="flex-shrink-0">
          <SummaryIcon item={item.itemType} />
        </div>
        
        <div className="flex flex-1 items-start justify-between gap-4">
          <div className="min-w-0 max-w-[calc(100%-100px)] flex-1">
            {item.itemType === "license" ? (
              <Link
                href={`/a/${item.itemType}/${item.itemId}`}
                className="text-primary block hover:underline"
              >
                <div className="text-foreground space-y-1">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground md:text-base">{itemType}</p>
                    <p className="break-all pr-2 text-sm font-medium md:text-base">{itemName}</p>
                  </div>
                  <div className="flex items-center gap-2">
                    <p className="text-foreground whitespace-nowrap font-semibold">
                      {convertToDollar(item.total)}
                    </p>
                    <div className="flex items-center gap-1">
                      {hasFees && (
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Button
                                type="button"
                                variant="ghost"
                                size="icon"
                                className="h-6 w-6 rounded-full p-0"
                              >
                                <FiInfo className="text-muted-foreground h-4 w-4" />
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent side="bottom" className="w-72">
                              <div className="space-y-2 p-1">
                                <div className="text-sm font-medium">
                                  Fee Breakdown
                                </div>
                                <Separator className="my-1" />
                                {item.fees.map((fee) => (
                                  <div
                                    key={fee.entityFeeId}
                                    className="flex justify-between text-sm"
                                  >
                                    <span className="break-words pr-2">{fee.label}</span>
                                    <span className="flex-shrink-0 font-medium">
                                      {formatter.format(fee.feeAmount)}
                                    </span>
                                  </div>
                                ))}
                                <Separator className="my-1" />
                                <div className="flex justify-between text-sm font-semibold">
                                  <span>Total</span>
                                  <span>{convertToDollar(item.total)}</span>
                                </div>
                              </div>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      )}
                    </div>
                  </div>
                </div>
              </Link>
            ) : (
              <div className="text-foreground space-y-1">
                <div>
                  <p className="text-sm font-medium text-muted-foreground md:text-base">{itemType}</p>
                  <p className="break-all pr-2 text-sm font-medium md:text-base">{itemName}</p>
                </div>
                <div className="flex items-center gap-2">
                  <p className="text-foreground whitespace-nowrap font-semibold">
                    {convertToDollar(item.total)}
                  </p>
                  <div className="flex items-center gap-1">
                    {hasFees && (
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Button
                              type="button"
                              variant="ghost"
                              size="icon"
                              className="h-6 w-6 rounded-full p-0"
                            >
                              <FiInfo className="text-muted-foreground h-4 w-4" />
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent side="bottom" className="w-72">
                            <div className="space-y-2 p-1">
                              <div className="text-sm font-medium">
                                Fee Breakdown
                              </div>
                              <Separator className="my-1" />
                              {item.fees.map((fee) => (
                                <div
                                  key={fee.entityFeeId}
                                  className="flex justify-between text-sm"
                                >
                                  <span className="break-words pr-2">{fee.label}</span>
                                  <span className="flex-shrink-0 font-medium">
                                    {formatter.format(fee.feeAmount)}
                                  </span>
                                </div>
                              ))}
                              <Separator className="my-1" />
                              <div className="flex justify-between text-sm font-semibold">
                                <span>Total</span>
                                <span>{convertToDollar(item.total)}</span>
                              </div>
                            </div>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    )}
                  </div>
                </div>
              </div>
            )}
          </div>

          <div className="flex flex-shrink-0 items-center gap-2 self-center">
            <SummaryButtons item={item} />
          </div>
        </div>
      </div>
    </div>
  );
};

const SummaryButtons = ({ item }: { item: ActiveCartItem }) => {
  const { removeFromCart } = useMyCart();
  const { hasPermissions } = useMyProfile();
  const admin = hasPermissions(["super-admin"]);

  return (
    <>
      {admin && item.itemType === "license" && (
        <EditTotal
          cartItem={item}
          className="flex h-8 w-8 items-center justify-center rounded-md p-0 text-blue-500 transition-colors hover:bg-blue-100 hover:text-blue-700"
        />
      )}
      <Button
        size="icon"
        variant="ghost"
        onClick={(e) => {
          e.preventDefault();
          e.stopPropagation();
          removeFromCart(item.cartItemId);
        }}
        className="h-8 w-8 text-rose-500 hover:bg-rose-100 hover:text-rose-700"
      >
        <FiTrash2 size={16} />
      </Button>
    </>
  );
};

export default CartItems;
