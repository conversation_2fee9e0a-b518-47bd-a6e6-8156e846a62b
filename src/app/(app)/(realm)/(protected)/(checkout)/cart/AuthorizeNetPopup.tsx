import { useGetResidentToken } from "@/hooks/api/useCart";
import React, { useRef, useState, useEffect } from "react";
import { FiX } from "react-icons/fi";


const AuthorizeNetPostUrl = {
  production: 'https://accept.authorize.net/payment/payment',
  sandbox: 'https://test.authorize.net/payment/payment'
}


function AuthorizeNetPopup({ cartId, amount }: { cartId: string; amount: number }) {
  const [isPopupVisible, setIsPopupVisible] = useState(false);
  const [iframeSrc, setIframeSrc] = useState("empty.html");
  const formRef = useRef<HTMLFormElement>(null);
  const getResidentToken = useGetResidentToken();

  const handleMessage = (event: MessageEvent) => {
    // Parse the message data

    console.log(event)
    const data = typeof event.data === 'string' ? new URLSearchParams(event.data) : null;
    const action = data?.get('action');

    switch (action) {
      case 'resizeWindow':
        // Handle resize window action if needed
        break;
      case 'cancel':
        // Close the popup when cancel action is received
        console.log("test")
        closePopup();
        break;
      case 'transactResponse':
        // Handle transaction response
        console.log('Transaction Response:', data?.get('response'));
        closePopup();
        break;
      default:
        console.log('Received unhandled message type:', action);
    }
  };

  useEffect(() => {
    window.addEventListener('message', handleMessage);

    return () => {
      window.removeEventListener('message', handleMessage);
    };
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (isPopupVisible && iframeSrc !== "empty.html") {
      if (formRef.current) {
        formRef.current.submit();
      }
    }
  }, [isPopupVisible, iframeSrc]); 

  const openPopup = () => {
    getResidentToken.mutate({ cartId, amount }, {
      onSuccess: (data) => {
        const iframeCommunicatorUrl = `${window.location.origin}/iframeCommunicator.html`;
        setIframeSrc(`${AuthorizeNetPostUrl.sandbox}?iframeCommunicatorUrl=${encodeURIComponent(iframeCommunicatorUrl)}`);
        if (formRef.current) {
          formRef.current.token.value = data.token;
        }
        setIsPopupVisible(true);
      }
    });
  };
  

  const closePopup = () => {
    setIsPopupVisible(false);
    setIframeSrc("about:blank"); 
  };

  return (
    <div>
      <form
        method="post"
        action={iframeSrc} 
        id="formAuthorizeNetPopup"
        name="formAuthorizeNetPopup"
        target="iframeAuthorizeNet"
        style={{ display: 'none' }}
        ref={formRef}
      >
        <input type="hidden" id="popupToken" name="token" />
      </form>

      {isPopupVisible && (
        <div className="fixed inset-0 flex items-center justify-center bg-gray-500 bg-opacity-50">
          <div className="w-full max-w-lg overflow-hidden rounded-lg bg-white shadow-xl transform transition-all">
            <div className="p-4 bg-gray-200 flex justify-between text-lg">
              <h2 className="font-bold">Make a Payment</h2>
              <button
                onClick={closePopup}
                className="p-1 font-bold text-gray-800  hover:text-red-700"
              >
                <FiX className="text-lg font-semibold"/>
              </button>
            </div>
            <div className="p-4">
              <iframe
                name="iframeAuthorizeNet"
                src={iframeSrc} 
                className="w-full h-96 border-0" // Use Tailwind CSS for border removal
                style={{ overflow: 'hidden' }} // Inline style for overflow, alternatively use Tailwind if possible
              ></iframe>
            </div>
            <div className="p-4 bg-gray-200 flex justify-end">
              Powered by Authorize.net
            </div>
          </div>
        </div>
      )}

      <button
        className="px-4 py-2 font-bold text-white bg-blue-500 rounded hover:bg-blue-700"
        onClick={openPopup}
      >
        Open AuthorizeNetPopup
      </button>
    </div>
  );
}

export default AuthorizeNetPopup;
