"use client";

import { useEffect } from "react";
import PageContainer from "@/components/ui/Page/PageContainer";
import { CgCreditCard } from "react-icons/cg";
import { AiOutlineBank } from "react-icons/ai";
import { BsCash } from "react-icons/bs";
import { useFormContext } from "react-hook-form";
import { ErrorMessage } from "@hookform/error-message";
import { LiaMoneyCheckSolid } from "react-icons/lia";
import { useMyProfile } from "@/hooks/providers/useMyProfile";

const FormSection = ({ children }: { children: React.ReactNode }) => {
  return <div className="mb-4 flex flex-wrap">{children}</div>;
};

const FormSectionLabel = ({ children }: { children: React.ReactNode }) => {
  return <h3 className="text-lg font-bold ">{children}</h3>;
};

const PaymentOptions: { [key: string]: any } = {
  resident: [
    {
      key: "card",
      name: "Card",
      icon: <CgCreditCard />,
      isDisabled: false,
    },
  ],
  clerk: [
    {
      key: "card",
      name: "Card",
      icon: <CgCreditCard />,
      isDisabled: false,
    },
    {
      key: "cash",
      name: "Cash",
      icon: <BsCash />,
      isDisabled: false,
    },
    {
      key: "personalCheck",
      name: "Personal Check",
      icon: <LiaMoneyCheckSolid />,
      isDisabled: false,
    },
    {
      key: "certifiedCheck",
      name: "Certified Check",
      icon: <LiaMoneyCheckSolid />,
      isDisabled: false,
    },
    {
      key: "moneyOrder",
      name: "Money Order",
      icon: <AiOutlineBank />,
      isDisabled: false,
    },
    // {
    //   key: "bankACH",
    //   name: "ACH Bank",
    //   icon: <AiOutlineBank />,
    //   isDisabled: true,
    // },
    // {
    //   key: "paypal",
    //   name: "Paypal",
    //   icon: (
    //     <Image
    //       src="/images/cardicons/paypal.svg"
    //       width={40}
    //       height={40}
    //       alt="paypal"
    //     />
    //   ),
    //   isDisabled: true,
    // },
  ],
};

const PaymentForm = () => {
  const {
    register,
    setValue,
    watch,
    formState: { errors },
  } = useFormContext();

  const { hasPermissions } = useMyProfile();
  const permitted = hasPermissions(["super-admin"]);

  

  const paymentMethod = watch("paymentMethod");

  const handlePaymentMethodChange = (newMethod: string) => {
    setValue("paymentMethod", newMethod, {
      shouldValidate: true,
      shouldDirty: true,
    });
  };

  useEffect(() => {
    register("paymentMethod", { required: "Payment Method is required." });

    if (!permitted) {
      setValue("paymentMethod", "card", {
        shouldValidate: true,
        shouldDirty: true,
      });
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [register, setValue]);

  
  const paymentOptions = permitted ? PaymentOptions.clerk : PaymentOptions.resident;

  return (
    <div className="flex w-full flex-col gap-4">
      <PageContainer className="w-full">
        <FormSectionLabel>
          Payment <span className="text-red-500">*</span>
        </FormSectionLabel>
        <FormSection>
          <div className="mt-2 grid grid-cols-2 lg:grid-col-3 2xl:grid-cols-5 w-full gap-4">
            {paymentOptions.map((method: any) => (
              <button
                type="button"
                key={method.key}
                onClick={() => handlePaymentMethodChange(method.key)}
                className={`
                    flex w-full flex-col items-start gap-2 rounded border p-4
                    ${method.isDisabled ? "cursor-not-allowed opacity-50" : ""}
                    ${
                      paymentMethod === method.key
                        ? "border-sky-500 text-sky-500"
                        : "text-neutral-600"
                    }
                  `}
                disabled={method.isDisabled}
              >
                <span className="text-2xl">{method.icon}</span>
                <span className="text-left font-semibold">{method.name}</span>
                {method.isDisabled && <small>Unavailable</small>}
              </button>
            ))}
          </div>

          <div className="mt-4">
            {(paymentMethod === "personalCheck" ||
              paymentMethod === "certifiedCheck") && (
              <input
                type="text"
                placeholder="Check Number"
                className="w-full rounded border p-4"
                {...register("paymentReference", {
                  required: "Check Number is required.",
                })}
              />
            )}
            {paymentMethod === "moneyOrder" && (
              <input
                type="text"
                placeholder="Money Order Number"
                className="w-full rounded border p-4"
                {...register("paymentReference", {
                  required: "Money Order Number is required.",
                })}
              />
            )}

            {/* {paymentMethod === "card" && (
              <CreditCard cardInfo={null} setCardInfo={null} />
            )} */}
          </div>

          <ErrorMessage
            errors={errors}
            name={"paymentMethod"}
            render={({ message }) => (
              <p className="mt-1 text-xs text-red-500">{message}</p>
            )}
          />
        </FormSection>
      </PageContainer>
    </div>
  );
};

export default PaymentForm;
