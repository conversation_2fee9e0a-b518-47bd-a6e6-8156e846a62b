"use client";
import React from "react";

// Config
import config from "@/components/formBuilderV2/forms/newResidentConfig.json";
import { useRouter, useSearchParams } from "next/navigation";
import Machine from "@/components/formBuilderV2/components/Machine";
import PageError from "../PageError";
import { useMyProfile } from "@/hooks/providers/useMyProfile";
import Loading from "@/app/(app)/loading";

const RegisterPage = () => {
  const router = useRouter();
  const registrationCode = useSearchParams().get("registrationCode") as string;

  const { profileIsError, profileIsLoading, profile } = useMyProfile();

  if (profileIsError) {
    return <PageError />;
  }

  if (profileIsLoading) {
    return <Loading text={"Loading..."} />;
  }

  if (profile) {
    if (profile.registered) router.push(`/home`);

    const primaryEmail = profile.individual.contacts.find(
      (contact: any) => contact.type === "Email" && contact.group === "Primary",
    );

    const primaryPhone = profile.individual.contacts.find(
      (contact: any) => contact.type === "Phone" && contact.group === "Home",
    );

    // CabsFZ48Ku8g
    console.log(registrationCode);
    const additionalContext = {
      entityType: "individual",
      entityId: profile.individual.entityId,
      form: {
        main: {
          phone: primaryPhone?.value || null,
          email: primaryEmail?.value || null,
          registrationCode: registrationCode?.length ? registrationCode : "",
          hasRegistrationCode: registrationCode ? "yes" : null,
        },
      },
    };

    return <Machine config={config} additionalContext={additionalContext} />;
  }

  return <div>No User Found</div>;
};

export default RegisterPage;
