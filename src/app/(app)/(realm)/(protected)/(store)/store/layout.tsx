"use client";
import UnauthorizedPage from "@/components/access/UnauthorizedPage";
import { useMyProfile } from "@/hooks/providers/useMyProfile";
import React from "react";

export default function StoreLayoutPermissions({
  children,
}: {
  children: React.ReactNode;
}) {
  const { hasPermissions } = useMyProfile();

  if (!hasPermissions(["super-admin"])) {
    return <UnauthorizedPage />;
  }

  return <>{children}</>;
}
