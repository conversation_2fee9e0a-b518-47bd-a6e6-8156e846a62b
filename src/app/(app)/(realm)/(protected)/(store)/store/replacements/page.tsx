"use client";
import PageContainer from "@/components/ui/Page/PageContainer";
import Button from "@/components/ui/buttons/Button";
import { useGetJSONStorage, useUpdateJSONStorage } from "@/hooks/api/useAdmin";
import { useMyProfile } from "@/hooks/providers/useMyProfile";
import { useMyCart } from "@/hooks/useMyCart";
import Image from "next/image";
import React, { useState } from "react";
import MonacoEditor from "@monaco-editor/react";
import { toastAtom } from "@/components/ui/toast/toast";
import { useAtom } from "jotai";
import Loading from "@/app/(app)/loading";

type StoreItem = {
  itemId: string;
  title: string;
  description: string;
  price: string;
  salePrice?: string;
  imageURL: string; 
  banner?: string;
};

const banners: {
  [key: string]: {
    label: string;
    style: string;
  };
} = {
  limited: {
    label: "Limited Time Only",
    style: "bg-red-700 text-red-50",
  },
};

const StoreFront = () => {
  const { addToCart } = useMyCart();
  const { hasPermissions } = useMyProfile();
  const [showSettings, setShowSettings] = useState<boolean>(false);

  const {
    data: storeItems,
    isLoading,
    isFetching,
    error,
    refetch,
  } = useGetJSONStorage("store", "online");

  if (isLoading || isFetching) {
    return <Loading fixed={false}/>;
  }

  if (error) {
    return <div>Error...</div>;
  }

  console.log(storeItems)

  return (
    <div className="container mx-auto h-full overflow-hidden p-6">
      <PageContainer className="flex h-full flex-col gap-2 overflow-hidden">
        <div className="flex justify-end">
          {hasPermissions(["super-admin"]) && (
            <Button onClick={() => setShowSettings(!showSettings)}>
              {showSettings ? "Close" : "Settings"}
            </Button>
          )}
        </div>
        {!showSettings ? (
          <div>
            <div className="mb-6 rounded bg-blue-100 p-10 text-center text-4xl">
              <span className="font-bold text-blue-900">
                Your City Hall Services, Now Online
              </span>
            </div>
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
              {storeItems && Array.isArray(storeItems) && storeItems?.map((item: StoreItem) => (
                <div
                  key={item.itemId}
                  className="rounded bg-white shadow-md hover:bg-indigo-50"
                >
                  <div className="flex h-full flex-col gap-4 p-4">
                    <div className="relative h-44 w-full shrink-0 overflow-hidden rounded bg-white p-1">
                      <Image
                        src={item.imageURL ?? ""}
                        fill
                        alt={item.price}
                        style={{
                          objectFit: "contain",
                        }}
                      />
                    </div>
                    <div className="mb-auto h-full">
                      <h3 className="flex justify-between font-bold ">
                        <span className="line-clamp-2">{item.title}</span>
                      </h3>
                      <div className="line-clamp-3 text-sm text-neutral-600">
                        {item.description}
                      </div>
                      {item.banner && (
                        <div
                          className={`w-fit px-2 py-1 text-sm font-bold ${banners[item.banner].style}`}
                        >
                          {banners[item.banner].label}
                        </div>
                      )}
                      <div className="mt-1 line-clamp-1 text-lg font-semibold">
                        {item.salePrice ? (
                          <div className="line-clamp-1 flex gap-2">
                            <span className="text-neutral-400 line-through">
                              {item.price}
                            </span>
                            <span className="text-neutral-800">
                              {item.salePrice}
                            </span>
                          </div>
                        ) : (
                          <div className=" line-clamp-1 text-neutral-800 ">
                            {item.price}
                          </div>
                        )}
                      </div>
                    </div>
                    <Button
                      onClick={() => {
                        addToCart(item.itemId, "tag");
                      }}
                    >
                      Add to Cart
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        ) : (
          <StoreSettings
            showSettings={showSettings}
            setShowSettings={setShowSettings}
            currentStoreConfig={storeItems}
            refetchStore={refetch}
          />
        )}
      </PageContainer>
    </div>
  );
};

export default StoreFront;

const StoreSettings = ({
  refetchStore,
  currentStoreConfig,
  showSettings,
  setShowSettings,
}: {
  currentStoreConfig: any;
  refetchStore: () => void;
  showSettings: boolean;
  setShowSettings: (show: boolean) => void;
}) => {
  const [config, setConfig] = useState<any>(currentStoreConfig);
  const [_, setToast] = useAtom(toastAtom);

  const mutateConfig = useUpdateJSONStorage("store", "online");

  const updateConfigButton = () => {
    mutateConfig.mutate(config, {
      onSuccess: () => {
        refetchStore();
        setToast({
          message: "Store Config Updated",
          label: "success",
          status: "success",
        });
      },
      onError: (error) => {
        setToast({
          message: "Error Updating Store Config",
          label: "error",
          status: "error",
        });
      },
    });
  };

  return (
    <div className="flex h-full w-full flex-col gap-4 overflow-hidden">
      <div className="h-full w-full">
        <MonacoEditor
          key={"store"}
          height="100%"
          language="json"
          theme="vs-dark"
          value={JSON.stringify(config, null, 2)}
          onChange={(storeItems: any) => {
            setConfig(JSON.parse(storeItems as string));
          }}
          options={{
            selectOnLineNumbers: true,
            automaticLayout: true,
          }}
        />
      </div>
      <div className="flex w-full items-end">
        <Button onClick={updateConfigButton}>Save</Button>
      </div>
    </div>
  );
};
