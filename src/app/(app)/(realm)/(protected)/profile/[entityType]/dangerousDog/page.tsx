"use client";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import { useSearchParams, useRouter, useParams } from "next/navigation";

type DangerousDogProps = {};

const DangerousDog = ({}: DangerousDogProps) => {
  
  const qParams = useSearchParams();

  const entityType = qParams.get("entityType");
  const entityId = qParams.get("entityId");

  const { push } = useRouter();

  return (
    <AlertDialog>
      <AlertDialogTrigger asChild>
        <Button variant="outline">Show Dialog</Button>
      </AlertDialogTrigger>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>
            Are you sure you want to mark this dog as dangerous?
          </AlertDialogTitle>
          <AlertDialogDescription>
            This action will mark this dog as dangerous and will require the
            owner to obtain a dangerous dog license.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel
            onClick={() => push(`/entity/${entityType}/${entityId}`)}
          >
            Cancel
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={() => console.log("d-dog")}
            className="bg-red-500 hover:bg-red-600"
          >
            Continue
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};

export default DangerousDog;
