export type Address = {
  address: string;
  address2: string | null;
  houseNo: string;
  road: string;
  addressType: string;
  entityId?: string;
  houseNumber?: string;
  streetName?: string;
  streetAddress?: string;
  streetAddress2?: string | null;
  town?: string | null;
  city?: string;
  state?: string;
  zip?: string;
  fullAddress?: string;
  latitude?: number;
  longitude?: number;
};

export type Owner = {
  firstName: string;
  lastName: string;
  dateOfBirth: string;
  address: Address;
  phone: string;
  email: string;
  entityId: string;
  contacts: {
    type: string
    group: string
    value: string
  }[]
};

export type Dog = {
  entityId: string;
  dogName: string;
  tagNumber: string;
  dogBreed: string;
  dogBirthDate: string;
  dogSex: string;
  dogSpayedOrNeutered?: string;
  dogPrimaryColor: string;
  dogSecondaryColor: string;
  microchipNumber: string;
  licenseExemptionDocumentsProvided?: string;
  dogBio: string;
  dogMarkings: string;
  catFriendly: boolean;
  dogFriendly: boolean;
  childFriendly: boolean;
  veterinaryName: string;
  veterinarianName: string;
  rabiesTagNumber: string;
  vaccineName: string;
  vaccineProducer: string;
  vaccineAdministeredDate: string;
  vaccineDueDate: string;
  vaccineLotNumber: string;
  vaccinePeriod: string;
  insuranceStartDate: string;
  insuranceExpirationDate: string;
  insuranceProvider: string;
  insurancePolicyNumber: string;
};

export type LicenseType = {
  code?: string, 
  name?: string, 
  groupName?: string
}

export type License = {
  actions?: string[];
  licenseId: number;
  entityId: string;
  licenseNumber: string;
  validFromDate: string;
  validToDate: string;
  licenseIssuedDate: string;
  licenseStatus?: string;
  status?: string;
  licenseType?: LicenseType;
  licenseTypeCode?: string;
  licenseTypeGroup?: string;
  licenseForm?: string | null
  licenseDocumentUrl?: string | null | undefined;
  type: string
  dog?: Dog;
  owner?: Owner;
  vaccineDatesExempt?: string;
  Address?: Address;
  activityType?: string;
};
