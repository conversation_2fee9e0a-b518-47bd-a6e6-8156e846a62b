import React, { useState } from "react";
import { FiChevronDown, FiChevronRight } from "react-icons/fi";

const ContactGroup = ({
  title,
  children,
}: {
  title: string;
  children: React.ReactNode;
}) => {
  const [isOpen, setIsOpen] = useState(true); // default to open

  return (
    <div className="">
      <h2
        className={`
          justify-between flex items-center
          text-black text-sm font-medium bg-slate-200 py-1 px-2 cursor-pointer 
          ${isOpen ? "rounded-t" : "rounded"}`}
        onClick={() => setIsOpen(!isOpen)}
      >
        {title} {isOpen ? <FiChevronDown className="inline-block text-lg" /> : <FiChevronRight className="inline-block text-lg" />}
      </h2>
      {isOpen && children}
    </div>
  );
};

export default ContactGroup;
