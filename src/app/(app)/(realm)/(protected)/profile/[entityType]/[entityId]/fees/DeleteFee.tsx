import { But<PERSON> } from "@/components/ui/button";
import { Trash } from "lucide-react";
import { Fee, FeeSet, useDeleteFee } from "@/hooks/api/useFees";
import { useAtom } from "jotai";
import { toastAtom } from "@/components/ui/toast/toast";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { useMyCart } from "@/hooks/useMyCart";

interface DeleteFeeProps {
  fee: FeeSet | Fee;
  refetch: () => void;
  setFees?: (updater: (prev: any) => any) => void;
  setFee?: (fee: Fee | null) => void;
  icon?: boolean;
}

const DeleteFee = ({
  fee,
  refetch,
  setFees,
  setFee,
  icon = false,
}: DeleteFeeProps) => {
  const { cartRefetch } = useMyCart();
  const deleteFeeMutation = useDeleteFee();
  const [_, setToast] = useAtom(toastAtom);

  const onDelete = () => {
    deleteFeeMutation.mutate(
      {
        feeEntityId: fee.entityId,
        feeEntityType: fee.entityType,
      },
      {
        onSuccess: () => {
          setToast({
            label: "Fee deleted",
            status: "success",
            message: "The fee was deleted successfully",
          });
          refetch();
          cartRefetch();

          if (fee.entityType === "feeSet" && setFees) {
            setFees((prev: any) =>
              prev.filter((f: any) => f.entityId !== fee.entityId),
            );
          } else if (fee.entityType === "fee" && setFee) {
            setFee(null);
          }
        },
        onError: (error: any) => {
          console.log(error);
          setToast({
            label: "Error",
            status: "error",
            message: error?.message ?? "Failed to delete fee",
          });
        },
      },
    );
  };

  return (
    <AlertDialog>
      <AlertDialogTrigger asChild>
        <Button
          variant="ghost"
          size="sm"
          className="flex items-center gap-2 text-rose-500 hover:bg-rose-100 hover:text-rose-700"
        >
          <Trash size={16} />
          {!icon && (
            <span className="hidden sm:inline">
              Delete {fee.entityType === "feeSet" ? "Fee Set" : "Fee"}
            </span>
          )}
        </Button>
      </AlertDialogTrigger>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>
            Are you absolutely sure you want to delete this{" "}
            {fee.entityType === "feeSet" ? "fee set" : "fee"}?
          </AlertDialogTitle>
          <AlertDialogDescription>
            This action cannot be undone. This will permanently delete the{" "}
            {fee.entityType === "feeSet" ? "fee set" : "fee"}.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>Cancel</AlertDialogCancel>
          <AlertDialogAction onClick={onDelete}>
            Delete {fee.entityType === "feeSet" ? "Fee Set" : "Fee"}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};

export default DeleteFee;
