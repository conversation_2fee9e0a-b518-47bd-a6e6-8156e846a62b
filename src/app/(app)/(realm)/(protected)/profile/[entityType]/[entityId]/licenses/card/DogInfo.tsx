import React from "react";

import Image from "next/image";
import { BiHash } from "react-icons/bi";
import { useRouter, useParams } from "next/navigation";
import AvatarImage, { getAvatarBlob } from "@/components/AvatarImage";
import { Dog } from "@/types/DogType";

export default function DogInfo({ dog }: { dog: Dog }) {
  console.log(dog);
  const router = useRouter();
  

  return (
    <>
      <button
        className="mt-2 flex w-full flex-col gap-2 p-2 hover:bg-clerk-primary/10 hover:shadow lg:flex-row"
        onClick={() => {
          router.push(`/profile/dog/${dog.entityId}?tab=profile`);
        }}
      >
        <AvatarImage
          entityType="dog"
          src={getAvatarBlob(dog?.documents) as string}
          width={60}
          height={60}
          alt="dog"
          className="rounded"
        />
        <div>
          <p className="text-left text-lg font-bold">
            {dog.dogName} ({dog.dogBreed})
          </p>
          <p className="flex items-center text-sm text-neutral-600">
            Tag #: {dog.tagNumber}
          </p>
        </div>
        <div></div>
      </button>
    </>
  );
}
