"use client";

import { useState } from "react";
import { useEntity } from "@/hooks/providers/useEntity";
import { Switch } from "@/components/ui/switch";
import { useHandleEvent } from "@/hooks/api/useProfiles";
import { useAtom } from "jotai";
import { toastAtom } from "@/components/ui/toast/toast";

export default function RedemptionDog() {
  const { entity, entityType, entityRefetch } = useEntity();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const mutateEvent = useHandleEvent();
  const [_, setToast] = useAtom(toastAtom);

  console.log(entity[entityType].entityId);

  const handleSwitchChange = (checked: boolean) => {
    mutateEvent.mutate(
      {
        entityId: entity[entityType].entityId,
        eventType: "dogImpounded",
        entityType,
        body: new FormData(),
      },
      {
        onSuccess: () => {
          setToast({
            status: "success",
            label: "Dog Found",
            message: "Successfully marked dog as found",
          });
          entityRefetch();
          setIsModalOpen(checked);

        },
        onError: (error: any) => {
          setToast({
            status: "error",
            label: "Error Updating Dog Status",
            message: error.message,
          });
        },
      },
    );
  };

  return (
    <div className="flex items-center gap-4">
      <p className="w-[180px] font-semibold">Dog Impounded</p>
      <Switch checked={isModalOpen} onCheckedChange={handleSwitchChange} />
      {/* <FeeModal
        isOpen={isModalOpen}
        onClose={() => {
          setIsModalOpen(false)
          entityRefetch();
        }}
        entity={entity}
        entityType={entityType}
        defaultGroup="dogImpoundment"
        title="Dog Impoundment Fee"
        groupLock
      /> */}
    </div>
  );
}
