/* eslint-disable @next/next/no-img-element */
import { useState } from "react";
import { Document as PDFDocument, Page } from "react-pdf";
import {
  FileText,
  XCircle,
  Download,
  FileImage,
  Paperclip,
  FileSpreadsheet,
  FileType,
  ChevronDown,
  ChevronUp,
  Loader2,
} from "lucide-react";
// Using DocumentType alias to avoid conflict with DOM's document
import { Document as DocumentType } from "@/types/DocumentType";
import { useGetDocumentBlob } from "@/hooks/api/useServices";
import { Button } from "@/components/ui/button";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import { cn } from "@/lib/utils";

// Helper function to get file icon based on MIME type
const getFileIcon = (mimeType: string) => {
  if (mimeType.startsWith("image/")) {
    return <FileImage className="text-blue-500" size={20} />;
  } else if (mimeType === "application/pdf") {
    return <Paperclip className="text-red-500" size={20} />;
  } else if (
    mimeType === "application/vnd.ms-excel" ||
    mimeType === "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
  ) {
    return <FileSpreadsheet className="text-green-500" size={20} />;
  } else {
    return <FileType className="text-gray-500" size={20} />;
  }
};

export const FeeDocuments = ({ documents }: { documents: DocumentType[] }) => {
  if (!documents || documents.length === 0) {
    return null;
  }

  return (
    <div className="mt-6">
      <h3 className="mb-3 flex items-center gap-2 text-lg font-semibold">
        <FileText className="text-muted-foreground" size={18} />
        Documents
      </h3>
      <div className="space-y-2">
        {documents.map((doc) => (
          <FeeDocument key={doc.documentUuid} document={doc} />
        ))}
      </div>
    </div>
  );
};

export const FeeDocument = ({ document }: { document: DocumentType }) => {
  const [isOpen, setIsOpen] = useState(false);
  const { data, isLoading, isError } = useGetDocumentBlob(
    document.documentUuid,
    true
  );

  // Create object URL for the blob
  const objectUrl = data ? URL.createObjectURL(data) : null;

  // Handle downloading the file
  const handleDownload = () => {
    if (!data) return;
    
    // Create a temporary anchor element - using window.document to avoid naming conflict
    const a = window.document.createElement("a");
    a.href = objectUrl || "";
    a.download = document.name || "download";
    window.document.body.appendChild(a);
    a.click();
    window.document.body.removeChild(a);
  };

  if (isLoading) {
    return (
      <div className="flex items-center gap-2 rounded-md border border-muted bg-muted/20 p-3">
        <Loader2 className="animate-spin text-muted-foreground" size={20} />
        <span className="text-muted-foreground">Loading document...</span>
      </div>
    );
  }

  if (!data || isError) {
    return (
      <div className="flex items-center gap-2 rounded-md border border-rose-200 bg-rose-50 p-3">
        <XCircle className="text-rose-500" size={20} />
        <span className="text-rose-500">Error loading document</span>
      </div>
    );
  }

  const isPDF = data.type === "application/pdf";
  const isImage = data.type.startsWith("image/");
  const canPreview = isPDF || isImage;

  return (
    <Collapsible
      open={isOpen}
      onOpenChange={setIsOpen}
      className="rounded-md border border-muted bg-card shadow-sm"
    >
      <div className="flex items-center justify-between p-3">
        <div className="flex items-center gap-2">
          {getFileIcon(data.type)}
          <span className="font-medium">{document.name}</span>
          <span className="text-xs text-muted-foreground">
            ({(data.size / 1024).toFixed(0)} KB)
          </span>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleDownload}
            className="flex items-center gap-1"
          >
            <Download size={16} />
            <span className="hidden sm:inline">Download</span>
          </Button>
          {canPreview && (
            <CollapsibleTrigger asChild>
              <Button variant="ghost" size="sm">
                {isOpen ? <ChevronUp size={18} /> : <ChevronDown size={18} />}
              </Button>
            </CollapsibleTrigger>
          )}
        </div>
      </div>

      {canPreview && (
        <CollapsibleContent>
          <div className="border-t border-muted p-3">
            {isImage && (
              <div className="flex justify-center">
                <img
                  src={objectUrl || ""}
                  alt={document.name}
                  className="max-h-96 rounded-md object-contain"
                />
              </div>
            )}
            {isPDF && (
              <PDFPreview url={objectUrl || ""} documentName={document.name} />
            )}
          </div>
        </CollapsibleContent>
      )}
    </Collapsible>
  );
};

// PDF Preview component with page navigation
const PDFPreview = ({ url, documentName }: { url: string; documentName: string }) => {
  const [numPages, setNumPages] = useState<number | null>(null);
  const [pageNumber, setPageNumber] = useState(1);
  const [isLoading, setIsLoading] = useState(true);

  const onDocumentLoadSuccess = ({ numPages }: { numPages: number }) => {
    setNumPages(numPages);
    setIsLoading(false);
  };

  return (
    <div className="flex flex-col items-center">
      {isLoading && (
        <div className="my-8 flex items-center justify-center">
          <Loader2 className="mr-2 h-6 w-6 animate-spin text-muted-foreground" />
          <span>Loading PDF...</span>
        </div>
      )}
      
      <div className={cn("max-h-96 overflow-auto", isLoading ? "hidden" : "")}>
        <PDFDocument
          file={url}
          onLoadSuccess={onDocumentLoadSuccess}
          loading={
            <div className="my-8 flex items-center justify-center">
              <Loader2 className="mr-2 h-6 w-6 animate-spin text-muted-foreground" />
              <span>Loading PDF...</span>
            </div>
          }
        >
          <Page pageNumber={pageNumber} width={550} />
        </PDFDocument>
      </div>
      
      {numPages && numPages > 1 && (
        <div className="mt-3 flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setPageNumber(Math.max(1, pageNumber - 1))}
            disabled={pageNumber <= 1}
          >
            Previous
          </Button>
          <span className="text-sm">
            Page {pageNumber} of {numPages}
          </span>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setPageNumber(Math.min(numPages, pageNumber + 1))}
            disabled={pageNumber >= numPages}
          >
            Next
          </Button>
        </div>
      )}
    </div>
  );
};