import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  <PERSON><PERSON>Title,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { useGlobalSearch } from "@/hooks/api/useSearch";
import { ArrowRight } from "lucide-react";
import { cn } from "@/lib/utils";
import { useGetEntity } from "@/hooks/api/useProfiles";
import { useEntity } from "@/hooks/providers/useEntity";
import { TbFidgetSpinner } from "react-icons/tb";
import { useApproveMergeRequest } from "@/hooks/api/useApprovals";
import { useAtom } from "jotai";
import { toastAtom } from "@/components/ui/toast/toast";
import { Dog } from "@/types/DogType";
import { License } from "@/types/LicenseType";
import { Address, Contact } from "@/types/IndividualType";



const MergeAccount = () => {
  const [open, setOpen] = useState(false);
  const [mergeWarning, setMergeWarning] = useState(false);
  const [searchInput, setSearchInput] = useState("");
  const [searchResults, setSearchResults] = useState<any[] | null>(null);
  const [currentUserId, setCurrentUserId] = useState<any | null>(null);
  const searchMutation = useGlobalSearch();
  const { entityId } = useEntity();

  const approveMutate = useApproveMergeRequest();
  const [_, setToast] = useAtom(toastAtom);

  const onCancel = () => {
    setOpen(false);
    setMergeWarning(false);
    setSearchInput("");
    setSearchResults(null);
    setCurrentUserId(null);
  };

  const handleSearch = () => {
    setCurrentUserId(null);
    searchMutation.mutate(searchInput, {
      onSuccess: (data) => {
        if (data) {
          console.log(data);
          const filtered = data.items.filter(
            (item: any) => item.entityId !== entityId,
          );
          console.log(filtered);
          setSearchResults(filtered);
        }
      },
      onError: (error) => {
        console.log(error);
      },
    });
  };

  const onSubmit = () => {
    if (currentUserId) {
      console.log(currentUserId);
      console.log(entityId);
      approveMutate.mutate(
        {
          requestedUserId: entityId,
          existingUserIds: [currentUserId],
        },
        {
          onSuccess: () => {
            setToast({
              status: "success",
              label: "License Approved",
              message: `Merge request has been approved successfully`,
            });
            onCancel();
          },
          onError: (error: any) => {
            setToast({
              status: "error",
              label: "Error approving merge request",
              message: error.message,
            });
          },
        },
      );
    }
  };

  return (
    <Dialog
      open={open}
      onOpenChange={() => {
        if (open) {
          onCancel();
        } else {
          setOpen(true);
        }
      }}
    >
      <div className="flex items-center gap-4">
        <p className="w-full max-w-[180px] font-semibold">Merge Accounts</p>
        <DialogTrigger asChild className="w-[180px]">
          <Button variant="outline">Lookup</Button>
        </DialogTrigger>
      </div>
      {!mergeWarning ? (
        // Merge Content
        <DialogContent
          className={cn(
            "flex w-full flex-col",
            searchResults ? "h-full max-h-[90vh] max-w-[90vw]" : "h-fit",
          )}
        >
          <DialogHeader>
            <DialogTitle>Merge</DialogTitle>
            <DialogDescription>
              Lookup an account you wish to merge with this one.
            </DialogDescription>

            <div className="mt-10">
              <div className="mt-6 flex items-center gap-2">
                <Input
                  autoComplete="off"
                  id="search"
                  type="text"
                  className="max-w-sm"
                  value={searchInput}
                  onChange={(e) => setSearchInput(e.target.value)}
                  placeholder="Search for a user"
                />
                <Button variant="primary" type="submit" onClick={handleSearch}>
                  Search
                </Button>
              </div>
            </div>
          </DialogHeader>
          {searchResults && searchResults.length > 0 && (
            <>
              <div className="flex h-full flex-col overflow-hidden">
                <div className="font-semibold">
                  Found User Accounts: {searchResults?.length} Accounts
                </div>

                <div className="flex h-full gap-3 overflow-hidden">
                  <div className="flex h-full w-full flex-col overflow-hidden rounded border border-neutral-500 shadow">
                    {searchMutation.isLoading ? (
                      <div className="flex items-center gap-2 p-4 text-xl">
                        <TbFidgetSpinner size={20} className="animate-spin" />{" "}
                        Loading...
                      </div>
                    ) : (
                      <div className="flex gap-4 overflow-hidden">
                        {/* Sidebar */}
                        <div className="flex h-full w-[200px] shrink-0 flex-col gap-3 overflow-auto p-2">
                          {searchResults?.map((result) => {
                            const phoneNumber = result.thirdDisplay?.find(
                              (item: any) => item.type === "phone",
                            ) ?? { value: "No Phone" };
                            const email = result.thirdDisplay?.find(
                              (item: any) => item.type === "email",
                            ) ?? { value: "No Email" };

                            const active = currentUserId === result.entityId;

                            return (
                              <button
                                key={result.entityId}
                                className={cn(
                                  "flex flex-col rounded border p-1 text-sm hover:bg-neutral-100",
                                  active && "border-blue-600 bg-blue-100",
                                )}
                                onClick={() => {
                                  setCurrentUserId(result.entityId);
                                }}
                              >
                                <div className="text-wrap text-left font-semibold">
                                  {result?.primaryDisplay ?? "No Data"}
                                </div>
                                <div className="text-wrap text-left">
                                  {result?.secondaryDisplay ?? "No Data"}
                                </div>
                                <div className="break-all text-left">
                                  {phoneNumber.value}
                                </div>
                                <div className="break-all text-left">
                                  {email.value}
                                </div>
                              </button>
                            );
                          })}
                        </div>
                        {currentUserId && (
                          <div className="flex h-full w-full flex-col overflow-auto py-2">
                            <UserAccount individualId={currentUserId} />
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                  <div className="flex h-full items-center ">
                    <ArrowRight size={24} />
                  </div>
                  <div className="flex h-full w-full flex-col overflow-auto rounded border border-green-500 p-3">
                    <CurrentAccount />
                  </div>
                </div>
              </div>
              <DialogFooter>
                <Button
                  type="button"
                  onClick={() => {
                    setMergeWarning(true);
                  }}
                  disabled={!currentUserId}
                >
                  Merge
                </Button>
              </DialogFooter>
            </>
          )}
          {searchResults && searchResults.length === 0 && (
            <div className="flex items-center gap-2 p-4 text-xl">
              No Results Found
            </div>
          )}

          {searchMutation.isLoading && (
            <div className="flex items-center gap-2 p-4 text-xl">
              <TbFidgetSpinner size={20} className="animate-spin" /> Loading...
            </div>
          )}
        </DialogContent>
      ) : (
        // Merge Warning
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Are you absolutely sure?</DialogTitle>
            <DialogDescription>
              This will merge the selected account with the current account.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setMergeWarning(false)}>
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={() => {
                onSubmit();
              }}
            >
              Merge
            </Button>
          </DialogFooter>
        </DialogContent>
      )}
    </Dialog>
  );
};

export default MergeAccount;

const UserAccount = ({ individualId }: { individualId: string }) => {
  const { data, isError, isLoading, isFetching } = useGetEntity(
    "individual",
    individualId,
  );

  console.log(isError);
  console.log(data);

  if (isLoading || isFetching) return <div>Loading...</div>;

  if (data) {
    return <DisplayAccount entity={data} />;
  }

  return null;
};

const CurrentAccount = () => {
  const { entity } = useEntity();
  return <DisplayAccount entity={entity} />;
};

const DisplayAccount = ({ entity }: any) => {
  const user = entity["individual"];

  const addresses: Address[] = user.addresses ?? [];
  const contacts: Contact[] = user.contacts ?? [];

  // const homePhone = contacts.find((contact) => contact.type === "Phone");
  const licenses: License[] = entity.license ?? [];

  const dog: Dog[] = entity.dog ?? [];

  console.log(dog);

  return (
    <>
      <div className="flex w-full flex-col gap-1">
        <h3 className="mt-2 text-lg font-semibold">Profile Information</h3>
        <div>
          {user.firstName} {user.lastName}
        </div>
        <h3 className="mt-10 text-lg font-semibold">Address Information</h3>
        {addresses.length > 0 ? (
          user.addresses.map((address: any) => {
            return (
              <div key={address.id} className="mt-4">
                <div className="font-semibold">
                  {address.participantAddressType}
                </div>
                <div>{address.streetName}</div>
                <div>
                  {address.city}, {address.state} {address.zip}
                </div>
              </div>
            );
          })
        ) : (
          <div>No Address</div>
        )}
      </div>

      <h3 className="mt-10 text-lg font-semibold">Contact Information</h3>
      {contacts.length > 0 ? (
        contacts.map((contact: any) => {
          return (
            <div key={contact.id} className="mt-4">
              <div className="font-base">{contact.group}</div>
              <div>{contact.value}</div>
            </div>
          );
        })
      ) : (
        <div>No Contact Information</div>
      )}

      {/* Licenses */}
      <h3 className="mt-10 flex items-center gap-2 text-lg font-semibold">
        Licenses
        <span className="text-xs">(Qty {licenses.length})</span>
      </h3>
      {licenses.length > 0 ? (
        licenses.map((license: License) => {
          return (
            <div key={license.entityId} className="mt-4">
              <div className="font-semibold">{license.licenseType.name}</div>
              <div className="text-sm">Lic #: {license.licenseNumber}</div>
              <div className="text-sm">Status: {license.status}</div>
            </div>
          );
        })
      ) : (
        <div>No License Information</div>
      )}

      {/* Dog Info */}
      <h3 className="mt-10 flex items-center gap-2 text-lg font-semibold">
        Dog
        <span className="text-xs">(Qty 1)</span>
      </h3>
      {dog?.length > 0 ? (
        dog.map((dog: Dog) => {
          console.log(dog);
          return (
            <div key={dog.entityId} className="mt-4">
              <div className="font-semibold">
                Name: {dog.dogName ?? "No Name"}
              </div>
              <div className="text-sm">Tag #: {dog.tagNumber ?? "No Tag"}</div>
              <div className="text-sm">Breed: {dog.dogBreed ?? "Uknown"}</div>
            </div>
          );
        })
      ) : (
        <div>No Dog Information</div>
      )}
    </>
  );
};
