"use client";
import MainEntityLayout from "./MainEntityLayout";
import { EntityProvider, useEntity } from "@/hooks/providers/useEntity";
import Loading from "@/app/(app)/loading";
import ErrorPage from "@/components/error/ErrorPage";

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <EntityProvider>
      <RenderProfile>{children}</RenderProfile>
    </EntityProvider>
  );
}

const RenderProfile = ({ children }: { children: React.ReactNode }) => {
  const { entityId, entityType, entity, entityIsError, entityIsLoading } =
    useEntity();

  if (entityIsLoading)
    return <Loading text={"Loading Profile"} fixed={false} />;
  if (entityIsError) return <ErrorPage message={"Error Loading Profile"} />;

  const mainLayouts = ["individual", "dog"];

  if (entity) {
    if (mainLayouts.includes(entityType)) {
      return (
        <MainEntityLayout
          entity={entity}
          entityId={entityId}
          entityType={entityType}
        >
          {children}
        </MainEntityLayout>
      );
    }
  }

  return null;
};
