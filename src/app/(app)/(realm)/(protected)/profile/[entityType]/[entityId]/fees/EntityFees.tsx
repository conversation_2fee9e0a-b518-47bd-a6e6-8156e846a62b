import { useEntity } from "@/hooks/providers/useEntity";
import FeeCard from "./FeeCard";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { useEffect, useState } from "react";
import { AlertCircle, CreditCard, Eye, FileText, Loader2, Plus } from "lucide-react";
import { useMyProfile } from "@/hooks/providers/useMyProfile";
import { FeeModal } from "@/components/fees/modal/FeeModal";
import { Button } from "@/components/ui/button";
import { FeeSet, ProfileFees } from "@/hooks/api/useFees";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { EntityRefreshingModal } from "./EntityRefreshingModal";

export default function EntityFees({
  entity,
  entityType,
}: {
  entity: any;
  entityType: string;
}) {
  const currentEntity = entity[entityType];

  const fees: ProfileFees = currentEntity?.fees ?? [];
  const { hasPermissions } = useMyProfile();
  const { entityRefetch, entityIsFetching } = useEntity();
  const active = currentEntity?.active;

  const [isModalOpen, setIsModalOpen] = useState(false);

  const feeSets = fees?.feeSets ?? [];
  const totals = fees?.totals ?? null;

  return (
    <div className="lg:container space-y-6 lg:py-6 p-3">
      {/* Loading Modal */}
      <EntityRefreshingModal isRefreshing={entityIsFetching} />
      
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Account Fees</h1>
          <p className="text-muted-foreground">View and manage your account fees.</p>
        </div>
        
        {hasPermissions(["super-admin"]) && (
          active ? (
            <Button 
              variant="default" 
              className="flex items-center gap-2" 
              onClick={() => setIsModalOpen(true)}
            >
              <Plus size={16} />
              <span>Add Fee</span>
            </Button>
          ) : (
            <Alert variant="destructive" className="max-w-md">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Account Inactive</AlertTitle>
              <AlertDescription>
                Cannot make changes at this time.
              </AlertDescription>
            </Alert>
          )
        )}
      </div>

      {feeSets?.length === 0 ? (
        <Card className="bg-muted/30">
          <CardContent className="flex flex-col items-center justify-center p-8 text-center">
            <CreditCard className="mb-4 h-12 w-12 text-muted-foreground/70" />
            <CardTitle className="text-xl font-semibold">No Account Fees</CardTitle>
            <CardDescription className="mt-2">
              There are no fees associated with this account.
            </CardDescription>
          </CardContent>
        </Card>
      ) : (
        <Card>
          <CardHeader>
            <CardTitle>Fee Summary</CardTitle>
            <CardDescription>Overview of your account fee status</CardDescription>
          </CardHeader>
          <CardContent>
            <FeeSection
              label="Total Outstanding:"
              amount={totals?.totalOutstandingAmount || 0}
            />
          </CardContent>
        </Card>
      )}

      <FeesAccordion fees={fees.feeSets} currentEntity={currentEntity} />
      
      <FeeModal
        isOpen={isModalOpen}
        onClose={() => {
          setIsModalOpen(false);
          entityRefetch();
        }}
        entity={entity}
        entityType={entityType}
        title="Add Fee"
      />
    </div>
  );
}

const FeeList = ({
  fees,
  setFees,
  emptyMessage,
}: {
  fees: FeeSet[];
  setFees: React.Dispatch<React.SetStateAction<FeeSet[]>>;
  emptyMessage: string;
}) => {
  return fees.length > 0 ? (
    <div className="grid gap-6 md:grid-cols-1">
      {fees
        .sort((a, b) => {
          return (
            new Date(b.createdDateTime).getTime() -
            new Date(a.createdDateTime).getTime()
          );
        })
        .map((feeSet) => (
          <FeeCard
            key={feeSet.entityId}
            feeSet={feeSet}
            setFees={setFees}
          />
        ))}
    </div>
  ) : (
    <div className="flex flex-col items-center justify-center rounded-md bg-muted/50 p-8 text-center">
      <FileText className="mb-2 h-10 w-10 text-muted-foreground/70" />
      <p className="text-muted-foreground">{emptyMessage}</p>
    </div>
  );
};

const FeesAccordion = ({
  fees,
  currentEntity,
}: {
  fees: FeeSet[];
  currentEntity: any;
}) => {
  const [unpaidFees, setUnpaidFees] = useState<FeeSet[]>([]);
  const [paidFees, setPaidFees] = useState<FeeSet[]>([]);

  useEffect(() => {
    // Filter unpaid and paid fees based on the "paid" property
    const unpaid = fees?.filter((fee) => fee.feeStatus === "unpaid") || [];
    const paid = fees?.filter((fee) => fee.feeStatus === "paid") || [];

    setUnpaidFees(unpaid);
    setPaidFees(paid);
  }, [fees]);

  return (
    <Accordion
      type="multiple"
      className="space-y-4"
      defaultValue={["unpaid"]}
    >
      <AccordionItem value="unpaid" className="border rounded-lg shadow-sm">
        <AccordionTrigger className="px-4 py-3 hover:bg-muted/30 hover:no-underline data-[state=open]:bg-muted/20">
          <div className="flex items-center gap-2">
            <span className="text-lg font-medium">Unpaid Fees</span>
            {unpaidFees.length > 0 && (
              <Badge variant="secondary" className="ml-2 bg-rose-100 text-rose-700">
                {unpaidFees.length}
              </Badge>
            )}
          </div>
        </AccordionTrigger>
        <AccordionContent className="p-4 pt-2">
          <FeeList
            fees={unpaidFees}
            setFees={setUnpaidFees}
            emptyMessage="No unpaid fees"
          />
        </AccordionContent>
      </AccordionItem>

      <AccordionItem value="paid" className="border rounded-lg shadow-sm">
        <AccordionTrigger className="px-4 py-3 hover:bg-muted/30 hover:no-underline data-[state=open]:bg-muted/20">
          <div className="flex items-center gap-2">
            <span className="text-lg font-medium">Paid Fees</span>
            {paidFees.length > 0 && (
              <Badge variant="outline" className="ml-2 border-emerald-200 bg-emerald-50 text-emerald-700">
                {paidFees.length}
              </Badge>
            )}
          </div>
        </AccordionTrigger>
        <AccordionContent className="p-4 pt-2">
          <FeeList
            fees={paidFees}
            setFees={setPaidFees}
            emptyMessage="No paid fees"
          />
        </AccordionContent>
      </AccordionItem>
    </Accordion>
  );
};

const FeeSection = ({ label, amount }: { label: string; amount: number }) => {
  const { entityIsFetching } = useEntity();

  return (
    <div className="flex items-center justify-between rounded-lg border bg-card p-4 shadow-sm">
      <span className="text-muted-foreground">{label}</span>
      <div className="flex items-center gap-2">
        {entityIsFetching ? (
          <Loader2 className="h-5 w-5 animate-spin text-muted-foreground" />
        ) : (
          <span className="text-xl font-semibold">
            {toDollar(amount)}
          </span>
        )}
      </div>
    </div>
  );
};

const toDollar = (amount: number) => {
  return `$${amount.toFixed(2).replace(/\d(?=(\d{3})+\.)/g, "$&,")}`;
};