import React, { useState, useEffect, useRef } from "react";
import { BiCheck } from "react-icons/bi";

interface IOption {
  value: string;
  label: string;
}

interface CustomSelectProps {
  name: string;
  placeholder: string;
  options: IOption[];
  selectedValue: string;
  onValueChange: (name: string, value: string) => void;
}

const CustomSelect: React.FC<CustomSelectProps> = ({
  name,
  selectedValue,
  onValueChange,
  placeholder,
  options,
}) => {
  const [isFocused, setIsFocused] = useState(false);
  const [displayValue, setDisplayValue] = useState(selectedValue || "");
  const [focusedOptionIndex, setFocusedOptionIndex] = useState(-1);
  const [filteredOptions, setFilteredOptions] = useState<IOption[]>(options);
  const optionsRef = useRef<HTMLUListElement>(null);

  useEffect(() => {
    const selectedOption = options.find(option => option.value === selectedValue);
    if (selectedOption) {
      setDisplayValue(selectedOption.label);
    }
  }, [selectedValue, options]);

  useEffect(() => {
    if (focusedOptionIndex >= 0 && optionsRef.current) {
      const optionElement = optionsRef.current.children[focusedOptionIndex] as HTMLElement;
      optionElement.scrollIntoView({ block: "nearest" });
    }
  }, [focusedOptionIndex]);

  const handleArrowKey = (direction: "up" | "down") => {
    setFocusedOptionIndex(prevIndex => {
      if (direction === "up") {
        return prevIndex > 0 ? prevIndex - 1 : filteredOptions.length - 1;
      } else {
        return prevIndex < filteredOptions.length - 1 ? prevIndex + 1 : 0;
      }
    });
  };

  const handleChange = (value: string) => {
    const selectedOptionLabel = options.find(option => option.value === value)?.label || '';
    setDisplayValue(selectedOptionLabel);
    onValueChange(name, value);
    setIsFocused(false);
  };

  // const handleInputChange = (inputValue: string) => {
  //   setDisplayValue(inputValue);
  //   setFilteredOptions(options.filter(option =>
  //     option.label.toLowerCase().includes(inputValue.toLowerCase())
  //   ));
  // };

  return (
    <div className="relative">
      <input
        type="text"
        placeholder={placeholder}
        value={displayValue}
        onKeyDown={(event) => {
          switch (event.key) {
            case "ArrowDown":
              event.preventDefault();
              handleArrowKey("down");
              break;
            case "ArrowUp":
              event.preventDefault();
              handleArrowKey("up");
              break;
            case "Enter":
              event.preventDefault();
              if (isFocused && focusedOptionIndex !== -1) {
                const selectedOption = filteredOptions[focusedOptionIndex];
                handleChange(selectedOption?.value);
              }
              break;
            default:
              break;
          }
        }}
        onChange={({ target: { value } }) => {
          setDisplayValue(value);
          setFilteredOptions(options.filter(option =>
            option.label.toLowerCase().includes(value.toLowerCase())
          ));
        }}
        
        onFocus={() => setIsFocused(true)}
        onBlur={() => {
          setIsFocused(false);
        
          const matchedOption = options.find(option => 
            option.label.toLowerCase() === displayValue.toLowerCase()
          );
        
          if (matchedOption) {
            onValueChange(name, matchedOption.value);
          } else if (displayValue.trim() !== '') {
            // Send the new value if it doesn't match any option
            onValueChange(name, displayValue);
          }
        }}        className="form-input block w-full h-10 px-3 py-2 border border-slate-200 bg-white text-sm placeholder:text-neutral-900 focus:ring-2 focus:ring-slate-950 focus:outline-none focus:ring-offset-2 focus:ring-offset-white rounded"
      />
      {isFocused && (
        <ul
          ref={optionsRef}
          className="absolute z-50 mt-1 w-full bg-white border border-slate-200 shadow-md max-h-60 rounded-md overflow-auto focus:outline-none"
        >
          {filteredOptions.map((option, index) => (
            <li
              key={option.value}
              className={`relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none hover:bg-slate-100 focus:bg-slate-100 focus:text-slate-900 ${
                index === focusedOptionIndex ? "bg-slate-100" : ""
              }`}
              onMouseDown={() => handleChange(option.value)}
              onMouseEnter={() => setFocusedOptionIndex(index)}
            >
              {displayValue === option.label && (
                <span className="absolute left-2 flex h-3.5 w-3.5 items-center justify-center">
                  <BiCheck className="h-4 w-4" />
                </span>
              )}
              {option.label}
            </li>
          ))}
        </ul>
      )}
    </div>
  );
};

export default CustomSelect;
