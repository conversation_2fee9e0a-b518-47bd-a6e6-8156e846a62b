import { getAvatarBlob } from "@/components/AvatarImage";
import { formatDate } from "@/components/license/licenseHelper";
import { Address } from "@/types/AddressType";
import { Dog } from "@/types/DogType";
import { Contact, Individual } from "@/types/IndividualType";
import { License } from "@/types/LicenseType";
import { isAfter } from "date-fns";

export type Entity = {
  primaryDisplay: string;
  secondaryDisplay: string;
  thirdDisplay?: string;
  warningDisplay?: string;
  avatarUrl: string;
  active: string;
  contacts?: {
    phone: string;
    email: string;
  };
};

export const entityDisplay = (entity: any, entityType: string) => {
  console.log(entityType);
  console.log(entity);
  switch (entityType) {
    case "dog":
      const dog = entity?.dog as Dog;
      console.log(dog);
      const rabiesValid = dog?.vaccineDueDate
        ? isAfter(new Date(dog?.vaccineDueDate), new Date())
        : false;
      const rabiesExempt = dog?.vaccineDatesExempt;

      const vaccineDueDate = dog?.vaccineDueDate
        ? formatDate(dog?.vaccineDueDate)
        : "No Date";

      const rabies = rabiesExempt
        ? "Rabies (Exempt): Not Required"
        : rabiesValid
          ? `Rabies (Valid): ${vaccineDueDate}`
          : `Rabies (Expired): ${vaccineDueDate}`;

      return {
        primaryDisplay: `${dog?.dogName} (${dog?.dogBreed})`,
        secondaryDisplay: `Tag Number: ${dog?.tagNumber} | Color: ${
          dog?.dogPrimaryColor
        } ${dog?.dogSecondaryColor ? "/" + dog?.dogSecondaryColor : ""}`,
        thirdDisplay: rabies,
        avatarUrl: getAvatarBlob(dog?.documents),
        active: dog?.active,
      };

    case "individual":
      const individual = entity?.individual as Individual;
      console.log(individual)
      const fullAddress = `${individual?.address ?? ""}${
        individual?.streetAddress2 ?? ""
      }, ${individual?.city ?? ""}, ${individual?.state ?? ""} ${individual?.zip ?? ""}`;

      const warning =
        entity?.status === "Deceased"
          ? "Deceased"
          : !entity?.registered
            ? "Account Not Registered Online"
            : null;

      const phone = individual?.contacts.find(
        (contact: Contact) =>
          contact.type === "Phone" && contact.group === "Home",
      );
      const email = individual?.contacts.find(
        (contact: Contact) =>
          contact.type === "Email" && contact.group === "Primary",
      );

      return {
        primaryDisplay: individual?.firstName + " " + individual?.lastName,
        secondaryDisplay: individual?.address ? fullAddress : "",
        warningDisplay: warning,
        avatarUrl: getAvatarBlob(entity?.documents),
        active: individual?.active,
        contacts: {
          phone: phone?.value ? formatPhoneNumber(phone?.value) : "No Phone",
          email: email?.value ? email.value : "No Email",
        },
      };

    case "address":
      const addressEntity = entity?.address as Address;
      console.log(addressEntity);

      return {
        primaryDisplay: `${addressEntity.streetAddress}${
          addressEntity.streetAddress2 ? " " + addressEntity.streetAddress2 : ""
        }`,
        secondaryDisplay: `${addressEntity.city}, ${addressEntity.state} ${addressEntity.zip}`,
        avatarUrl: null,
        active: true,
      };
  }
};

export const entityDisplayMin = (entity: any, entityType: string) => {
  switch (entityType) {
    case "dog":
      const dog = entity as Dog;

      const rabiesValid = dog?.vaccineDueDate
        ? isAfter(new Date(dog?.vaccineDueDate), new Date())
        : false;
      const rabiesExempt = dog?.vaccineDatesExempt;

      const vaccineDueDate = dog?.vaccineDueDate
        ? formatDate(dog?.vaccineDueDate)
        : "No Date";

      const rabies = rabiesExempt
        ? "Rabies (Exempt): Not Required"
        : rabiesValid
          ? `Rabies (Valid): ${vaccineDueDate}`
          : `Rabies (Expired): ${vaccineDueDate}`;

      return {
        primaryDisplay: `${dog?.dogName} (${dog?.dogBreed})`,
        secondaryDisplay: `Tag Number: ${dog?.tagNumber} | Color: ${
          dog?.dogPrimaryColor
        } ${dog?.dogSecondaryColor ? "/" + dog?.dogSecondaryColor : ""}`,
        thirdDisplay: rabies,
        warningDisplay: !dog?.active ? dog?.status : null,
        // avatarUrl: "/images/icons/dog.png",
        avatarUrl: getAvatarBlob(dog?.documents),

        active: dog?.active,
      };

    case "individual":
      const individual = entity as Individual;

      const warning =
        individual.status === "Deceased"
          ? "Deceased"
          : !entity.registered
            ? "Account Not Registered Online"
            : null;

      const phone = individual.contacts.find(
        (contact: Contact) =>
          contact.type === "Phone" && contact.group === "Home",
      );
      const email = individual.contacts.find(
        (contact: Contact) =>
          contact.type === "Email" && contact.group === "Primary",
      );

      return {
        primaryDisplay: individual.firstName + " " + individual.lastName,
        warningDisplay: warning,
        avatarUrl: getAvatarBlob(individual.documents),
        // avatarUrl: "/images/icons/user.png",

        active: individual.active,
        contacts: {
          phone: phone?.value ? formatPhoneNumber(phone.value) : "No Phone",
          email: email?.value ? email.value : "No Email",
        },
      };

    case "address":
      const addressEntity = entity as Address;
      console.log(addressEntity);

      return {
        primaryDisplay: `${addressEntity.streetAddress}${
          addressEntity.streetAddress2 ? " " + addressEntity.streetAddress2 : ""
        }`,
        secondaryDisplay: `${addressEntity.city}, ${addressEntity.state} ${addressEntity.zip}`,
        // warningDisplay: addressWarning,
        avatarUrl: null,
        // avatarUrl: "/images/icons/address.png",

        active: true,
      };
  }
};

const formatPhoneNumber = (phoneNumber: string) => {
  return phoneNumber.replace(/(\d{3})(\d{3})(\d{4})/, "($1) $2-$3");
};
