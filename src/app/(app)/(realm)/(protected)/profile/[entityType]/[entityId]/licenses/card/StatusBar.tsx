import React from "react";
import { cardStyle } from "../../../../../../../../../components/license/cards/cardStyle";

export const StatusBar = ({ status }: { status: string }) => {
  // if it exists in cardStyle then it will return the value of the key else use default
  const label = cardStyle[status]?.label || "default";
  return (
    <>
      <div
        className={`${cardStyle[label]?.bar} h-1 px-2 shrink-0 flex items-center justify-center`}
      ></div>
      <div className="px-6">
        <div className={`${cardStyle[label].styling} w-fit rounded text-xs px-2 mt-4`}>
          {cardStyle[label]?.label}
        </div>
      </div>
    </>
  );
};
