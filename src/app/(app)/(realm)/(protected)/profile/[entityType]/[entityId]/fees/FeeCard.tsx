import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  CreditCard,
  InfoIcon,
  CalendarIcon,
  DollarSign,
} from "lucide-react";
import { useAtom } from "jotai";
import { toast<PERSON>tom } from "@/components/ui/toast/toast";
import { cn } from "@/lib/utils";
import { useMyCart } from "@/hooks/useMyCart";
import { useEntity } from "@/hooks/providers/useEntity";
import { useMyProfile } from "@/hooks/providers/useMyProfile";
import { format, isValid } from "date-fns";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";

import { Badge } from "@/components/ui/badge";
import { FeeSet } from "@/hooks/api/useFees";
import { FeeDocuments } from "./FeeDocuments";
import DeleteFee from "./DeleteFee";

const toDollar = (amount: number) => {
  return `$${amount.toFixed(2).replace(/\d(?=(\d{3})+\.)/g, "$&,")}`;
};

export default function FeeCard({
  feeSet,
  setFees,
}: {
  feeSet: FeeSet;
  setFees: any;
}) {
  const { entityRefetch } = useEntity();
  const { hasPermissions } = useMyProfile();
  const fees = feeSet.fees ?? [];
  const documents = feeSet?.documents ?? [];

  const editable = hasPermissions(["super-admin"]);

  return (
    <Card className="mx-auto w-full overflow-hidden border shadow-sm transition-all hover:shadow-md">
      {/* Status indicator strip */}
      <div
        className={cn(
          "h-1 w-full",
          feeSet.feeStatus === "paid" ? "bg-emerald-500" : "bg-rose-500"
        )}
      />

      {/* Header */}
      <CardHeader className="pb-2">
        <div className="flex flex-col justify-between gap-2 md:flex-row md:items-center">
          <div>
            <div className="flex items-center gap-2">
              <CardTitle className="text-xl font-semibold">
                {feeSet.label}
              </CardTitle>
              <Badge
                variant={
                  feeSet.feeStatus === "paid" ? "success" : "destructive"
                }
                className="capitalize"
              >
                {feeSet.feeStatus}
              </Badge>
            </div>
            <CardDescription className="mt-1 flex items-center gap-1 text-sm text-neutral-600">
              <CalendarIcon size={14} />
              <span>
                {isValid(new Date(feeSet.createdDateTime))
                  ? `Created: ${format(
                      new Date(feeSet.createdDateTime),
                      "MMM d, yyyy"
                    )}`
                  : "No Creation Date"}{" "}
                •{" "}
                {isValid(new Date(feeSet.updatedDateTime))
                  ? `Updated: ${format(
                      new Date(feeSet.updatedDateTime),
                      "MMM d, yyyy"
                    )}`
                  : "No Update Date"}
              </span>
            </CardDescription>
          </div>
        </div>
      </CardHeader>

      {/* Content */}
      <CardContent className="space-y-4">
        {fees.length === 0 ? (
          <div className="bg-muted/50 flex flex-col items-center justify-center rounded-md p-6 text-center">
            <InfoIcon className="text-muted-foreground/70 mb-2 h-10 w-10" />
            <p className="text-muted-foreground">
              There are no fees in this set.
            </p>
          </div>
        ) : (
          <>
            {/* Desktop Table - hidden on mobile */}
            <div className="hidden md:block">
              {/* Table header */}
              <div className="bg-muted/30 text-muted-foreground grid grid-cols-12 gap-2 rounded-lg p-3 text-xs font-medium">
                <div
                  className={cn(
                    "col-span-5",
                    editable ? "col-span-6" : "col-span-8"
                  )}
                >
                  Fee Name
                </div>
                <div className="col-span-2 text-center">Status</div>
                <div className="col-span-2 text-right">Amount</div>
                {editable && <div className="col-span-2 text-right">Actions</div>}
              </div>

              {/* Table rows */}
              <div className="space-y-1">
                {fees.map((fee) => (
                  <div
                    key={fee.entityId}
                    className="hover:bg-neutral-100 grid grid-cols-12 gap-2 rounded-md p-3 transition-colors"
                  >
                    {/* Fee Name */}
                    <div
                      className={cn(
                        "text-foreground self-center text-sm font-medium",
                        editable ? "col-span-6" : "col-span-8"
                      )}
                    >
                      {fee.feeName}
                    </div>

                    {/* Status */}
                    <div className="col-span-2 self-center text-center">
                      <Badge
                        variant={
                          fee.feeStatus === "paid" ? "outline" : "secondary"
                        }
                        className={cn(
                          "capitalize",
                          fee.feeStatus === "paid"
                            ? "border-emerald-200 bg-emerald-50 text-emerald-700"
                            : "border-rose-200 bg-rose-50 text-rose-700"
                        )}
                      >
                        {fee.feeStatus}
                      </Badge>
                    </div>

                    {/* Amount */}
                    <div className="col-span-2 self-center text-right text-sm font-semibold">
                      {toDollar(fee.feeAmount)}
                    </div>

                    {/* Actions */}
                    {editable && (
                      <div className="col-span-2 flex items-center justify-end gap-2">
                        <DeleteFee
                          fee={fee}
                          refetch={entityRefetch}
                          setFees={setFees}
                          icon={true}
                        />
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>

            {/* Mobile Cards - shown only on mobile */}
            <div className="space-y-3 md:hidden">
              {fees.map((fee) => (
                <div
                  key={fee.entityId}
                  className="border-muted bg-card rounded-lg border p-3 shadow-sm"
                >
                  <div className="flex items-center justify-between">
                    <h4 className="text-foreground font-medium">
                      {fee.feeName}
                    </h4>
                    <Badge
                      variant={
                        fee.feeStatus === "paid" ? "outline" : "secondary"
                      }
                      className={cn(
                        "capitalize",
                        fee.feeStatus === "paid"
                          ? "border-emerald-200 bg-emerald-50 text-emerald-700"
                          : "border-rose-200 bg-rose-50 text-rose-700"
                      )}
                    >
                      {fee.feeStatus}
                    </Badge>
                  </div>

                  <div className="text-muted-foreground mt-2 flex items-center gap-1 text-sm">
                    <DollarSign
                      size={14}
                      className={
                        fee.feeStatus === "paid"
                          ? "text-emerald-600"
                          : "text-rose-600"
                      }
                    />
                    <span className="text-foreground font-semibold">
                      {toDollar(fee.feeAmount)}
                    </span>
                  </div>

                  {/* Mobile delete action */}
                  {editable && fee.feeStatus === "unpaid" && (
                    <div className="mt-2 flex justify-end">
                      <DeleteFee fee={fee} refetch={entityRefetch} />
                    </div>
                  )}
                </div>
              ))}
            </div>
          </>
        )}
      </CardContent>

      {documents && documents?.length > 0 && (
        <CardContent className="space-y-4 border-t">
          <FeeDocuments documents={documents} />
        </CardContent>
      )}

      {/* Footer */}
      <CardFooter className="bg-muted/10 border-t p-4">
        <div className="flex w-full flex-col items-center justify-between gap-4 md:flex-row">
          {/* Total Outstanding */}
          {fees.length > 0 && (
            <div className="flex items-center gap-2">
              <div className="text-muted-foreground text-sm">
                Total Outstanding:
              </div>
              <div className="text-foreground text-lg font-semibold">
                {toDollar(feeSet.totals.totalOutstandingAmount)}
              </div>
            </div>
          )}

          {/* Buttons */}
          <div className="flex items-center gap-2">
            {editable && feeSet.feeStatus === "unpaid" && (
              <DeleteFee
                fee={feeSet}
                refetch={entityRefetch}
                setFees={setFees}
              />
            )}
            {fees.length > 0 && feeSet.feeStatus === "unpaid" && (
              <AddToCartButton fee={feeSet} />
            )}
          </div>
        </div>
      </CardFooter>
    </Card>
  );
}

const AddToCartButton = ({ fee }: { fee: FeeSet }) => {
  const { addToCart, cartSummary } = useMyCart();
  const cartItems: any = cartSummary?.items ?? [];
  const [_, setToast] = useAtom(toastAtom);

  const isInCart = cartItems.some(
    (item: { entityId: string }) => item?.entityId === fee.entityId
  );

  return (
    <Button
      size="sm"
      variant={isInCart ? "outline" : "default"}
      className={cn(
        "flex items-center gap-2 transition-all",
        isInCart
          ? "border-blue-200 bg-blue-50 text-blue-700 hover:bg-blue-100"
          : "hover:bg-primary/90"
      )}
      onClick={() => {
        if (isInCart) {
          setToast({
            label: "Fee already in cart",
            status: "info",
            message: "This fee has already been added to your cart",
          });
        } else {
          addToCart(fee.entityId, fee.entityType);
        }
      }}
    >
      <CreditCard size={16} />
      <span>{isInCart ? "Added to Cart" : "Add to Cart"}</span>
    </Button>
  );
};
