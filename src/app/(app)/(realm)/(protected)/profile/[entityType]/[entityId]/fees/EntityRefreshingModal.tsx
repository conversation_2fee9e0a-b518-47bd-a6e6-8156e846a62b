import { Loader2 } from "lucide-react";

export const EntityRefreshingModal = ({
  isRefreshing
}: {
  isRefreshing: boolean;
}) => {
  if (!isRefreshing) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
      <div className="rounded-lg bg-white p-6 shadow-lg">
        <div className="flex flex-col items-center gap-4">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <p className="text-lg font-medium">Refreshing entity data...</p>
        </div>
      </div>
    </div>
  );
};