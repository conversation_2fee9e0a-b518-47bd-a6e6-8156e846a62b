import React from "react";
import { useState } from "react";
import TabsBar from "@/components/ui/Tabs/TabsBar";
import AffiliationResult from "./AffiliationResult";
import PageContainer from "@/components/ui/Page/PageContainer";
import { useEntity } from "@/hooks/providers/useEntity";

type Affiliation = {
  primaryDisplay?: string;
  secondaryDisplay?: string;
  thirdDisplay?: string;
  entityType: string;
  entityId: string;
  avatarUrl: string;
};

const Affiliations = () => {

  const { entity, entityType } = useEntity();
  console.log(entity)
  console.log(entityType)

  const [activeRadio, setActiveRadio] = useState<string | null>("all");

  const options: {
    section: string;
    value: string;
  }[] = [
    {
      section: "All",
      value: "all",
    },
    {
      section: "Individual",
      value: "individual",
    },
    {
      section: "Address",
      value: "address",
    },
    {
      section: "Dog",
      value: "dog",
    },
  ];

  const processEntityType = (entityArray: any, type: string) => {
    if (!Array.isArray(entityArray) || entityArray.length === 0) {
      return [];
    }
    return entityArray.map((item) => ({ ...item, entityType: type }));
  };

  const results = [
    ...processEntityType(entity?.individual, "individual"),
    ...processEntityType(entity?.address, "address"),
    ...processEntityType(entity?.dog, "dog"),
    ...processEntityType(entity?.organization, "organization"),
  ];

  console.log(results);

  const filteredResults =
    activeRadio === "all"
      ? results ?? []
      : results.filter(
          (affiliation: Affiliation) => affiliation.entityType === activeRadio,
        ) ?? [];

  return (
    <div className="w-full ">
      <PageContainer>
        <TabsBar
          options={options}
          activeRadio={activeRadio}
          setActiveRadio={setActiveRadio}
          buttonLabel="New Affiliation"
          buttonOnClick={() => console.log("clicked")}
          buttonDisabled
        />
        <div className="flex h-full flex-col gap-2 overflow-auto py-4">
          {filteredResults.map((affiliation: any, index: number) => (
            <AffiliationResult key={index} result={affiliation} />
          ))}
        </div>
      </PageContainer>
    </div>
  );
};

export default Affiliations;
