import React from "react";
import Image from "next/image";
import { <PERSON><PERSON><PERSON><PERSON>velope, Bi<PERSON><PERSON>, BiPhone } from "react-icons/bi";
import { useRouter, useParams } from "next/navigation";
import ProfileActions from "@/components/profile/ProfileActions";
import { FiMoreVertical } from "react-icons/fi";
import Link from "next/link";
import { Dog } from "@/types/DogType";
import type { Individual } from "@/types/IndividualType";
import { Entity, entityDisplayMin } from "../../profile/entityHelper";
import AvatarImage from "@/components/AvatarImage";

export default function EntityInfo({
  entity,
  entityType,
}: {
  entity: Individual | Dog;
  entityType: string;
}) {
  

  const ent = entityDisplayMin(entity, entityType);

  return (
    <Link
      className="mt-2 flex w-full flex-col gap-2 p-2 hover:bg-clerk-primary/10 hover:shadow lg:flex-row"
      href={`/entity/${entityType}/${entity?.entityId}?tab=profile`}
    >
      <div className="relative h-[60px] w-[60px] shrink-0 overflow-hidden rounded">
        <AvatarImage
          entityType={entityType}
          src={ent?.avatarUrl as string}
          fill
          alt={ent?.primaryDisplay ?? "Icon Image"}
          className={`absolute object-cover object-center ${
            ent?.active ? "" : "grayscale"
          }`}
        />
      </div>
      <div className="w-full min-w-0">
        <p className="text-left text-lg font-bold">{ent?.primaryDisplay}</p>
        <div className="flex flex-col items-start justify-start">
          {ent?.contacts && (
            <div className="flex flex-wrap items-center gap-4">
              <p className="flex items-center text-sm text-neutral-600">
                <BiPhone className="mr-1 inline-block" />
                {ent?.contacts?.phone}
              </p>
              <p className="flex items-center text-sm text-neutral-600">
                <BiEnvelope className="mr-1 inline-block" />
                {ent?.contacts?.email}
              </p>
            </div>
          )}

          <p className="flex items-center text-sm text-neutral-600">
            {ent?.secondaryDisplay ?? ""}
          </p>
          <p className="flex items-center text-sm text-red-600">
            {ent?.warningDisplay ?? ""}
          </p>
        </div>
      </div>
      <ProfileActions
        className="h-fit shrink-0 rounded border bg-white p-2 hover:bg-clerk-background/10"
        entityId={entity.entityId}
        entityType={entityType}
      >
        <FiMoreVertical />
      </ProfileActions>
    </Link>
  );
}
