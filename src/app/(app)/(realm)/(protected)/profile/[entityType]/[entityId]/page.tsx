"use client";
import LicenseComponent from "@/components/profile/LicenseComponent";
import ProfileComponent from "@/components/profile/ProfileComponent";
import { useEntity } from "@/hooks/providers/useEntity";
import { useSearchParams } from "next/navigation";
import React from "react";
import Affiliations from "./profile/Affiliations";
import { EntityHistory } from "@/components/history/EntityHistory";
import { EntityFiles } from "@/components/files/EntityFiles";
import EntitySettings from "./settings/EntitySettings";
import { useMyProfile } from "@/hooks/providers/useMyProfile";
import EntityFees from "./fees/EntityFees";
import NotesComponent from "@/components/profile/NotesComponent";
import { ProfileNotesProvider } from "@/components/profile/notes/useProfileNotes";

const ViewPermission = ({
  permissions,
  children,
}: {
  permissions: string[];
  children: React.ReactNode;
}) => {
  const { hasPermissions } = useMyProfile();
  if (hasPermissions(permissions)) {
    return <>{children}</>;
  }
  return <div>You are not permitted to view this page</div>;
};

const ProfilePage = () => {
  const tab = useSearchParams().get("tab");
  const { entity, entityType, entityId } = useEntity();

  switch (tab) {
    case "profile":
      return (
        <ViewPermission permissions={["super-admin", "resident"]}>
          <ProfileNotesProvider>
            <ProfileComponent />
          </ProfileNotesProvider>
        </ViewPermission>
      );
    case "licenses":
      return (
        <ViewPermission permissions={["super-admin", "resident"]}>
          <LicenseComponent entity={entity} entityType={entityType} />
        </ViewPermission>
      );
    case "notes":
      return (
        <ViewPermission permissions={["super-admin"]}>
          <ProfileNotesProvider>
            <NotesComponent />
          </ProfileNotesProvider>
        </ViewPermission>
      );
    case "associations":
      return (
        <ViewPermission permissions={["super-admin"]}>
          <Affiliations />;
        </ViewPermission>
      );
    case "history":
      return (
        <ViewPermission permissions={["super-admin", "resident"]}>
          <EntityHistory
            entity={entity[entityType]}
            entityType={entityType as string}
          />
        </ViewPermission>
      );
    case "fees":
      return <EntityFees entity={entity} entityType={entityType as string} />;
    case "payments":
      return <div>Payments</div>;
    case "files":
      return (
        <ViewPermission permissions={["super-admin", "resident"]}>
          <EntityFiles
            documents={{
              your: entity[entityType].documents,
            }}
            entityId={entityId}
            entityType={entityType}
            sheet={false}
          />
        </ViewPermission>
      );
    case "settings":
      return (
        <ViewPermission permissions={["super-admin", "resident"]}>
          <EntitySettings />
        </ViewPermission>
      );
    default:
      return <div>No Selection</div>;
  }
};

export default ProfilePage;
