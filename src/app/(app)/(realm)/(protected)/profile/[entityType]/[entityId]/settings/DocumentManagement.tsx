import { Title } from "@/components/profile/ProfileComponents";
import { Section } from "@/components/profile/helpers/Dialogs";
import { Switch } from "@/components/ui/switch";
import { useEntity } from "@/hooks/providers/useEntity";

import React, { useState } from "react";
import { OptInStateType } from "./EntitySettings";
import { useUpdateEntityProfile2 } from "@/hooks/api/useProfiles";
import { useAtom } from "jotai";
import { toastAtom } from "@/components/ui/toast/toast";
import { createFormData } from "../../../../(resident)/(registered)/resident/[entityType]/[entityId]/components/modals/EditDialog";

type Params = {
  [key: string]: any;
};

export default function DocumentManagement() {
  const [, setToast] = useAtom(toastAtom);
  const { entity, entityId, entityType } = useEntity();
  const currentEntity = entity[entityType];

  const [entityOptIns, setEntityOptIns] = useState<OptInStateType>(
    formatOptIns(currentEntity.optIns),
  );

  const updateEntity = useUpdateEntityProfile2();

  const handleSave = (fieldName: string, value: any, params?: Params) => {
    let data = {};

    if (params) {
      data = { ...params };
    } else data = { [fieldName]: value };

    const formData = createFormData(data);

    updateEntity.mutate(
      {
        entityId: entityId,
        body: formData,
      },
      {
        onSuccess: () => {
          setToast({
            status: "success",
            label: "Profile Updated",
            message: "Successfully Updated Profile",
          });
        },
        onError: (error: any) => {
          console.log(error);
          setToast({
            status: "error",
            label: "Error Updating Profile",
            message: error?.response?.data?.message,
          });
          setEntityOptIns((prev) => {
            return prev.map((a) => {
              return {
                ...a,
                [fieldName]: !value,
              };
            });
          });
        },
      },
    );
  };

  return (
    <div>
      <Title>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <span>Document Management</span>
          </div>
        </div>
      </Title>

      <div className="flex flex-col gap-4">
        {optIns.map((a) => {
          const checked = entityOptIns.find((b) => b[a.key] === true) as any;

          return (
            <Section label={a.label} key={a.key}>
              <Switch
                checked={checked}
                onCheckedChange={(value) => {
                  const newOptIns = entityOptIns.map((b) => {
                    if (b[a.key]) {
                      return {
                        [a.key]: value,
                      };
                    }
                    return b;
                  });

                  setEntityOptIns(newOptIns);
                  handleSave(a.key, value);
                }}
              />
              {updateEntity.isLoading && (
                <span className="animate-pulse">Saving...</span>
              )}
            </Section>
          );
        })}
      </div>
    </div>
  );
}

const formatOptIns = (optIns: any) => {
  const formattedOptIns: OptInStateType = optIns.map((a: any) => {
    return {
      [a.name]: a.active,
    };
  });

  return formattedOptIns;
};

const optIns: {
  key: string;
  label: string;
}[] = [
  {
    key: "optInPaperless",
    label: "Paperless",
  },
  // {
  //   key: "optInLicenseUpdates",
  //   label: "License Updates",
  // },
  // {
  //   key: "optInDogUpdates",
  //   label: "Dog Updates",
  // },
];
