import { useEffect, useRef, useState } from "react";
import { Check, ChevronsUpDown } from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { ScrollArea } from "@/components/ui/scroll-area";

interface IOption {
  value: string;
  label: string;
}

interface CustomSelectProps {
  name: string;
  placeholder: string;
  options: IOption[];
  selectedValue: string;
  onValueChange: (name: string, value: string) => void;
  className?: string;
}

export default function ComboBoxComponent({
  name,
  selectedValue,
  onValueChange,
  placeholder,
  options,
  className,
}: CustomSelectProps) {
  const [open, setOpen] = useState(false);
  const [inputValue, setInputValue] = useState("");
  const [filteredOptions, setFilteredOptions] = useState<IOption[]>(options);
  const [buttonWidth, setButtonWidth] = useState<number>(0);
  const buttonRef = useRef<HTMLButtonElement>(null);
  const inputRef = useRef(null);

  useEffect(() => {
    if (buttonRef.current) {
      setButtonWidth(buttonRef.current.offsetWidth);
    }

    // Set the display value based on the selectedValue
    const selectedOption = options.find(
      (option) => option.value === selectedValue,
    );
    setInputValue(selectedOption ? selectedOption.label : selectedValue || "");
  }, [selectedValue, options]);

  const handleSelect = (value: string) => {
    setInputValue(
      options.find((option) => option.value === value)?.label || value,
    );
    onValueChange(name, value);
    setOpen(false);
  };

  const handleInputChange = (search: string) => {
    setInputValue(search);
    const filters = options.filter((option) =>
      option.label.toLowerCase().includes(search.toLowerCase()),
    );
    console.log(filters);
    setFilteredOptions(filters);
  };

  const handleAddNewOption = () => {
    if (inputValue.trim()) {
      onValueChange(name, inputValue);
      setOpen(false);
    }
  };

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild className="w-full">
        <Button
          ref={buttonRef}
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className="w-full justify-between px-2 py-1 text-base font-normal"
        >
          {inputValue || placeholder}
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent
        className="w-full p-0"
        style={{ width: buttonWidth ?? "" }}
      >
        <Command>
          <CommandInput
            placeholder="Search option..."
            value={inputValue}
            onValueChange={handleInputChange}
          />
          <ScrollArea className="max-h-[200px] overflow-auto">
            <CommandEmpty className="p-0">
              <div className="p-1">
                <button
                  className="flex w-full items-center justify-start rounded-md px-2 py-1.5 text-sm hover:bg-slate-100 focus:bg-slate-100"
                  onClick={handleAddNewOption}
                >
                  <Check className="mr-2 h-4 w-4" />
                  Add &quot;{inputValue}&quot;
                </button>
              </div>
            </CommandEmpty>
            <CommandGroup>
              {filteredOptions?.map((option) => {
                return (
                  <CommandItem
                    key={option.label}
                    value={option.value}
                    onSelect={() => handleSelect(option.value)}
                  >
                    <Check
                      className={cn(
                        "mr-2 h-4 w-4",
                        inputValue === option.label
                          ? "opacity-100"
                          : "opacity-0",
                      )}
                    />
                    {option.label}
                  </CommandItem>
                );
              })}
            </CommandGroup>
          </ScrollArea>
        </Command>
      </PopoverContent>
    </Popover>
  );
}
