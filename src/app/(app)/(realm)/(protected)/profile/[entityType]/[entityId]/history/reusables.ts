import DogDeceased from "@/components/profile/events/dog/DogDeceased";
import DogFound from "@/components/profile/events/dog/DogFound";
import DogLost from "@/components/profile/events/dog/DogLost";
import DogReactivated from "@/components/profile/events/dog/DogReactivated";
import DogRelinquished from "@/components/profile/events/dog/DogRelinquished";
import DogSetDangerousFalse from "@/components/profile/events/dog/DogSetDangerousFalse";
import DogSetDangerousTrue from "@/components/profile/events/dog/DogSetDangerousTrue";
import IndividualDeceased from "@/components/profile/events/individual/IndividualDeceased";
import IndividualMovedOutsideJurisdiction from "@/components/profile/events/individual/IndividualMovedOutsideJurisdiction";
import IndividualReactivated from "@/components/profile/events/individual/IndividualReactivated";

const dogActions: { [key: string]: string } = {
  dogLost: "Dog Lost",
  dogFound: "Dog Found",
  dogDeceased: "Dog Deceased",
  dogRelinquished: "Dog Relinquished",
  dogReactivated: "Dog Reactivated",
  dogSetDangerousFalse: "Dog marked as not dangerous",
  dogSetDangerousTrue: "Dog marked as dangerous",
  dogLicenseTransfer: "Dog License Transfer",
  dogLicenseAdd: "Dog License Add",
  dogName: "Dog Name",
  tagNumber: "Tag Number",
  entityId: "Entity ID",
  uuid: "Unique Identifier",
  eventTypeId: "Event Type ID",
  createdBy: "Created By",
  createdDate: "Creation Date",
  comment: "Comment",
  action: "Action",
  participantType: "Participant Type",
  status: "Status",
  registered: "Registered",
  active: "Active",
  isDangerous: "Is Dangerous",
  vaccineName: "Vaccine Name",
  childFriendly: "Child Friendly",
  veterinarianName: "Veterinarian Name",
  vaccineProducer: "Vaccine Producer",
  vaccineAdministeredDate: "Vaccine Administered Date",
  dogTag: "Dog Tag",
  vaccineDatesExempt: "Vaccine Dates Exempt",
  dogBirthDate: "Dog Birth Date",
  dogBio: "Dog Bio",
  rabiesTagNumber: "Rabies Tag Number",
  microchipNumber: "Microchip Number",
  avatarUrl: "Avatar URL",
  vaccineLotNumber: "Vaccine Lot Number",
  vaccinePeriod: "Vaccine Period",
  vaccineDueDate: "Vaccine Due Date",
  catFriendly: "Cat Friendly",
  dogPrimaryColor: "Dog Primary Color",
  dogMarkings: "Dog Markings",
  vaccineLotExpirationDate: "Vaccine Lot Expiration Date",
  dogSecondaryColor: "Dog Secondary Color",
  vaccineBrand: "Vaccine Brand",
  dogSpayedOrNeutered: "Dog Spayed or Neutered",
  dogFriendly: "Dog Friendly",
  dogSex: "Dog Sex",
  identificationDocumentsUpload: "Identification Documents Upload",
  dogBreed: "Dog Breed",
  licenseExempt: "License Exempt",
  veterinaryName: "Veterinary Name",
};
const individualActions: { [key: string]: string } = {
  individualDeceased: "Individual Deceased",
  individualMovedOutsideJurisdiction: "Individual Moved Outside Jurisdiction",
  individualReactivated: "Individual Reactivated",
  entityId: "Entity ID",
  name: "Name",
  participantType: "Participant Type",
  contacts: "Contacts",
  status: "Status",
  registered: "Registered",
  active: "Active",
  mailingSameAsPrimary: "Mailing Address Same as Primary",
  firstName: "First Name",
  lastName: "Last Name",
  avatarUrl: "Avatar URL",
  dateOfBirth: "Date of Birth",
  middleName: "Middle Name",
  title: "Title",
  suffix: "Suffix",
  type: "Contact Type",
  group: "Contact Group",
  value: "Contact Value",
};

export const actionsTitle: { [key: string]: any } = {
  dog: dogActions,
  individual: individualActions,
};

export function replaceKeysWithValues(str: string, entityType: string) {
  // Split the string into lines
  const lines = str.split("\n");

  const actions = actionsTitle[entityType];

  // Traverse each line and replace keys with values
  const modifiedLines = lines.map((line) => {
    for (const key in actions) {
      if (line.includes(key)) {
        line = line.replace(key, actions[key]);
        break; // Stop the loop after the first replacement
      }
    }
    return line;
  });

  // Reassemble the modified lines into a single string
  return modifiedLines.join("\n");
}

type EventList = {
  name: string;
  component: any;
  link?: {
    type: "modal" | "link";
    value: string;
  };
  disabled: string[];
};

export const individualEventsList: { [key: string]: EventList } = {
  individualDeceased: {
    name: "Individual Deceased",
    component: IndividualDeceased,
    disabled: ["Deceased"],
  },
  individualMovedOutsideJurisdiction: {
    name: "Individual Moved outside Jurisdiction",
    component: IndividualMovedOutsideJurisdiction,
    disabled: ["Deceased"],
  },
  individualReactivated: {
    name: "Reactivate Individual",
    component: IndividualReactivated,
    disabled: ["Deceased"],
  },
  individualAddressChange: {
    name: "Address Change",
    component: null,
    link: {
      type: "link",
      value: "individualAddressChange",
    },
    disabled: ["Deceased"],
  },
};

export const dogEventsList: { [key: string]: EventList } = {
  dogLost: {
    name: "Dog Lost",
    component: DogLost,
    disabled: ["Lost", "Deceased", "Transferred"],
  },
  dogFound: {
    name: "Dog Found",
    component: DogFound,
    disabled: ["NotLost", "Transferred"],
  },
  dogDeceased: {
    name: "Dog Deceased",
    component: DogDeceased,
    disabled: ["Deceased", "Transferred"],
  },
  dogRelinquished: {
    name: "Dog Relinquished",
    component: DogRelinquished,
    disabled: ["Deceased", "Transferred"],
  },
  dogReactivated: {
    name: "Reactivate Dog",
    component: DogReactivated,
    disabled: ["Deceased", "Transferred"],
  },
  dogSetDangerousFalse: {
    name: "Dog Set Dangerous False",
    component: DogSetDangerousFalse,
    disabled: ["NotDangerous", "Transferred"],
  },
  dogSetDangerousTrue: {
    name: "Dog Set Dangerous True",
    component: DogSetDangerousTrue,
    disabled: ["Dangerous", "Deceased", "Transferred"],
  },
  dogTransferOfOwnership: {
    name: "Dog Transfer Of Ownership",
    component: null,
    link: {
      type: "modal",
      value: "dogTransferOfOwnership",
    },
    disabled: ["Deceased", "Transferred"],
  },
};
