import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { toastAtom } from "@/components/ui/toast/toast";
import { useHandleEvent } from "@/hooks/api/useProfiles";
import { useEntity } from "@/hooks/providers/useEntity";
import { useAtom } from "jotai";
import { useEffect, useState } from "react";
import { CgSpinner } from "react-icons/cg";
import { parseISO, isAfter, startOfDay } from "date-fns";

const ImpoundedDog = () => {
  const mutateEvent = useHandleEvent();
  const [_, setToast] = useAtom(toastAtom);
  const { entityId, entityType, entity, entityRefetch, entityIsFetching } =
    useEntity();
  const [open, setOpen] = useState<boolean>(false);
  const [dogImpoundmentDate, setDogImpoundmentDate] = useState<string>("");
  const [error, setError] = useState<string | null>(null);

  const { events, documents, fees, ...otherStuff } = entity.dog;
  console.log(otherStuff);

  const currentEntity = entity[entityType];
  const isImpounded = currentEntity?.status === "Impounded" || false;

  const [isSwitchOn, setIsSwitchOn] = useState<boolean>(isImpounded);

  useEffect(() => {
    setIsSwitchOn(isImpounded);
  }, [isImpounded]);

  const isActive = currentEntity?.active ?? false;

  useEffect(() => {
    if (open) {
      const today = new Date().toISOString().split("T")[0];
      setDogImpoundmentDate(today);
    }
  }, [open]);

  const handleSwitchChange = (checked: boolean) => {
    if (!isActive) {
      console.log("Entity is not active, cannot change status.");
      return;
    }

    if (checked) {
      setOpen(true);
    } else {
      dogMarkReleased();
    }
  };

  const dogMarkReleased = () => {
    const formData = new FormData();
    formData.append("dogImpoundmentDate", "");

    mutateEvent.mutate(
      {
        entityId,
        eventType: "dogReleaseFromImpoundment",
        entityType,
        body: formData,
      },
      {
        onSuccess: () => {
          setToast({
            status: "success",
            label: "Dog Released",
            message: "Successfully marked dog as released",
          });
          entityRefetch();
          setIsSwitchOn(false);
        },
        onError: (error: any) => {
          setToast({
            status: "error",
            label: "Error Updating Dog Status",
            message: error.message,
          });
          setIsSwitchOn(true);
        },
      },
    );
  };

  const handleMarkImpounded = (date: string) => {
    console.log(date);

    if (!date) {
      setError("Please select a date.");
      return;
    }

    const selectedDate = startOfDay(parseISO(date));
    const today = startOfDay(new Date());

    if (isAfter(selectedDate, today)) {
      setError("Please select a date that is today or in the past.");
      return;
    }

    const formData = new FormData();
    formData.append("dogImpoundmentDate", date);
    mutateEvent.mutate(
      {
        entityId,
        eventType: "dogImpoundment",
        entityType,
        body: formData,
      },
      {
        onSuccess: () => {
          setToast({
            status: "success",
            label: "Dog Marked as Impounded",
            message: "Successfully marked dog as impounded.",
          });
          entityRefetch();
          handleDialogClose();
        },
        onError: (error: any) => {
          setToast({
            status: "error",
            label: "Error Updating Dog Status",
            message: error.message,
          });
        },
      },
    );
  };

  const handleDialogClose = () => {
    setIsSwitchOn(isImpounded);
    setOpen(false);
    setDogImpoundmentDate("");
    setError(null);
  };

  return (
    <div className="flex flex-col gap-4">
      <div className="flex items-center gap-4">
        <p className="w-[180px] font-semibold">Impounded Dog</p>
        <Switch
          checked={isSwitchOn}
          onCheckedChange={handleSwitchChange}
          disabled={entityIsFetching || !isActive}
        />
        {!isActive && (
          <span className="text-sm text-red-500">
            This account is not active.
          </span>
        )}
        {entityIsFetching && (
          <>
            <CgSpinner className="animate-spin text-red-500" size={28} />
            <span className="animate-pulse">Loading...</span>
          </>
        )}
      </div>
      <Dialog
        open={open}
        onOpenChange={(isOpen) => {
          if (!isOpen) {
            handleDialogClose();
          }
        }}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirm Action</DialogTitle>
            <DialogDescription className="pt-4 text-neutral-800">
              <Label className="text-neutral-800" htmlFor="impoundmentDate">
                Please enter the impoundment date:
              </Label>
              <Input
                id="impoundmentDate"
                type="date"
                value={dogImpoundmentDate}
                onChange={(e) => {
                  setDogImpoundmentDate(e.target.value);
                  setError(null);
                }}
                className="w-full"
                placeholder="Select date"
              />
              {error && <p className="mt-2 text-sm text-red-500">{error}</p>}
              <p className="mt-3">
                Are you sure you want to mark this dog as impounded? This action
                will notify relevant parties and update the status in the
                system.
              </p>
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={handleDialogClose}>
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={() => handleMarkImpounded(dogImpoundmentDate)}
              disabled={entityIsFetching || !dogImpoundmentDate}
            >
              {entityIsFetching ? (
                <span className="animate-pulse">Processing...</span>
              ) : (
                <span>Confirm</span>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default ImpoundedDog;
