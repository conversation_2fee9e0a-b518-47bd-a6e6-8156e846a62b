"use client";
import FormBuilder from "@/components/forms/formbuilder/FormBuilder";
import LoadingSpinner from "@/components/ui/LoadingSpinner";
import { dogOwnerTransferForm } from "@/lib/configuration/profile-events/dogOwnerTransferForm";
import { useGetProfile } from "@/hooks/api/useProfiles";
import { useSearchParams } from "next/navigation";
import React from "react";

const DogTransferOfOwnership = () => {
  const params = useSearchParams();
  const fetchData = params.get("fetchData");
  const entityType = params.get("entityType");
  const entityId = params.get("entityId");

  if (fetchData === "true") {
    return <RenewWithData entitytype={entityType} entityId={entityId} />;
  } else {
    return <FormBuilder form={dogOwnerTransferForm} />;
  }
};

const RenewWithData = ({
  entityId,
  entitytype,
}: {
  entitytype: string | null;
  entityId: string | null;
}) => {
  const profile = useGetProfile(entitytype as string, entityId as string);

  if (profile.isLoading) return <LoadingSpinner />;
  if (profile.isError) return <div>Failed to load data</div>;

  const dog = profile.data?.dog;

  return <FormBuilder form={dogOwnerTransferForm} prevData={dog} />;
};

export default DogTransferOfOwnership;
