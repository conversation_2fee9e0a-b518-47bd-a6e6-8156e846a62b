import Loading from "@/app/(app)/loading";
import { useGetEntityQuery } from "@/hooks/api/useProfiles";
import { Dog } from "@/types/DogType";
import { useRouter, useSearchParams } from "next/navigation";
import React, { useEffect, useState } from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import {
  AlertCircle,
  PawPrint,
} from "lucide-react";
import ImpoundedDog from "./ImpoundedDog";


export default function Impounded({
  status,
  entityType,
}: {
  status: string;
  entityType: string;
}) {
  const [impoundedDogs, setImpoundedDogs] = useState<Dog[]>([]);
  const [totalPages, setTotalPages] = useState(1);
  const [totalElements, setTotalElements] = useState(0);

  const router = useRouter();
  const searchParams = useSearchParams();
  const page = searchParams.get("page") || "0";
  const size = searchParams.get("size") || "10";

  const { data, isLoading, isFetching, isError, refetch } = useGetEntityQuery({
    entityType: entityType,
    status: status,
    page: Number(page),
    size: Number(size),
  });

  console.log(data)

  useEffect(() => {
    if (data) {
      setImpoundedDogs(data.content);
      setTotalPages(data.totalPages);
      setTotalElements(data.totalElements);
    }
  }, [data]);

  const filteredDogs = impoundedDogs;

  if (isLoading || isFetching) {
    return <Loading fixed={false} />;
  }

  if (isError) {
    return (
      <div className="flex items-center justify-center p-12 text-red-600">
        <AlertCircle className="mr-2 h-6 w-6" />
        <span className="text-lg font-medium">
          Error Loading Impounded Dogs
        </span>
      </div>
    );
  }

  return (
    <div className="flex flex-col gap-2 overflow-y-auto overflow-x-hidden px-4 py-8">
      <div className="container mx-auto max-w-6xl">
        <div className="mb-8 text-center">
          <h1 className="text-3xl font-bold text-gray-800">Impounded Dogs</h1>
          <p className="mt-2 text-gray-600">
            Manage dogs currently in custody.
          </p>
        </div>

        {/* Page Size Controls */}
        <div className="mb-6 flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-600">Rows per page</span>
            <Select
              value={size}
              onValueChange={(newSize) => {
                const params = new URLSearchParams(searchParams.toString());
                params.set("size", newSize);
                params.set("page", "0"); // Reset to first page when changing size
                router.push(`?${params.toString()}`);
              }}
            >
              <SelectTrigger className="w-20 rounded-md border shadow-sm">
                <SelectValue placeholder={size} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="5">5</SelectItem>
                <SelectItem value="10">10</SelectItem>
                <SelectItem value="20">20</SelectItem>
                <SelectItem value="50">50</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="mt-4 text-sm text-gray-600 sm:mt-0">
            <span className="font-medium">{totalElements}</span> total impounded
            dogs
          </div>
        </div>

        {filteredDogs.length === 0 ? (
          <div className="rounded-lg border bg-white p-12 text-center">
            <div className="mb-4 flex justify-center">
              <PawPrint className="h-12 w-12 text-gray-300" />
            </div>
            <h3 className="mb-2 text-lg font-medium text-gray-700">
              No Dogs Found
            </h3>
            <p className="text-gray-500">
              There are no impounded dogs at this time.
            </p>
          </div>
        ) : (
          <div className="space-y-6">
            {filteredDogs.map((dog) => (
              <ImpoundedDog 
                dog={dog} 
                key={dog.entityId} 
                refetch={refetch}
              />
            ))}
          </div>
        )}

        {/* Pagination Controls */}
        {totalPages > 1 && (
          <div className="mt-8 flex justify-center">
            <Pagination>
              <PaginationContent>
                <PaginationItem>
                  <PaginationPrevious
                    onClick={() => {
                      if (Number(page) > 0) {
                        const params = new URLSearchParams(
                          searchParams.toString(),
                        );
                        params.set("page", (Number(page) - 1).toString());
                        router.push(`?${params.toString()}`);
                      }
                    }}
                    className={
                      Number(page) === 0 ? "pointer-events-none opacity-50" : ""
                    }
                  />
                </PaginationItem>

                {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                  const pageNum = Number(page);
                  let pageToShow;

                  if (totalPages <= 5 || pageNum < 3) {
                    pageToShow = i;
                  } else if (pageNum >= totalPages - 3) {
                    pageToShow = totalPages - 5 + i;
                  } else {
                    pageToShow = pageNum - 2 + i;
                  }

                  if (pageToShow < 0 || pageToShow >= totalPages) return null;

                  return (
                    <PaginationItem key={pageToShow}>
                      <PaginationLink
                        isActive={pageToShow === Number(page)}
                        onClick={() => {
                          const params = new URLSearchParams(
                            searchParams.toString(),
                          );
                          params.set("page", pageToShow.toString());
                          router.push(`?${params.toString()}`);
                        }}
                      >
                        {pageToShow + 1}
                      </PaginationLink>
                    </PaginationItem>
                  );
                })}

                {totalPages > 5 && Number(page) < totalPages - 3 && (
                  <PaginationItem>
                    <PaginationEllipsis />
                  </PaginationItem>
                )}

                {totalPages > 5 && Number(page) < totalPages - 3 && (
                  <PaginationItem>
                    <PaginationLink
                      onClick={() => {
                        const params = new URLSearchParams(
                          searchParams.toString(),
                        );
                        params.set("page", (totalPages - 1).toString());
                        router.push(`?${params.toString()}`);
                      }}
                    >
                      {totalPages}
                    </PaginationLink>
                  </PaginationItem>
                )}

                <PaginationItem>
                  <PaginationNext
                    onClick={() => {
                      if (Number(page) < totalPages - 1) {
                        const params = new URLSearchParams(
                          searchParams.toString(),
                        );
                        params.set("page", (Number(page) + 1).toString());
                        router.push(`?${params.toString()}`);
                      }
                    }}
                    className={
                      Number(page) >= totalPages - 1
                        ? "pointer-events-none opacity-50"
                        : ""
                    }
                  />
                </PaginationItem>
              </PaginationContent>
            </Pagination>
          </div>
        )}
      </div>
    </div>
  );
}
