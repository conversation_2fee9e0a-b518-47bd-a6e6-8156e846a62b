import { Dog } from "@/types/DogType";
import { Individual } from "@/types/IndividualType";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  AlertCircle,
  PawPrint,
  User,
  DollarSign,
  CheckIcon,
  Paperclip,
  Dog as DogIcon,
} from "lucide-react";
import Link from "next/link";
import { useAtom } from "jotai";
import { toastAtom } from "@/components/ui/toast/toast";
import {
  AlertDialog,
  AlertDialogTrigger,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogAction,
  AlertDialogCancel,
} from "@/components/ui/alert-dialog";
import {
  Accordion,
  AccordionItem,
  AccordionTrigger,
  AccordionContent,
} from "@/components/ui/accordion";
import { useGetEntity, useHandleEvent } from "@/hooks/api/useProfiles";
import { License } from "@/types/LicenseType";
import { cn } from "@/lib/utils";

export default function ImpoundedDog({
  dog,
  refetch,
}: {
  dog: Dog;
  refetch: () => void;
}) {
  const [_, setToast] = useAtom(toastAtom);
  const mutateEvent = useHandleEvent();
  const {
    data: dogData,
    isLoading,
    isError,
  } = useGetEntity(dog.entityType, dog.entityId);

  const dogMarkReleased = ({
    entityId,
    entityType,
  }: {
    entityId: string;
    entityType: string;
  }) => {
    const formData = new FormData();
    formData.append("dogImpoundmentDate", "");

    mutateEvent.mutate(
      {
        entityId,
        eventType: "dogReleaseFromImpoundment",
        entityType,
        body: formData,
      },
      {
        onSuccess: () => {
          refetch();
          setToast({
            status: "success",
            label: "Dog Released to Owner",
            message: "Successfully marked dog as released",
          });
        },
        onError: (error: any) => {
          setToast({
            status: "error",
            label: "Error Updating Dog Status",
            message: error.message,
          });
        },
      },
    );
  };

  const dogMarkRelinquished = ({
    entityId,
    entityType,
  }: {
    entityId: string;
    entityType: string;
  }) => {
    const formData = new FormData();
    formData.append("dogImpoundmentDate", "");

    mutateEvent.mutate(
      {
        entityId,
        eventType: "dogRelinquished",
        entityType,
        body: formData,
      },
      {
        onSuccess: () => {
          refetch();
          setToast({
            status: "success",
            label: "Dog relinquished",
            message: "Successfully marked dog as relinquished",
          });
        },
        onError: (error: any) => {
          setToast({
            status: "error",
            label: "Error Updating Dog Status",
            message: error.message,
          });
        },
      },
    );
  };

  const { events, fees, documents, ...other } = dog;

  const renderFeesBreakdown = (dog: Dog) => {
    const dogFees = dog.fees;
    if (!dogFees || !dogFees.feeSets) {
      return (
        <p className="text-sm italic text-gray-500">
          No fee information available.
        </p>
      );
    }

    const totalOutstanding = Number(
      dogFees.totals?.totalOutstandingAmount || 0,
    ).toFixed(2);

    return (
      <div className="space-y-3">
        {dogFees.feeSets.map((feeSet: any, idx: number) => (
          <div
            key={idx}
            className="rounded-md border border-gray-200 bg-white p-4"
          >
            <div className="mb-3 flex items-center justify-between">
              <h4 className="text-sm font-medium text-gray-700">
                {feeSet.label}
              </h4>
              <span className="rounded-md bg-gray-100 px-3 py-1 text-sm font-medium text-gray-700">
                ${Number(feeSet.totals.totalOutstandingAmount).toFixed(2)}
              </span>
            </div>
            <div className="space-y-2">
              {feeSet.fees
                .sort((a: any, b: any) => a.feeName.localeCompare(b.feeName))
                .map((fee: any, feeIdx: number) => (
                  <div
                    key={feeIdx}
                    className="flex items-center justify-between text-sm"
                  >
                    <div className="flex items-center">
                      <CheckIcon size={14} className="mr-2 text-gray-500" />
                      <span className="text-gray-600">{fee.feeName}</span>
                    </div>
                    <span className="font-medium text-gray-700">
                      ${Number(fee.feeAmount).toFixed(2)}
                    </span>
                  </div>
                ))}
            </div>
          </div>
        ))}
        <div className="flex items-center justify-between rounded-md border border-gray-300 bg-gray-50 p-4">
          <span className="font-medium text-gray-700">Total Outstanding</span>
          <span className="text-lg font-medium text-gray-800">
            ${totalOutstanding}
          </span>
        </div>
      </div>
    );
  };

  const firstLicenseIsActive = dogData?.license?.[0].status === "Active";

  return (
    <Accordion type="single" collapsible>
      <AccordionItem value={dog.entityId}>
        <AccordionTrigger className="flex flex-wrap items-center gap-4 border-b bg-gray-100 p-4 hover:no-underline">
          {/* Dog Info Section */}
          <div className="flex items-center gap-4">
            <span className="mr-2 flex h-10 w-10 items-center justify-center rounded bg-gray-200">
              <DogIcon size={24} className="text-gray-700" />
            </span>
            <div className="flex flex-col">
              <Link
                href={`/profile/${dog.entityType}/${dog.entityId}?tab=profile`}
                target="_blank"
                className="w-fit text-left text-xl font-medium px-2 text-blue-500 hover:text-blue-600 hover:underline"
              >
                {dog.dogName || "Unknown Dog"}
              </Link>
              <div className="mt-1 flex items-center gap-2">
                <Badge variant="outline" className="bg-gray-50">
                  Tag #: {dog?.tagNumber ?? "None"}
                </Badge>

                {dogData?.license?.[0] && (
                  <Badge
                    className={cn(
                      "",
                      firstLicenseIsActive
                        ? "bg-green-100 hover:bg-green-100 text-green-800 border"
                        : "bg-red-100 hover:bg-red-100 text-red-800",
                    )}
                  >
                    License: {dogData.license[0].status}
                  </Badge>
                )}

                {dog.fees?.totals?.totalOutstandingAmount !== undefined && (
                  <Badge variant="outline" className="bg-gray-50">
                    Outstanding: $
                    {Number(dog.fees.totals.totalOutstandingAmount).toFixed(2)}
                  </Badge>
                )}

                <Badge variant="outline" className="bg-gray-50">
                  Impounded Date: {dog.dogImpoundmentDate || "Unknown"}
                </Badge>

              </div>
            </div>
          </div>
          {/* Action Buttons */}
          <div
            className="ml-auto mr-4 flex gap-3"
            onClick={(e) => e.stopPropagation()}
          >
            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button variant="outline" size="sm">
                  Relinquish
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Confirm Relinquishment</AlertDialogTitle>
                  <AlertDialogDescription>
                    Are you sure you want to mark{" "}
                    <span className="font-semibold">{dog.dogName}</span> as
                    relinquished? This action cannot be undone.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                  <AlertDialogAction
                    onClick={() =>
                      dogMarkRelinquished({
                        entityId: dog.entityId,
                        entityType: dog.entityType,
                      })
                    }
                    className="bg-gray-700 hover:bg-gray-800"
                  >
                    Confirm Relinquishment
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>

            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button
                  size="sm"
                  className="bg-gray-700 hover:bg-gray-800"
                  disabled={!firstLicenseIsActive}
                >
                  Release to Owner
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Confirm Release</AlertDialogTitle>
                  <AlertDialogDescription>
                    Are you sure you want to mark{" "}
                    <span className="font-semibold">{dog.dogName}</span> as
                    released to owner?
                    {dog.fees?.totals?.totalOutstandingAmount > 0 && (
                      <div className="mt-2 rounded border border-gray-200 bg-gray-50 p-2 text-gray-800">
                        <div className="flex items-center">
                          <AlertCircle className="mr-2 h-4 w-4" />
                          <span className="font-medium">Outstanding Fees</span>
                        </div>
                        <p className="mt-1">
                          There are unpaid fees of $
                          {Number(
                            dog.fees.totals.totalOutstandingAmount,
                          ).toFixed(2)}{" "}
                          for this dog.
                        </p>
                      </div>
                    )}
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                  <AlertDialogAction
                    onClick={() =>
                      dogMarkReleased({
                        entityId: dog.entityId,
                        entityType: dog.entityType,
                      })
                    }
                    className="bg-gray-700 hover:bg-gray-800"
                  >
                    Confirm Release
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </div>
        </AccordionTrigger>

        <AccordionContent className="bg-white p-6">
          <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
            <DogInformation
              dogInfo={dogData}
              isLoading={isLoading}
              isError={isError}
            />
            <OwnerInfo
              dogData={dogData}
              isLoading={isLoading}
              isError={isError}
            />
            <div className="space-y-4">
              <div className="mb-2 flex items-center text-gray-700">
                <DollarSign className="mr-2 h-5 w-5 text-gray-500" />
                <h3 className="text-base font-medium">Fees and Charges</h3>
              </div>
              {renderFeesBreakdown(dog)}
            </div>
          </div>
        </AccordionContent>
      </AccordionItem>
    </Accordion>
  );
}

const DogInformation = ({
  dogInfo,
  isLoading,
  isError,
}: {
  dogInfo: {
    license: License[];
    dog: Dog;
  };
  isLoading: boolean;
  isError: boolean;
}) => {
  if (isLoading)
    return (
      <div className="space-y-3">
        <div className="mb-2 flex items-center text-gray-700">
          <PawPrint className="mr-2 h-5 w-5 text-gray-500" />
          <h3 className="text-base font-medium">Dog Information</h3>
        </div>
        <div className="animate-pulse">Loading Dog Information...</div>
      </div>
    );
  if (isError) return <div>Error loading dog information</div>;

  const firstLicense = dogInfo?.license?.[0] || null;
  const dog = dogInfo?.dog || null;

  return (
    <div className="space-y-4">
      <div className="mb-2 flex items-center text-gray-700">
        <PawPrint className="mr-2 h-5 w-5 text-gray-500" />
        <h3 className="text-base font-medium">Dog Information</h3>
      </div>
      <div className="space-y-3 rounded-md border border-gray-200 p-4">
        <div className="flex items-center justify-between">
          <span className="text-gray-500">Breed:</span>
          <span className="font-medium text-gray-700">
            {dog.dogBreed || "Unknown"}
          </span>
        </div>
        <div className="flex items-center justify-between">
          <span className="text-gray-500">Color:</span>
          <span className="font-medium text-gray-700">
            {dog.dogPrimaryColor && dog.dogSecondaryColor
              ? `${dog.dogPrimaryColor} / ${dog.dogSecondaryColor}`
              : dog.dogPrimaryColor || "Unknown"}
          </span>
        </div>
        {dog.microchipNumber && (
          <div className="flex items-center justify-between">
            <span className="text-gray-500">Microchip:</span>
            <span className="font-medium text-gray-700">
              {dog.microchipNumber}
            </span>
          </div>
        )}
      </div>
      <div className="mb-2 flex items-center text-gray-700">
        <Paperclip className="mr-2 h-5 w-5 text-gray-500" />
        <h3 className="text-base font-medium">License Information</h3>
      </div>
      <div className="space-y-3 rounded-md border border-gray-200 p-4">
        <div className="flex items-center justify-between">
          <span className="text-gray-500">License Status:</span>
          <span className={cn("rounded-md bg-gray-200 px-3 py-1 text-sm font-medium text-gray-700",
            firstLicense?.status === "Active"
              ? "bg-green-100 text-green-800"
              : "bg-red-100 text-red-800",
          )}>
            {firstLicense?.status ?? "Unknown"}
          </span>
        </div>
        <div className="flex items-center justify-between">
          <span className="text-gray-500">Impounded Date:</span>
          <span className="font-medium text-gray-700">
            {dog?.dogImpoundmentDate ?? "Unknown"}
          </span>
        </div>
      </div>
    </div>
  );
};

const getPrimaryContact = (owner: Individual): string => {
  const primaryContact = owner.contacts.find(
    (contact) => contact?.isPrimary && contact.type === "Phone",
  );
  return primaryContact?.value || "No contact info";
};

const getAddress = (owner: Individual): string => {
  if (!owner.addresses || owner.addresses.length === 0)
    return "No address on file";
  const address = owner.addresses[0];
  const parts = [
    address.streetAddress,
    address.streetAddress2,
    address.city,
    address.state,
    address.zip,
  ].filter(Boolean);
  return parts.join(", ");
};

const OwnerInfo = ({
  dogData,
  isLoading,
  isError,
}: {
  dogData: { individual: Individual[] };
  isLoading: boolean;
  isError: boolean;
}) => {
  if (isLoading)
    return (
      <div className="space-y-3">
        <div className="mb-2 flex items-center text-gray-700">
          <User className="mr-2 h-5 w-5 text-gray-500" />
          <h3 className="text-base font-medium">Owner Information</h3>
        </div>
        <div className="animate-pulse">Loading Owner Information...</div>
      </div>
    );
  if (isError) return <div>Error loading owner information</div>;

  const owner = dogData?.individual[0];

  return (
    <div className="space-y-3">
      <div className="mb-2 flex items-center text-gray-700">
        <User className="mr-2 h-5 w-5 text-gray-500" />
        <h3 className="text-base font-medium">Owner Information</h3>
      </div>
      {owner ? (
        <div className="rounded-md border border-gray-200 p-4">
          <div className="mb-4">
            <Link
              href={`/profile/${owner.entityType}/${owner.entityId}?tab=profile`}
              className="text-lg font-medium text-blue-600 hover:text-blue-700 hover:underline"
              target="_blank"
            >
              {owner.firstName} {owner.lastName}
            </Link>
          </div>
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <span className="w-16 text-gray-500 shrink-0">Phone:</span>
              <span className="font-medium text-gray-700">
                {getPrimaryContact(owner)}
              </span>
            </div>
            <div className="flex items-center gap-2">
              <span className="w-16 text-gray-500 shrink-0">Email:</span>
              <span className="font-medium text-gray-700">
                {owner.contacts.find(
                  (c: {
                    type: string;
                    group: string;
                    value: string;
                    id: number;
                  }) => c.type === "Email",
                )?.value || "No email"}
              </span>
            </div>
            <div className="flex items-start gap-2">
              <span className="w-16 text-gray-500 shrink-0">Address:</span>
              <span className="font-medium text-gray-700">
                {getAddress(owner)}
              </span>
            </div>
          </div>
        </div>
      ) : (
        <div className="rounded-md border border-gray-200 p-4 py-4 text-center">
          <p className="mb-2 text-gray-500">No owner information available</p>
        </div>
      )}
    </div>
  );
};
