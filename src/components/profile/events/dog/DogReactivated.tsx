import EventDialog from "../EventDialog";

const DogReactivated = ({
  event,
  entityType,
  entityId,
}: {
  event: any;
  entityType: string;
  entityId: string;
}) => {
  return (
    <EventDialog
      event={event}
      title="You are about to reactivate this dog."
      description="Are you sure you want to do this?"
      entityId={entityId}
      entityType={entityType}
    />
  );
};

export default DogReactivated;
