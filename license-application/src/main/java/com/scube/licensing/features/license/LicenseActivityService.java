package com.scube.licensing.features.license;

import com.scube.licensing.features.license.dto.LicenseActivityAddFeeRequest;
import com.scube.licensing.features.license.dto.LicenseActivityDateResponse;
import com.scube.licensing.features.license.exception.LicenseStatusNotFoundException;
import com.scube.licensing.features.license.fee.sql_fees.*;
import com.scube.licensing.features.license.partial_save.create_final_license.CreateFinalLicenseResponse;
import com.scube.licensing.features.license.renewals.LicenseRenewalCommand;
import com.scube.licensing.infrastructure.axon.AxonGateway;
import com.scube.licensing.infrastructure.db.entity.license.License;
import com.scube.licensing.infrastructure.db.entity.license.LicenseActivity;
import com.scube.licensing.infrastructure.db.repository.license.LicenseActivityRepository;
import com.scube.rabbit.core.AmqpGateway;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.ObjectUtils;
import org.springframework.web.server.ResponseStatusException;

import java.math.BigDecimal;
import java.util.List;
import java.util.UUID;

@Service
@AllArgsConstructor
@Slf4j
public class LicenseActivityService {
    private final LicenseService licenseService;
    private final LicenseActivityRepository licenseActivityRepository;
    private final AmqpGateway amqpGateway;
    private final AxonGateway axonGateway;
    private final LicenseFeeService licenseFeeService;

    public void issuedFees(License license, LicenseActivity licenseActivity, boolean isResident) {
        var calculationFees = licenseFeeService.calculateLicenseFee(license);
        issuedFees(calculationFees, licenseActivity, isResident);
    }

    public void issuedFees(List<LicenseCalculationResponse> calculationFees, LicenseActivity licenseActivity, boolean isResident) {
        List<String> feeCodes = calculationFees.stream()
                .map(LicenseCalculationResponse::getFeeCode)
                .toList();

        calculationFees.forEach(f -> {
            licenseActivity.addLicenseFee(f.getFeeCode(), f.getFeeAmount());
        });

        List<Fee> queriedFees = amqpGateway.queryResult(new GetFeesQuery(feeCodes)).orElseThrow().fees();

        for (Fee item : queriedFees) {
            if (item.getOperation().equals(FeeType.FLAT)) {
                licenseActivity.setFeeAmountByFeeCode(item.getKey(), item.getAmount());
            }
        }

        var feeAmount = licenseActivity.getTotalAmount();
        for (Fee item : queriedFees) {
            if (item.isPercentage()) {
                var percentAmount = item.getAmount();
                Assert.notNull(percentAmount, "Percentage for %s cannot be null".formatted(item.getKey()));
                Assert.isTrue(percentAmount.compareTo(BigDecimal.ZERO) > 0, "Percentage for %s must be greater than zero.".formatted(item.getKey()));
                percentAmount = percentAmount.divide(BigDecimal.valueOf(100));
                var amount = feeAmount.multiply(percentAmount);
                licenseActivity.setFeeAmountByFeeCode(item.getKey(), amount);
            }
            if (item.isResidentOnly()) {
                if (isResident) continue;
                // remove the fee
                licenseActivity.removeFeeByFeeCode(item.getKey());
            }
        }
    }

    public void removeAndReIssueFees(License license, LicenseActivity licenseActivity, boolean isResident) {
        licenseActivity.clearFees();
        issuedFees(license, licenseActivity, isResident);
    }

    @Transactional
    public CreateFinalLicenseResponse removeAndReIssueUnpaidActivityFees(UUID entityId, boolean autoApproval) {
        var license = findLicenseAndAddReIssueStatus(entityId);

        var activities = license.getLicenseActivities()
                .stream()
                .filter(LicenseActivity::isUnpaid)
                .toList();
        activities.forEach(license.getLicenseActivities()::remove);

        license.setValidFromDate(null);
        license.setValidToDate(null);

        try {
            var durationOptional = license.getDuration();
            if (durationOptional.isEmpty() || durationOptional.get() <= 0)
                throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "License with entity id " + entityId + " has no duration");
            axonGateway.sendAndWait(new LicenseRenewalCommand(license.getUuid(),
                    durationOptional.get(),
                    autoApproval,
                    license.getStartYear(),
                    license.getEndYear())
            );
        } catch (NumberFormatException ex) {
            log.error("Error while parsing license duration", ex);
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Failed to parse license duration to integer");
        }

        licenseService.save(license);

        return new CreateFinalLicenseResponse(license.getUuid(), "license");
    }

    @Transactional
    public CreateFinalLicenseResponse removeAndReIssueUnpaidActivityFees(UUID entityId, Long activityId, boolean isResident) {
        var license = findLicenseAndAddReIssueStatus(entityId);

        var activity = license.getLicenseActivities().stream()
                .filter(licenseActivity -> licenseActivity.getId().equals(activityId))
                .findFirst()
                .orElseThrow();
        removeAndReIssueFees(license, activity, isResident);

        licenseService.save(license);

        return new CreateFinalLicenseResponse(license.getUuid(), "license");
    }

    private License findLicenseAndAddReIssueStatus(UUID entityId) {
        var license = licenseService.findLicenseByEntityId(new LicenseService.FindLicenseByEntityIdQuery(entityId))
                .orElseThrow();
        var setting = licenseService.findSettingByLicenseTypeIdOrElseThrow(license.getLicenseType().getId());
        var licenseStatus = setting.getOnFormSubmitLicenseStatus();
        if (ObjectUtils.isEmpty(licenseStatus))
            throw new LicenseStatusNotFoundException("OnFormSubmit license status not found");

        license.setLicenseStatus(licenseStatus);
        return license;
    }

    public LicenseActivityDateResponse determineLicenseActivityDates(UUID licenseEntityId) {
        var result = licenseActivityRepository.determineLicenseActivityDates(licenseEntityId);
        var validFrom = result.get("valid_from_date");
        var validTo = result.get("valid_to_date");
        return new LicenseActivityDateResponse(validFrom, validTo);
    }

    public void addLicenseFee(UUID activityId, LicenseActivityAddFeeRequest request) {
        LicenseActivity licenseActivity = licenseActivityRepository.findByUuidOrThrow(activityId);
        licenseActivity.addLicenseFee(request.getFeeCode(), request.getFeeAmount(), request.getReason());
        licenseActivityRepository.save(licenseActivity);
    }
}
