package com.scube.licensing.features.merge_request;

import com.c4_soft.springaddons.security.oidc.OpenidClaimSet;
import com.scube.licensing.features.merge_request.dtos.CreateMergeRequestDto;
import com.scube.licensing.features.merge_request.dtos.me.LoggedInUserMergeRequestResponse;
import com.scube.licensing.features.merge_request.dtos.me.LoggedInUserMergeRequestResponseLineItem;
import com.scube.licensing.features.permission.Permissions;
import com.scube.licensing.infrastructure.db.entity.merge_request.MergeRequestStatusEnum;
import jakarta.annotation.security.RolesAllowed;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

import java.util.UUID;

@RestController
@RequestMapping("me/merge-requests")
@Slf4j
@AllArgsConstructor
public class LoggedInUserMergeRequestController {
    private final MergeRequestService mergeRequestService;

    @PostMapping
    @ResponseStatus(HttpStatus.ACCEPTED)
    @RolesAllowed(Permissions.LoggedInUserMergeRequest.CREATE_MERGE_REQUEST)
    public void createMergeRequest(@AuthenticationPrincipal OpenidClaimSet jwt, @RequestBody CreateMergeRequestDto createMergeRequestDto) {
        mergeRequestService.createMergeRequest(UUID.fromString(jwt.getSubject()), createMergeRequestDto);
    }

    @GetMapping
    @ResponseStatus(HttpStatus.OK)
    @RolesAllowed(Permissions.LoggedInUserMergeRequest.GET_MERGE_REQUESTS)
    public LoggedInUserMergeRequestResponse getMergeRequests(@AuthenticationPrincipal OpenidClaimSet jwt, @RequestParam(required = false) MergeRequestStatusEnum... statuses) {
        var requests = mergeRequestService.getMergeRequestsByRequestUserReadOnly(UUID.fromString(jwt.getSubject()), statuses);
        return new LoggedInUserMergeRequestResponse(requests);
    }

    @GetMapping("{mergeRequestEntityId}")
    @ResponseStatus(HttpStatus.OK)
    @RolesAllowed(Permissions.LoggedInUserMergeRequest.GET_MERGE_REQUEST)
    @PreAuthorize("isOwnerOfMergeRequest(#mergeRequestEntityId)")
    public LoggedInUserMergeRequestResponseLineItem getMergeRequest(@PathVariable UUID mergeRequestEntityId) {
        return new LoggedInUserMergeRequestResponseLineItem(mergeRequestService.getMergeRequestReadOnly(mergeRequestEntityId));
    }
}