package com.scube.licensing.features.merge_request;

import com.scube.licensing.features.profile.ProfileService;
import com.scube.licensing.features.profile.dto.config.IAssociableDto;
import com.scube.licensing.infrastructure.db.entity.merge_request.MergeRequest;
import com.scube.licensing.infrastructure.db.entity.participant.Participant;
import lombok.experimental.UtilityClass;
import org.springframework.lang.NonNull;

import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

@UtilityClass
public class MergeRequestUtil {
    @NonNull
    public static Map<UUID, IAssociableDto> buildResidentDtos(List<MergeRequest> requests, ProfileService profileService) {
        var userUUIDS = requests.stream()
                .flatMap(x -> x.getResidentUUIDs().stream())
                .collect(Collectors.toSet());
        return profileService.getResidentDtos(userUUIDS).stream()
                .collect(Collectors.toMap(IAssociableDto::getEntityId, x -> x));
    }

    @NonNull
    public static Map<UUID, Participant> buildResident(List<MergeRequest> requests, ProfileService profileService) {
        var userUUIDS = requests.stream()
                .flatMap(x -> x.getResidentUUIDs().stream())
                .collect(Collectors.toSet());
        return profileService.getResidents(userUUIDS).stream()
                .collect(Collectors.toMap(Participant::getUuid, x -> x));
    }
}