package com.scube.licensing.features.merge_request;

import com.scube.licensing.features.merge_request.dtos.*;
import com.scube.licensing.features.permission.Permissions;
import com.scube.licensing.features.profile.ProfileService;
import com.scube.licensing.infrastructure.db.entity.merge_request.MergeRequestStatusEnum;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.annotation.security.RolesAllowed;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.UUID;

@RestController
@RequestMapping("/merge-requests")
@Slf4j
@AllArgsConstructor
@Validated
public class MergeRequestController {
    private final MergeRequestService mergeRequestService;
    private final ProfileService profileService;

    @PostMapping
    @ResponseStatus(HttpStatus.ACCEPTED)
    @RolesAllowed(Permissions.MergeRequest.CREATE_MERGE_REQUEST)
    public void createMergeRequest(@RequestParam UUID requestedUserId, @RequestBody CreateMergeRequestDto createMergeRequestDto) {
        mergeRequestService.createMergeRequest(requestedUserId, createMergeRequestDto);
    }

    @GetMapping
    @ResponseStatus(HttpStatus.OK)
    @RolesAllowed(Permissions.MergeRequest.GET_MERGE_REQUESTS)
    public GetAllMergeRequestResponse getMergeRequests(@RequestParam(required = false) List<MergeRequestStatusEnum> statuses) {
        return new GetAllMergeRequestResponse(mergeRequestService.getMergeRequestsReadOnly(statuses), profileService);
    }

    @GetMapping("/requested-user/{requestedUserId}")
    @ResponseStatus(HttpStatus.OK)
    @RolesAllowed(Permissions.MergeRequest.GET_MERGE_REQUESTS_BY_REQUEST_USER)
    public GetByRequestedUserMergeRequestResponse getMergeRequestsByRequestUser(@PathVariable UUID requestedUserId, @RequestParam(required = false) MergeRequestStatusEnum... statuses) {
        var mergeRequests = mergeRequestService.getMergeRequestsByRequestUserReadOnly(requestedUserId, statuses);
        return new GetByRequestedUserMergeRequestResponse(requestedUserId, mergeRequests, profileService);
    }

    @PostMapping("approve")
    @ResponseStatus(HttpStatus.OK)
    @Operation(summary = "On approve transfer licenses from existingUserIds to requestedUserId", description = "This can be used to transfer licenses from one user to another." +
            " The requestedUserId is the user the license will be transferred to and the existingUserId is the user the license will be transferred from." +
            "When this endpoint is used, all pending merge requests for the requestedUserId will be approved")
    @RolesAllowed(Permissions.MergeRequest.APPROVE_MERGE_REQUEST)
    public void approveMergeRequest(@RequestBody @Valid ApproveMergeRequest approveMergeRequest) {
        mergeRequestService.approveMergeRequest(approveMergeRequest);
    }

    @PostMapping("reject")
    @ResponseStatus(HttpStatus.OK)
    @RolesAllowed(Permissions.MergeRequest.REJECT_MERGE_REQUEST)
    public void rejectMergeRequest(@RequestBody @Valid RejectMergeRequest rejectMergeRequest) {
        mergeRequestService.rejectMergeRequest(rejectMergeRequest);
    }
}