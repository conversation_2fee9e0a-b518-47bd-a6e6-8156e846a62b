package com.scube.licensing.features.entity_group;

import com.scube.audit.auditable.services.AuditableEntityService;
import com.scube.licensing.features.document.dto.DocumentDto;
import com.scube.licensing.features.entity.AssociableService;
import com.scube.licensing.features.entity_fee.EntityFeeService;
import com.scube.licensing.features.entity_fee.dtos.GetAllEntityFeeResponse;
import com.scube.licensing.features.entity_group.dtos.EntityGroupDto;
import com.scube.licensing.features.entity_group.dtos.EntityGroupRequest;
import com.scube.licensing.features.entity_group.dtos.FeeSetDto;
import com.scube.licensing.features.entity_group.dtos.GetAllEntityGroupResponse;
import com.scube.licensing.features.entity_group.mapper.EntityGroupMapper;
import com.scube.licensing.features.entity_group.mapper.FeeSetMapper;
import com.scube.licensing.features.profile.ProfileService;
import com.scube.licensing.infrastructure.db.entity.association.Associable;
import com.scube.licensing.infrastructure.db.entity.entity_group.EntityGroup;
import com.scube.licensing.infrastructure.db.repository.entity_group.EntityGroupRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;
import java.util.UUID;

@Slf4j
@Service
@RequiredArgsConstructor
@Transactional
public class EntityGroupService extends AuditableEntityService<Long, EntityGroup, EntityGroupRepository> {
    private final ProfileService profileService;
    private final EntityGroupMapper mapper;
    private final AssociableService associableService;
    private final FeeSetMapper feeSetMapper;
    private final EntityFeeService entityFeeService;

    public EntityGroupDto createGroup(EntityGroupRequest request) {
        log.info("Start createGroup...");
        EntityGroup group = new EntityGroup();
        group.createGroup(request);
        repository.save(group);
        log.info("End createGroup...");
        return mapper.toDto(group);
    }

    public EntityGroupDto updateGroup(EntityGroup group, EntityGroupRequest request) {
        log.info("Start updateGroup with groupId {}", group.getUuid());
        group.updateGroup(request);
        repository.save(group);
        log.info("End updateGroup...");
        return mapper.toDto(group);
    }

    public EntityGroupDto addAssociation(UUID groupId, EntityGroupRequest request) {
        EntityGroup group = repository.findByUuidOrThrow(groupId);
        group.addAssociations(request.getAssociations());
        repository.save(group);
        return mapper.toDto(group);
    }

    public EntityGroupDto updateGroup(UUID groupId, EntityGroupRequest request) {
        EntityGroup group = repository.findByUuidOrThrow(groupId);
        return updateGroup(group, request);
    }

    public GetAllEntityGroupResponse getAll(String entityType, UUID entityId) {
        log.info("Start getAllGroups...");
        Associable associable = profileService.getProfileOrElseThrow(entityType, entityId);
        var groups = associable.getEntityGroups().stream()
                .map(mapper::toDto)
                .toList();
        return new GetAllEntityGroupResponse(groups);
    }

    public List<FeeSetDto> getAllFeeSet(String entityType, UUID entityId) {
        GetAllEntityFeeResponse feeResponse = entityFeeService.getAllFees(entityType, entityId);
        Associable associable = profileService.getProfileOrElseThrow(entityType, entityId);
        return associable.getEntityGroups().stream()
                .map(feeSetMapper::mapToDto)
                .toList();
    }

    public EntityGroupDto getGroup(UUID groupId) {
        log.info("Start getGroup with groupId {}", groupId);
        EntityGroup group = repository.findByUuidOrThrow(groupId);
        log.info("End getGroup...");
        return mapper.toDto(group);
    }

    public void deleteGroup(UUID groupId) {
        log.info("Start deleteGroup with groupId {}", groupId);
        EntityGroup group = repository.findByUuidOrThrow(groupId);
        group.removeAssociations();
        repository.delete(group);
        log.info("End deleteGroup...");
    }

    public boolean isOwnerOfEntityGroup(UUID groupId, UUID userId) {
        EntityGroup group = repository.findByUuidOrElseNull(groupId);
        if (group == null) return false;
        return group.permissions().hasOwner(userId);
    }

    public List<DocumentDto> addFiles(UUID feeEntityId, Map<String, MultipartFile> files) {
        log.info("Start addFiles with feeEntityId {}", feeEntityId);
        EntityGroup fee = repository.findByUuidOrThrow(feeEntityId);
        return associableService.addFiles(fee, files);
    }
}