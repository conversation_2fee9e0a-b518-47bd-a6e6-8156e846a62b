package com.scube.licensing.features.merge_request;

import com.scube.licensing.features.merge_request.dtos.ApproveMergeRequest;
import com.scube.licensing.features.merge_request.dtos.CreateMergeRequestDto;
import com.scube.licensing.features.merge_request.dtos.RejectMergeRequest;
import com.scube.licensing.features.participant.ParticipantService;
import com.scube.licensing.infrastructure.axon.AxonGateway;
import com.scube.licensing.infrastructure.db.entity.merge_request.MergeRequest;
import com.scube.licensing.infrastructure.db.entity.merge_request.MergeRequestExistingUser;
import com.scube.licensing.infrastructure.db.entity.merge_request.MergeRequestStatusEnum;
import com.scube.licensing.infrastructure.db.repository.merge_request.MergeRequestRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.web.server.ResponseStatusException;

import java.util.*;
import java.util.function.Consumer;

@Service
@RequiredArgsConstructor
@Transactional
public class MergeRequestService {
    private final MergeRequestRepository mergeRequestRepository;
    private final AxonGateway axonGateway;
    private final ParticipantService participantService;

    private MergeRequest findByEntityId(UUID mergeRequestEntityId) {
        return mergeRequestRepository.findByUuid(mergeRequestEntityId)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Merge request with id " + mergeRequestEntityId + " not found"));
    }

    private void throwIfMergeRequestAlreadyExist(UUID requestedUserId, String licenseNumber, String tagNumber) {
        var exist = mergeRequestRepository.findAllByRequestedUserIdAndStatusIn(requestedUserId, List.of(MergeRequestStatusEnum.PENDING)).stream()
                .anyMatch(mergeRequest -> (!ObjectUtils.isEmpty(mergeRequest.getLicenseNumber()) && mergeRequest.getLicenseNumber().equals(licenseNumber))
                                          || (!ObjectUtils.isEmpty(mergeRequest.getTagNumber()) && mergeRequest.getTagNumber().equals(tagNumber)));
        if (exist) {
            if (!ObjectUtils.isEmpty(licenseNumber) && !ObjectUtils.isEmpty(tagNumber)) {
                throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Request already exists for license number " + licenseNumber + " and tag number " + tagNumber);
            } else if (!ObjectUtils.isEmpty(licenseNumber)) {
                throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Request already exists for license number " + licenseNumber);
            } else if (!ObjectUtils.isEmpty(tagNumber)) {
                throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Request already exists for tag number " + tagNumber);
            }
        }
    }

    private void throwIfExceedMaximumThreeRequests(UUID requestedUserId) {
        var existingMergeRequests = mergeRequestRepository.findAllByRequestedUserIdAndStatusIn(requestedUserId, List.of(MergeRequestStatusEnum.PENDING));
        if (existingMergeRequests.size() >= 3) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "You cannot have more than 3 pending merge requests");
        }
    }

    private List<MergeRequestExistingUser> getMergeUserIds(String licenseNumber, String tagNumber, UUID requestedUserId) {
        var result = new ArrayList<MergeRequestExistingUser>();
        var existSet = new HashSet<UUID>();
        var dbs = mergeRequestRepository.getSearchMergeRequests(licenseNumber, tagNumber, requestedUserId).stream().toList();
        dbs.stream().map(MergeRequestExistingUser::new)
                .filter(mr -> !mr.getExistingUserId().equals(requestedUserId))
                .forEach(mr -> {
                    if (!existSet.contains(mr.getExistingUserId())) {
                        result.add(mr);
                        existSet.add(mr.getExistingUserId());
                    }
                });
        return result;
    }

    public void createMergeRequest(UUID requestedUserId, CreateMergeRequestDto createMergeRequestDto) {
        if (ObjectUtils.isEmpty(createMergeRequestDto.getLicenseNumber()) && ObjectUtils.isEmpty(createMergeRequestDto.getTagNumber()))
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "License number or tag number is required");
        // user cannot have more than 3 pending merge requests
        throwIfExceedMaximumThreeRequests(requestedUserId);
        // check if the request already exists
        throwIfMergeRequestAlreadyExist(requestedUserId, createMergeRequestDto.getLicenseNumber(), createMergeRequestDto.getTagNumber());

        var existingUserIds = getMergeUserIds(createMergeRequestDto.getLicenseNumber(), createMergeRequestDto.getTagNumber(), requestedUserId);

        var mergeRequest = new MergeRequest(
                requestedUserId, existingUserIds,
                createMergeRequestDto.getTagNumber(), createMergeRequestDto.getLicenseNumber()
        );
        mergeRequestRepository.save(mergeRequest);
    }

    @Transactional(readOnly = true)
    public MergeRequest getMergeRequestReadOnly(UUID mergeRequestEntityId) {
        return findByEntityId(mergeRequestEntityId);
    }

    @Transactional(readOnly = true)
    public List<MergeRequest> getMergeRequestsReadOnly(List<MergeRequestStatusEnum> statusEnums) {
        if (ObjectUtils.isEmpty(statusEnums)) {
            statusEnums = List.of(MergeRequestStatusEnum.PENDING);
        }
        return mergeRequestRepository.findAllByStatusIn(statusEnums);
    }

    @Transactional(readOnly = true)
    public List<MergeRequest> getMergeRequestsByRequestUserReadOnly(UUID requestedUserId, MergeRequestStatusEnum... statuses) {
        if (ObjectUtils.isEmpty(statuses)) {
            statuses = new MergeRequestStatusEnum[]{MergeRequestStatusEnum.PENDING};
        }
        return mergeRequestRepository.findAllByRequestedUserIdAndStatusIn(requestedUserId, List.of(statuses));
    }

    public void approveMergeRequest(@NonNull ApproveMergeRequest approveMergeRequest) {
        var requestedParticipant = participantService.getParticipantOrElseThrow(approveMergeRequest.requestedUserId());
        for (UUID existingUserId : approveMergeRequest.existingUserIds()) {
            var existingParticipant = participantService.getParticipantOrElseThrow(existingUserId);
            participantService.transferOwnerShip(existingParticipant, requestedParticipant);
        }

        applyAction(approveMergeRequest.requestedUserId(), approveMergeRequest.existingUserIds(), MergeRequest::approve);
    }


    public void rejectMergeRequest(@NonNull RejectMergeRequest rejectMergeRequest) {
        applyAction(
                rejectMergeRequest.requestedUserId(),
                rejectMergeRequest.existingUserIds(),
                mergeRequest -> mergeRequest.reject(rejectMergeRequest.reason(), rejectMergeRequest.comment())
        );
    }

    public void applyAction(UUID requestedUserId, Set<UUID> existingUserIds, Consumer<MergeRequest> action) {
        List<MergeRequest> rejectedMergeRequests = new ArrayList<>();
        Set<UUID> existingUserIdsSet = new HashSet<>(existingUserIds);

        mergeRequestRepository.findAllByRequestedUserIdAndStatusIn(requestedUserId, List.of(MergeRequestStatusEnum.PENDING))
                .forEach(mergeRequest -> {
                    Set<UUID> residentUUIDs = new HashSet<>(mergeRequest.getResidentUUIDs());
                    residentUUIDs.retainAll(existingUserIdsSet); // Retain only existing user IDs
                    if (!residentUUIDs.isEmpty()) {
                        action.accept(mergeRequest);
                        rejectedMergeRequests.add(mergeRequest);
                    }
                });

        mergeRequestRepository.saveAll(rejectedMergeRequests);
    }
}