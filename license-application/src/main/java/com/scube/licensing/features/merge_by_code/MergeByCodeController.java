package com.scube.licensing.features.merge_by_code;

import com.scube.licensing.features.merge_by_code.dtos.MergeByCodeRequest;
import com.scube.licensing.features.permission.Permissions;
import com.scube.licensing.features.profile.dto.ParticipantDto;
import jakarta.annotation.security.RolesAllowed;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Size;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.UUID;

@RestController
@RequestMapping("/merge-by-code")
@RequiredArgsConstructor
@Validated
public class MergeByCodeController {
    private final MergeByCodeService mergeByCodeService;

    @PostMapping("/participant/{participantEntityId}")
    @RolesAllowed(Permissions.MergeByCode.MERGE_BY_CODE)
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public void mergeByCode(@PathVariable UUID participantEntityId, @Valid @RequestBody MergeByCodeRequest request) {
        mergeByCodeService.mergeByCode(participantEntityId, request.registrationCode());
    }

    @GetMapping("{registrationCode}/exists/participant/{participantEntityId}")
    @RolesAllowed(Permissions.MergeByCode.EXISTS_BY_REGISTRATION_CODE)
    @ResponseStatus(HttpStatus.OK)
    public ParticipantDto existsByRegistrationCode(@PathVariable UUID participantEntityId, @PathVariable @Size(max = 20) String registrationCode) {
        return mergeByCodeService.existsByRegistrationCode(participantEntityId, registrationCode);
    }
}