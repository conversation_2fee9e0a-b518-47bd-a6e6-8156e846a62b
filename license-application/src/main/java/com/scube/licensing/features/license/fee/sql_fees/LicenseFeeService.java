package com.scube.licensing.features.license.fee.sql_fees;

import com.scube.licensing.features.license.CalculationObserverEvent;
import com.scube.licensing.features.license.dto.LicenseActivityFeeRemoveRequest;
import com.scube.licensing.features.license.dto.LicenseActivityFeeUpdateRequest;
import com.scube.licensing.infrastructure.db.PostgresService;
import com.scube.licensing.infrastructure.db.entity.license.License;
import com.scube.licensing.infrastructure.db.entity.license.LicenseActivityFee;
import com.scube.licensing.infrastructure.db.entity.license.type.LicenseType;
import com.scube.licensing.infrastructure.db.repository.license.LicenseActivityFeeRepository;
import com.scube.licensing.infrastructure.db.repository.license.LicenseRepository;
import com.scube.licensing.infrastructure.db.repository.license.LicenseTypeRepository;
import com.scube.licensing.infrastructure.httpclient.CalculationService;
import com.scube.rabbit.core.AmqpGateway;
import lombok.RequiredArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.springframework.http.HttpStatus;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.web.server.ResponseStatusException;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class LicenseFeeService {
    private final LicenseRepository licenseRepository;
    private final LicenseTypeRepository licenseTypeRepository;
    private final JdbcTemplate jdbcTemplate;
    private final PostgresService postgresService;
    private final LicenseActivityFeeRepository licenseActivityFeeRepository;
    private final CalculationService calculationService;
    private final AmqpGateway amqpGateway;

    @Transactional(readOnly = true)
    public FeeCalculationByDurationResponse getFeePreviews(UUID entityId, boolean isResident) {
        License license = licenseRepository.findByUuidOrThrow(entityId);
        return getFeePreviews(license, isResident);
    }

    public FeeCalculationByDurationResponse getFeePreviews(License license, boolean isResident) {
        var licenseType = licenseTypeRepository.findByCodeIgnoreCase(license.getLicenseType().getCode())
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "License type with code " + license.getLicenseType().getCode() + " not found"));

        var feePreviewFunction = Optional.ofNullable(licenseType.getFeePreviewFunction())
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.BAD_REQUEST, "License type with code " + license.getLicenseType().getCode() + " does not have a fee preview function"));
        List<LicenseCalculationResponse> queryResult = queryFunctionForList(feePreviewFunction, license.getUuid()).stream()
                .map(LicenseCalculationResponse::new).toList();

        var determineDurationFunction = Optional.ofNullable(licenseType.getDurationFunction())
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.BAD_REQUEST, "License type with code " + license.getLicenseType().getCode() + " does not have a duration function"));
        var durationResult = queryFunctionForList(determineDurationFunction, license.getUuid()).stream().findFirst()
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.BAD_REQUEST, "Duration not found"));

        Map<String, List<LicenseCalculationResponse>> feePreviews = queryResult.stream()
                .collect(Collectors.groupingBy(LicenseCalculationResponse::getLabel));
        List<FeeCalculationResponse> feeResponses = feePreviews.entrySet().stream()
                .map(entry -> {
                    List<LicenseCalculationResponse> values = entry.getValue();
                    List<Fee> fees = values.stream()
                            .map(LicenseCalculationResponse::toFee)
                            .toList();

                    var feeCodes = fees.stream().map(Fee::getKey).toList();
                    List<Fee> queriedFees = getFeeQuery(feeCodes);
                    for (Fee item : queriedFees) {
                        if (List.of(FeeType.FLAT, FeeType.PERCENTAGE).contains(item.getOperation())) {
                            for (var fee : fees) {
                                if (!fee.getKey().equals(item.getKey())) continue;
                                fee.setFeeName(item.getFeeName());
                                fee.setAmount(item.getAmount());
                                fee.setProperties(item.getProperties());
                                fee.setOperation(item.getOperation());
                            }
                        }
                    }

                    fees = new ArrayList<>(fees);
                    calculatePercentageAmount(fees, isResident);

                    LicenseCalculationResponse firstValue = values.getFirst();
                    return new FeeCalculationResponse(
                            fees,
                            entry.getKey(),
                            firstValue.getDuration(),
                            firstValue.getStartYear(),
                            firstValue.getEndYear()
                    );
                })
                .toList();
        return new FeeCalculationByDurationResponse(feeResponses, durationResult);
    }

    public FeeCalculationResponse calculateAndGetFees(License license, boolean isResident) {
        LicenseType licenseType = license.getLicenseType();

        return calculateAndGetFees(Map.of(
                "entity_id", license.getUuid(),
                "license_type_code", licenseType.getCode()
        ), isResident);
    }

    public FeeCalculationResponse calculateAndGetFees(Map<String, Object> licenseData, boolean isResident) {
        List<LicenseCalculationResponse> licenseCalculationResponses = calculateLicenseFee(licenseData);

        List<String> feeCodes = licenseCalculationResponses.stream()
                .filter(x -> Objects.isNull(x.getFeeAmount()) || BigDecimal.ZERO.compareTo(x.getFeeAmount()) == 0)
                .map(LicenseCalculationResponse::getFeeCode)
                .toList();


        List<Fee> calculatedFees = licenseCalculationResponses.stream()
                .filter(x -> !feeCodes.contains(x.getFeeCode()))
                .map(LicenseCalculationResponse::toFee)
                .toList();

        var queriedFees = getFeeQuery(feeCodes);

        List<Fee> allFees = new ArrayList<>(queriedFees);
        allFees.addAll(calculatedFees);

        calculatePercentageAmount(allFees, isResident);

        return new FeeCalculationResponse(allFees);
    }

    private @NotNull List<Fee> getFeeQuery(List<String> feeCodes) {
        return feeCodes.stream()
                .map(calculationService::getFeeByKey)
                .filter(Objects::nonNull)
                .toList();
    }

    private static void calculatePercentageAmount(List<Fee> allFees, boolean isResident) {
        BigDecimal feeAmount = allFees.stream()
                .filter(f -> !f.isPercentage())
                .map(Fee::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        var toRemove = new ArrayList<Fee>();
        for (var item : allFees) {
            if (item.isPercentage()) {
                var percentAmount = item.getAmount();
                Assert.notNull(percentAmount, "Percentage for %s cannot be null".formatted(item.getKey()));
                Assert.isTrue(percentAmount.compareTo(BigDecimal.ZERO) > 0, "Percentage for %s must be greater than zero.".formatted(item.getKey()));
                percentAmount = percentAmount.divide(BigDecimal.valueOf(100));
                var amount = feeAmount.multiply(percentAmount);
                item.setAmount(amount);
            }

            if (item.isResidentOnly()) {
                if (isResident) continue;
                // add to the list to remove
                toRemove.add(item);
            }
        }
        allFees.removeAll(toRemove);
    }

    public List<LicenseCalculationResponse> calculateLicenseFee(License license) {
        LicenseType licenseType = license.getLicenseType();

        return calculateLicenseFee(Map.of(
                "entity_id", license.getUuid(),
                "license_type_code", licenseType.getCode()
        ));
    }

    private List<LicenseCalculationResponse> calculateLicenseFee(Map<String, Object> licenseData) {
        var request = new LicenseCalculationRequest(licenseData);
        var licenseType = licenseTypeRepository.findByCodeIgnoreCase(request.getLicenseTypeCode())
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "License type with code " + request.getLicenseTypeCode() + " not found"));

        var feeFunction = Optional.ofNullable(licenseType.getFeeConfigFunction())
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.BAD_REQUEST, "License type with code " + request.getLicenseTypeCode() + " does not have a fee calculation function"));

        List<Map<String, Object>> result = queryFunctionForList(feeFunction, request.getLicenseUuid());
        return result.stream()
                .map(LicenseCalculationResponse::new)
                .toList();
    }

    private List<Map<String, Object>> queryFunctionForList(String functionName, Object... args) {
        if (!postgresService.doesFunctionExist(functionName)) {
            throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Fee calculation function " + functionName + " does not exist");
        }
        var sql = "SELECT * from " + functionName + "(?)";
        return jdbcTemplate.queryForList(sql, args);
    }

    public void updateLicenseFee(UUID feeId, LicenseActivityFeeUpdateRequest request) {
        LicenseActivityFee licenseActivityFee = licenseActivityFeeRepository.findByUuidOrThrow(feeId);
        licenseActivityFee.changeFeeAmount(request.getFeeAmount(), request.getReason());
        licenseActivityFeeRepository.save(licenseActivityFee);
    }

    public void removeLicenseFee(UUID feeId, LicenseActivityFeeRemoveRequest request) {
        var licenseActivityFee = licenseActivityFeeRepository.findByUuidOrThrow(feeId);
        var licenseActivity = licenseActivityFee.getLicenseActivity();
        licenseActivity.removeLicenseFee(feeId, request.getReason());
        licenseActivityFeeRepository.delete(licenseActivityFee);
    }
}