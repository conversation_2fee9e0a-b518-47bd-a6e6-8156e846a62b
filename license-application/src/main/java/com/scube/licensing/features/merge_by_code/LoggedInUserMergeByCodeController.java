package com.scube.licensing.features.merge_by_code;

import com.scube.auth.library.MyOpenIdClaimSet;
import com.scube.licensing.features.merge_by_code.dtos.MergeByCodeRequest;
import com.scube.licensing.features.permission.Permissions;
import com.scube.licensing.features.profile.dto.ParticipantDto;
import jakarta.annotation.security.RolesAllowed;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Size;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.UUID;

@RestController
@RequestMapping("/me/merge-by-code")
@RequiredArgsConstructor
@Validated
public class LoggedInUserMergeByCodeController {
    private final MergeByCodeService mergeByCodeService;

    @PostMapping
    @RolesAllowed(Permissions.LoggedInUserMergeByCode.MERGE_BY_CODE)
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public void mergeByCode(@AuthenticationPrincipal MyOpenIdClaimSet claimSet, @Valid @RequestBody MergeByCodeRequest request) {
        mergeByCodeService.mergeByCode(UUID.fromString(claimSet.getSubject()), request.registrationCode());
    }

    @GetMapping("{registrationCode}/exists")
    @RolesAllowed(Permissions.LoggedInUserMergeByCode.EXISTS_BY_REGISTRATION_CODE)
    @ResponseStatus(HttpStatus.OK)
    public ParticipantDto existsByRegistrationCode(@AuthenticationPrincipal MyOpenIdClaimSet claimSet, @PathVariable @Size(max = 20) String registrationCode) {
        return mergeByCodeService.existsByRegistrationCode(UUID.fromString(claimSet.getSubject()), registrationCode);
    }
}