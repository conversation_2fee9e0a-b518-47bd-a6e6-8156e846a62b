package com.scube.licensing.features.merge_by_code;

import com.scube.audit.auditable.properties.FindByOption;
import com.scube.audit.auditable.properties.value_type.DatePropertyValue;
import com.scube.licensing.features.participant.ParticipantService;
import com.scube.licensing.features.participant.mapper.ParticipantDtoMapper;
import com.scube.licensing.features.profile.dto.ParticipantDto;
import com.scube.licensing.infrastructure.db.entity.participant.Participant;
import com.scube.licensing.infrastructure.db.repository.participant.ParticipantRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.web.server.ResponseStatusException;

import java.time.LocalDate;
import java.util.Map;
import java.util.UUID;

@Slf4j
@Service
@RequiredArgsConstructor
@Transactional
public class MergeByCodeService {
    private final ParticipantRepository participantRepository;
    private final ParticipantService participantService;
    private final ParticipantDtoMapper mapper;


    public void mergeByCode(UUID participantEntityId, String registrationCode) {
        log.info("Merging participant {} with registration code: {}", participantEntityId, registrationCode);
        if (ObjectUtils.isEmpty(registrationCode))
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Registration code cannot be empty");
        var participant = participantRepository.findByUuidOrThrow(participantEntityId);
        Map<String, Object> registrationCodeProperty = Map.of(Participant.REGISTRATION_CODE, registrationCode.trim());
        var matchedParticipantResult = participantRepository.findFirstByProperties(registrationCodeProperty, FindByOption.AND);

        if (matchedParticipantResult.isEmpty()) return;
        log.info("Found matched participant {} with registration code: {}", participantEntityId, registrationCode);
        var matchedParticipant = matchedParticipantResult.get();


        participant.mergeByRegistrationCode(matchedParticipant);

        participantService.transferOwnerShip(matchedParticipant, participant);

        participantRepository.save(participant);
        log.info("Participant {} merged successfully with registration code: {}", participantEntityId, registrationCode);
    }


    public ParticipantDto existsByRegistrationCode(UUID participantEntityId, String registrationCode) {
        try {
            if (ObjectUtils.isEmpty(registrationCode))
                throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Registration code cannot be empty");

            Map<String, Object> registrationCodeProperty = Map.of(Participant.REGISTRATION_CODE, registrationCode.trim());
            var matchedParticipant = participantRepository.findFirstByProperties(registrationCodeProperty, FindByOption.AND)
                    .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Cannot find participant with registration code: " + registrationCode));

            ParticipantDto dto = mapper.toDto(matchedParticipant);
            var dateOfBirth = new DatePropertyValue(dto.getCustomFields().getOrDefault("dateOfBirth", null))
                    .getValueOptional();
            if (dateOfBirth.isPresent() && (dateOfBirth.get().isAfter(LocalDate.now()) || dateOfBirth.get().getYear() == 1900)) {
                // if the date of birth is in future or is 1900 then replace it with null
                dto.getCustomFields().put("dateOfBirth", null);
            }
            return dto;
        } catch (ResponseStatusException e) {
            e.printStackTrace();
            return null;
        }
    }
}