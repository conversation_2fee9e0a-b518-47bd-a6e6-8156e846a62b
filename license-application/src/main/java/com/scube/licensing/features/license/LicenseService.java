package com.scube.licensing.features.license;

import com.scube.audit.auditable.entity.AuditableEntity;
import com.scube.audit.auditable.properties.type.PropertyTypeEnum;
import com.scube.audit.auditable.properties.value_type.BooleanPropertyValue;
import com.scube.calculation.dto.order.gen_dto.OrderInvoiceResponse;
import com.scube.licensing.features.association.delete_association.DeleteAssociationCommand;
import com.scube.licensing.features.association.find_all_child_associations_by_parent.FindAllChildAssociationsByParentQuery;
import com.scube.licensing.features.association.find_all_child_associations_by_parent_type_and_child_type.FindAllChildAssociationsByParentTypeAndChildTypeQuery;
import com.scube.licensing.features.association.find_all_parent_associations_by_child.FindAllParentAssociationsByChildQuery;
import com.scube.licensing.features.association.save_association.SaveAssociationCommand;
import com.scube.licensing.features.license.change_status.ChangeStatusCommand;
import com.scube.licensing.features.license.change_status.LicenseStatusChangedEvent;
import com.scube.licensing.features.license.exception.LicenseNotFoundException;
import com.scube.licensing.features.license.exception.LicenseStatusNotFoundException;
import com.scube.licensing.features.license.mapper.LicenseDtoMapper;
import com.scube.licensing.features.participant.ParticipantService;
import com.scube.licensing.features.participant.approvals.MarkParticipantAllAsApprovedCommand;
import com.scube.licensing.features.participant.dog.dto.CreateDogRequestDto;
import com.scube.licensing.features.profile.dto.LicenseDto;
import com.scube.licensing.features.profile.mapper.ProfileMapperService;
import com.scube.licensing.features.tenant.TenantService;
import com.scube.licensing.infrastructure.axon.AxonGateway;
import com.scube.licensing.infrastructure.axon.request.IRequestAxon;
import com.scube.licensing.infrastructure.axon.request.IRequestVoidAxon;
import com.scube.licensing.infrastructure.db.entity.association.Associable;
import com.scube.licensing.infrastructure.db.entity.association.AssociationType;
import com.scube.licensing.infrastructure.db.entity.license.License;
import com.scube.licensing.infrastructure.db.entity.license.LicenseActivity;
import com.scube.licensing.infrastructure.db.entity.license.LicenseActivityFee;
import com.scube.licensing.infrastructure.db.entity.license.status.LicenseStatus;
import com.scube.licensing.infrastructure.db.entity.license.status.LicenseStatusCodeEnum;
import com.scube.licensing.infrastructure.db.entity.license.type.LicenseType;
import com.scube.licensing.infrastructure.db.entity.license.type.LicenseTypeSetting;
import com.scube.licensing.infrastructure.db.entity.participant.Participant;
import com.scube.licensing.infrastructure.db.entity.participant.address.ParticipantAddress;
import com.scube.licensing.infrastructure.db.repository.license.*;
import com.scube.licensing.infrastructure.db.seed.participant.ParticipantAddressTypeSeed;
import com.scube.licensing.infrastructure.db.seed.participant.ParticipantGroupTypeSeed;
import com.scube.licensing.utils.DynamicViewUtils;
import com.scube.rabbit.core.AmqpGateway;
import com.scube.rabbit.core.fanout.publisher.IRabbitFanoutPublisher;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.axonframework.commandhandling.CommandHandler;
import org.axonframework.queryhandling.QueryHandler;
import org.hibernate.envers.AuditReader;
import org.hibernate.envers.query.AuditEntity;
import org.hibernate.envers.query.AuditQuery;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.web.server.ResponseStatusException;

import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

import static com.scube.lib.misc.CaseUtils.camelCaseToFriendlyName;
import static com.scube.licensing.features.license.specifications.LicenseSpecifications.createSpecification;
import static com.scube.licensing.utils.DynamicViewUtils.formatDataList;
import static org.apache.commons.lang3.StringUtils.isNotBlank;

@Service
@Slf4j
@RequiredArgsConstructor
@Transactional
public class LicenseService {
    public static final List<LicenseStatusCodeEnum> RENEWABLE_LICENSE_STATUSES = List.of(LicenseStatusCodeEnum.ACTIVE, LicenseStatusCodeEnum.EXPIRED, LicenseStatusCodeEnum.CANCELED, LicenseStatusCodeEnum.CLOSED);
    private final LicenseRepository licenseRepository;
    private final LicenseTypeRepository licenseTypeRepository;
    private final LicenseStatusRepository licenseStatusRepository;
    private final LicenseTypeSettingRepository licenseTypeSettingRepository;
    private final LicenseActivityRepository licenseActivityRepository;
    private final LicenseActivityFeeRepository licenseActivityFeeRepository;
    private final JdbcTemplate jdbcTemplate;
    private final AxonGateway axonGateway;
    private final AmqpGateway amqpGateway;
    private final AuditReader auditReader;
    private final LicenseDtoMapper licenseDtoMapper;
    private final ProfileMapperService profileMapperService;
    private final TenantService tenantService;


    @QueryHandler
    public Optional<License> findLicenseByEntityId(FindLicenseByEntityIdQuery query) {
        return licenseRepository.findByUuid(query.entityId());
    }

    @QueryHandler
    public License findLicenseByEntityIdOrElseThrow(FindLicenseByEntityIdOrElseThrowQuery query) {
        return licenseRepository.findByUuid(query.entityId())
                .orElseThrow(() -> new LicenseNotFoundException("License with entity id " + query.entityId() + " not found"));
    }

    @QueryHandler
    public License findLicenseByIdOrElseThrow(FindLicenseByIdOrElseThrowQuery query) {
        return licenseRepository.findById(query.id())
                .orElseThrow(() -> new LicenseNotFoundException("License not found"));
    }

    public License save(License license) {
        return licenseRepository.save(license);
    }

    public License saveAndFlush(License license) {
        return licenseRepository.saveAndFlush(license);
    }

    public LicenseType findLicenseTypeByCodeOrElseThrow(final String code) {
        return licenseTypeRepository.findByCodeIgnoreCase(code)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.BAD_REQUEST, "License type with code " + code + " not found"));
    }

    public LicenseType findLicenseTypeOrCreate(final String code) {
        return licenseTypeRepository.findByCodeIgnoreCase(code)
                .orElseGet(() -> {
                    var licenseType = new LicenseType();
                    licenseType.setCode(code);
                    licenseType.setCode(camelCaseToFriendlyName(code));
                    return licenseTypeRepository.save(licenseType);
                });
    }

    public LicenseStatus findLicenseStatusOrCreate(final String status) {
        return licenseStatusRepository.findByNameIgnoreCase(status)
                .orElseGet(() -> {
                    var licenseStatus = new LicenseStatus();
                    licenseStatus.setName(status);
                    return licenseStatusRepository.save(licenseStatus);
                });
    }

    public Optional<LicenseStatus> findLicenseStatusByName(String status) {
        return licenseStatusRepository.findByNameIgnoreCase(status);
    }

    public Optional<LicenseStatus> findLicenseStatusById(Long id) {
        return licenseStatusRepository.findById(id);
    }


    public LicenseTypeSetting findSettingByLicenseTypeIdOrElseThrow(Long licenseTypeId) {
        return licenseTypeSettingRepository.findByLicenseTypeId(licenseTypeId)
                .orElseThrow(() -> new ResponseStatusException(
                        org.springframework.http.HttpStatus.INTERNAL_SERVER_ERROR,
                        "License type setting not found"
                ));
    }

    public List<Map<String, Object>> getLicenseDataDynamic(String sql, Object[] params) {
        List<Map<String, Object>> licenses = jdbcTemplate.queryForList(sql, params);

        if (ObjectUtils.isEmpty(licenses)) {
            return new ArrayList<>();
        }

        // override with tenant data
        licenses.forEach(this::addTenantData);

        var formattedData = licenses.stream()
                .map(DynamicViewUtils::formatData)
                .collect(Collectors.toList());

        return formattedData;
    }

    public Map<String, Object> getLicenseData(UUID entityId) {
        return getLicenseData(entityId, true);
    }

    public Map<String, Object> getLicenseData(UUID entityId, boolean formatData) {
        String query = "SELECT * FROM view_pivoted_license WHERE entity_id = ?";
        var licenses = jdbcTemplate.queryForList(query, entityId);
        if (ObjectUtils.isEmpty(licenses)) return new HashMap<>();
        var license = licenses.getFirst();

        addTenantData(license);

        if (!formatData) return license;
        var result = formatDataList(licenses);
        return result;
    }

    private void addTenantData(Map<String, Object> licenseData) {
        var tenantData = tenantService.getTenant();
        licenseData.put("clerk.clerkEmail", tenantData.getClerkEmail());
        licenseData.put("clerk.clerkTitle", tenantData.getClerkTitle());
        licenseData.put("clerk.clerkPhoneNumber", tenantData.getClerkPhoneNumber());
        licenseData.put("clerk.clerkName", tenantData.getClerkName());
        licenseData.put("clerk.clerkOfficeName", tenantData.getCityClerkOfficeName());
        licenseData.put("clerk.clerkXpressUrl", tenantData.getClerkXpressUrl());

        licenseData.put("municipality.building", tenantData.getAdminOffice());
        licenseData.put("municipality.address", tenantData.getAdminStreet());
        licenseData.put("municipality.roomNumber", tenantData.getAdminOfficeRoom());
        licenseData.put("municipality.city", tenantData.getAdminCity());
        licenseData.put("municipality.state", tenantData.getAdminState());
        licenseData.put("municipality.zipCode", tenantData.getAdminZipCode());
        licenseData.put("municipality.cityPhoneNumber", tenantData.getClerkPhoneNumber());
    }

    public String getLicenseData(UUID entityId, String fieldName) {
        var map = getLicenseData(entityId, false);
        return DynamicViewUtils.getData(map, fieldName);
    }

    public List<LicenseActivityFee> getUnpaidLicenseFees(UUID entityId) {
        return licenseRepository.findByUuid(entityId)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.BAD_REQUEST, "License with entity id " + entityId + " not found")
                ).getUnpaidLicenseFees();
    }

    public List<String> getLicenseAction(UUID entityId) {
        var result = new ArrayList<String>();
        var license = licenseRepository.findByUuid(entityId)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.BAD_REQUEST, "License with entity id " + entityId + " not found")
                );

        //check if the license is expired or about to expired in 30 days
        if (license.isAllowableRenewalStatus() && license.canRenew()) {
            result.add("pendingRenewal");
        }

        // DR: commented out cuz of greenwich data
//        if (license.isAllowableDeletionStatus())
//            result.add("canDelete");

        if (license.isAllowableDeletionStatus() || license.isAllowableRenewalCancelStatus())
            result.add("canCancelRenewal");

        // this to add pending payment or pending approval
        result.addAll(license.getOutstandingApprovals());

        return result;
    }

    public Boolean updateLicenseActivityFeeWithOrderId(List<Long> licenseActivityFeeIds, UUID orderId) {
        var licenseActivityFees = licenseActivityFeeRepository.findAllById(licenseActivityFeeIds);
        if (licenseActivityFees.isEmpty())
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "License activity fee not found");
        licenseActivityFees.forEach(licenseActivityFee -> licenseActivityFee.setOrderId(orderId));
        licenseActivityFeeRepository.saveAll(licenseActivityFees);
        return true;
    }

    @Transactional(isolation = Isolation.REPEATABLE_READ)
    public void updateLicenseActivityFeeAsPaid(UUID orderId) {
        licenseActivityFeeRepository.updatePaymentStatusByOrderId(LicenseActivityFee.PaymentStatus.PAID, orderId, Instant.now());
    }

    @Transactional(isolation = Isolation.REPEATABLE_READ)
    public void updateLicenseActivityFeeAsPaid(OrderInvoiceResponse orderInvoice) {
        var licenseActivityFees = getLicenseActivityFeeIds(orderInvoice);

        licenseActivityFees.forEach(licenseActivityFee -> {
            licenseActivityFee.setPaymentStatus(LicenseActivityFee.PaymentStatus.PAID);
            licenseActivityFee.setPaidDate(Instant.now());
            licenseActivityFee.setOrderId(orderInvoice.getOrderId());
        });

        licenseActivityFeeRepository.saveAll(licenseActivityFees);
    }

    @Transactional(isolation = Isolation.REPEATABLE_READ)
    public void updateLicenseActivityFeeAsUnPaid(OrderInvoiceResponse orderInvoice) {
        var licenseActivityFees = getLicenseActivityFeeIds(orderInvoice);

        licenseActivityFees.forEach(licenseActivityFee -> {
            licenseActivityFee.setPaymentStatus(LicenseActivityFee.PaymentStatus.UNPAID);
            licenseActivityFee.setPaidDate(Instant.now());
            licenseActivityFee.setOrderId(orderInvoice.getOrderId());
        });

        licenseActivityFeeRepository.saveAll(licenseActivityFees);
    }

    @Transactional(isolation = Isolation.REPEATABLE_READ)
    public void updateLicenseActivityFeeAsPartiallyPaid(OrderInvoiceResponse orderInvoice) {
        var licenseActivityFees = getLicenseActivityFeeIds(orderInvoice);

        licenseActivityFees.forEach(licenseActivityFee -> {
            if (licenseActivityFee.getPaymentStatus().equals(LicenseActivityFee.PaymentStatus.PAID))
                return;
            licenseActivityFee.setPaymentStatus(LicenseActivityFee.PaymentStatus.PAID);
            licenseActivityFee.setOrderId(orderInvoice.getOrderId());
        });

        licenseActivityFeeRepository.saveAll(licenseActivityFees);
    }

    private List<LicenseActivityFee> getLicenseActivityFeeIds(OrderInvoiceResponse orderInvoice) {
        List<Long> licenseActivityFeeIds = orderInvoice.getItems()
                .stream()
                .filter(item -> item.getItemType().equalsIgnoreCase("license"))
                .map(item -> (List<Long>) item.getProperties().get("licenseActivityFeeIds"))
                .flatMap(Collection::stream)
                .toList();

        return licenseActivityFeeRepository.findAllById(licenseActivityFeeIds);
    }

    public License getSecondToLastRevision(Long id) {
        AuditQuery auditQuery = auditReader.createQuery()
                .forRevisionsOfEntity(License.class, true, true)
                .add(AuditEntity.id().eq(id))
                .addOrder(AuditEntity.revisionNumber().desc())
                .setMaxResults(2);
        var result = auditQuery.getResultList();
        if (result.size() < 2) return null;
        return (License) auditQuery.getResultList().get(1);
    }

    public License getLastRevisionNotOfStatus(Long id, LicenseStatus status) {
        AuditQuery auditQuery = auditReader.createQuery()
                .forRevisionsOfEntity(License.class, true, true)
                .add(AuditEntity.id().eq(id))
                .add(AuditEntity.property("licenseStatus").ne(status))
                .addOrder(AuditEntity.revisionNumber().desc())
                .setMaxResults(1);
        var result = auditQuery.getResultList();
        if (result.isEmpty()) return null;
        return (License) auditQuery.getResultList().getFirst();
    }

    @Nullable
    public License getLastRevisionThatIsRenewable(Long id) {
        var statuses = licenseStatusRepository.findAllByCodeIn(RENEWABLE_LICENSE_STATUSES);
        var statusIds = statuses.stream().map(LicenseStatus::getId).toList();
        AuditQuery auditQuery = auditReader.createQuery()
                .forRevisionsOfEntity(License.class, true, true)
                .add(AuditEntity.id().eq(id))
                .addOrder(AuditEntity.revisionNumber().desc());
        List<License> result = auditQuery.getResultList();
        if (result.isEmpty()) return null;
        return result.stream()
                .filter(license -> statusIds.contains(license.getLicenseStatus().getId()))
                .findFirst()
                .orElse(null);
    }

    public void deleteLicense(UUID entityId) {
        License license = licenseRepository.findByUuid(entityId)
                .orElse(null);

        if (license == null)
            return;

        if (license.isAllowableRenewalCancelStatus()) {
            undoRenewal(license);
            return;
        }

        if (!license.isAllowableDeletionStatus())
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "License is not in a deletable status: " + license.getLicenseStatus().getName());

        deleteLicense(license);
    }

    public void deleteLicense(License license) {
        //Get all parent associations
        var parentAssociations = axonGateway.query(new FindAllChildAssociationsByParentQuery(license));

        Set<UUID> participantIds = new HashSet<>();

        //Delete parent associations
        for (var association : parentAssociations) {
            //If it's a dog license we need to delete the dog also
            //When we have more license types we can implement strategy patterns here
            if (association.getChildAssociationType().equals(AssociationType.PARTICIPANT)) {
                Participant participant = axonGateway.query(new ParticipantService.FindParticipantByIdOrElseThrowQuery(association.getChildId()));

                if (license.getLicenseType().getName().equalsIgnoreCase("Dog License")
                    && participant.getParticipantTypeGroup().getParticipantGroup().getName().equalsIgnoreCase("Dog")) {
                    participantIds.add(participant.getUuid());
                }

            }

            //Delete parent associations
            axonGateway.send(new DeleteAssociationCommand(association));
        }

        for (var participantId : participantIds) {
            amqpGateway.publish(new DeleteParticipantCommand(participantId));
        }

        //get all child associations
        var childAssociations = axonGateway.query(new FindAllParentAssociationsByChildQuery(license));

        //Delete child associations
        for (var association : childAssociations) {
            axonGateway.send(new DeleteAssociationCommand(association));
        }

        //delete the license
        licenseRepository.deleteById(license.getId());
    }

    public void undoRenewal(UUID licenseUUID) {
        License license = licenseRepository.findByUuid(licenseUUID)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.BAD_REQUEST, "License with entity id " + licenseUUID + " not found"));

        undoRenewal(license);
    }

    @Transactional(isolation = Isolation.REPEATABLE_READ)
    public void undoRenewal(License license) {
        var activities = license.getLicenseActivities()
                .stream()
                .filter(LicenseActivity::isUnpaid)
                .toList();

        activities.forEach(license.getLicenseActivities()::remove);

        // verify that the status is going back to a status that'll make the license renewable again
        LicenseStatus nonRenewableStatus = Optional.ofNullable(license.getLicenseStatus())
                .filter(x -> RENEWABLE_LICENSE_STATUSES.contains(x.getCode()))
                .orElse(null);
        if (nonRenewableStatus == null) {
            var validToDate = license.getValidToDate();
            if (validToDate == null) {
                nonRenewableStatus = findLicenseStatusOrCreate("Draft");
            } else if (validToDate.isAfter(Instant.now())) {
                // if date in future then set as active
                // otherwise to expired
                nonRenewableStatus = findLicenseStatusOrCreate("Active");
            } else {
                nonRenewableStatus = findLicenseStatusOrCreate("Expired");
            }
            license.setLicenseStatus(nonRenewableStatus);
            license.setApproved(true);
        }

        save(license);
        amqpGateway.publish(new CalculationObserverEvent("remove", List.of(license.getUuid())));
    }


    public void changeAllLicenseStatuses(Associable profile, String status, String modifier) {
        //retrieve dog associations
        var licenses = profile.getLicenses().stream()
                .filter(lic -> !lic.isDraft())
                .toList();
        licenses.forEach(license -> changeStatus(license, status, modifier, false));
    }


    public void revertAllLicenseStatuses(Associable profile) {
        //retrieve dog associations
        var licenses = profile.getLicenses().stream()
                .filter(lic -> !lic.isDraft())
                .toList();
        licenses.forEach(license -> {
            var previous = getLastRevisionNotOfStatus(license.getId(), license.getLicenseStatus());
            if (ObjectUtils.isEmpty(previous)) return;

            String previousStatus = previous.getLicenseStatus().getName();
            String previousModifier = previous.getModifier();

            if (ObjectUtils.isEmpty(previousStatus)) return;

            changeStatus(license, previousStatus, previousModifier, false);
        });
    }


    public void changeAllLicenseModifiers(Associable profile, String modifier) {
        var licenses = profile.getLicenses().stream()
                .filter(lic -> !lic.isDraft())
                .toList();

        //regenerate the dog licenses
        licenses.forEach(license -> updateModifier(license, modifier));
    }


    public void changeStatus(UUID entityId, String status, String modifier, boolean publishEvent) {
        var license = findLicenseByEntityId(new LicenseService.FindLicenseByEntityIdQuery(entityId)).orElseThrow();
        changeStatus(license, status, modifier, publishEvent);
    }

    @Transactional(isolation = Isolation.REPEATABLE_READ)
    public void changeStatus(License license, String status, String modifier, boolean publishEvent) {
        var licenseStatus = findLicenseStatusByName(status).orElseThrow(() -> new LicenseStatusNotFoundException("License status not found"));

        // update the license number if the license is active
        // and the license number has not yet been set or the license is a pending license
        // or the license number is an integer meaning it is a license that is not the current sequencing
        if (licenseStatus.getCode() == LicenseStatusCodeEnum.ACTIVE
            && (ObjectUtils.isEmpty(license.getLicenseNumber()) || license.getLicenseNumber().contains("PEND") || isInteger(license.getLicenseNumber()))
        ) {
            //TODO: needs to be refactored to have a number sequencing feature
            var currentYear = Calendar.getInstance().get(Calendar.YEAR);
            if (license.isPurebredDogLicense()) {
                license.setLicenseNumber(String.format("%sPDL%s", currentYear, getNextPurebredLicenseNumber()));
            } else if (license.isDogLicense()) {
                license.setLicenseNumber(String.format("%sDL%s", currentYear, getNextLicenseNumber()));
            } else {
                throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Number sequencing not implemented for this license type: " + license.getLicenseType().getName());
            }
        }

        if (isNotBlank(modifier)) {
            license.setModifier(modifier);
        }

        license.setLicenseStatus(licenseStatus);

        save(license);

        if (publishEvent) {
            amqpGateway.publish(new LicenseStatusChangedEvent(license));
        }
    }

    private boolean isInteger(@NonNull String s) {
        return s.matches("\\d+");
    }


    public void saveRenewal(LicenseActivity licenseRenewal) {
        licenseActivityRepository.save(licenseRenewal);
    }

    public Long getNextLicenseNumber() {
        return licenseRepository.getNextLicenseNumber();
    }

    public Long getNextPurebredLicenseNumber() {
        return licenseRepository.getNextPurebredLicenseNumber();
    }


    public Long getNextPendingDogLicenseNumber() {
        return licenseRepository.getNextPendingDogLicenseNumber();
    }


    public Long getNextPendingPurebredDogLicenseNumber() {
        return licenseRepository.getNextPendingPurebredDogLicenseNumber();
    }

    public void updateModifier(License license, String modifier) {
        license.setModifier(modifier);
        save(license);
    }

    @CommandHandler
    public void regenerateLicenseForm(RegenerateLicenseFormCommand command) {
        regenerateLicenseForm(command.entityId());
    }

    public void regenerateLicenseForm(UUID licenseEntityId) {
        var license = licenseRepository.findByUuidOrThrow(licenseEntityId);
        license.setProperty(PropertyTypeEnum.STRING, "licenseForm", "");
        save(license);
        amqpGateway.publish(new GenerateLicenseFormEvent(license.getUuid().toString(), license.getLicenseType().getCode()));
    }

    public GetLicenseFormResponse getLicenseFormResponse(UUID licenseEntityId) {
        var license = licenseRepository.findByUuidOrThrow(licenseEntityId);
        var licenseForm = license.getPropertyAsString("licenseForm").orElse(null);
        return new GetLicenseFormResponse(licenseForm);
    }

    public LicenseDto toDto(License license) {
        return licenseDtoMapper.toDto(license);
    }

    @Transactional(readOnly = true)
    public Page<?> query(Pageable pageable, Map<String, Object> searchParams) {
        var start = System.currentTimeMillis();
        Page<License> licensePage = licenseRepository.findAll(createSpecification(searchParams), pageable);
        log.info("License query took {} ms", System.currentTimeMillis() - start);
        Page<HashMap<String, Object>> mappedList = licensePage
                .map(license -> {
                    var result = new HashMap<String, Object>();
                    result.put("license", licenseDtoMapper.toQueryDto(license));
                    var childResult = new HashMap<String, List<Object>>();
                    license.getChildAssociables()
                            .forEach(child -> {
                                var childDto = profileMapperService.toQueryDto(child);
                                childResult.computeIfAbsent(child.getEntityType(), k -> new ArrayList<>()).add(childDto);
                            });
                    result.putAll(childResult);
                    return result;
                });
        log.info("License query dto took {} ms", System.currentTimeMillis() - start);
        return mappedList;
    }


    @Transactional(isolation = Isolation.REPEATABLE_READ)
    public void markAsApproved(UUID licenseEntityId) {
        License license = axonGateway.query(new FindLicenseByEntityIdOrElseThrowQuery(licenseEntityId));
        license.markAsApproved();
        save(license);

        var participants = new ArrayList<Participant>();
        participants.addAll(license.getChildAssociables(Participant.class));
        participants.addAll(license.getParentAssociables(Participant.class));

        axonGateway.sendAndWait(new MarkParticipantAllAsApprovedCommand(
                participants.stream()
                        .map(AuditableEntity::getUuid)
                        .toArray(UUID[]::new))
        );

        axonGateway.sendAndWait(new ChangeStatusCommand(license.getUuid(), "Approved"));
        axonGateway.sendAndWait(new ChangeStatusCommand(license.getUuid(), "Active"));
    }

    public void markAsPendingApproval(UUID licenseEntityId) {
        var license = axonGateway.query(new FindLicenseByEntityIdOrElseThrowQuery(licenseEntityId));
        license.clearApproval();
        save(license);

        axonGateway.sendAndWait(new ChangeStatusCommand(licenseEntityId, "Pending Approval"));
    }


    public void markAsRejected(UUID licenseEntityId, @NonNull String reason, String comment) {
        var license = axonGateway.query(new FindLicenseByEntityIdOrElseThrowQuery(licenseEntityId));
        license.markAsRejected(reason, comment);
        save(license);

        axonGateway.sendAndWait(new ChangeStatusCommand(license.getUuid(), "Rejected"));
    }

    public void addDogToLicense(UUID licenseEntityId, Participant dogEntity, CreateDogRequestDto requestDto) {
        var licenseEntity = axonGateway.query(new FindLicenseByEntityIdOrElseThrowQuery(licenseEntityId));

        //associate the dog to the license
        axonGateway.sendAndWait(new SaveAssociationCommand(licenseEntity, dogEntity));
        axonGateway.sendAndWait(new SaveAssociationCommand(dogEntity, licenseEntity));

        var dogHolderAssociation = axonGateway.query(new FindAllChildAssociationsByParentTypeAndChildTypeQuery(licenseEntity, AssociationType.PARTICIPANT));
        for (var holderAssociation : dogHolderAssociation) {
            var dogHolderEntity = axonGateway.query(new ParticipantService.FindParticipantByIdOrElseThrowQuery(holderAssociation.getChildId()));

            if (dogHolderEntity.getParticipantTypeGroup().getParticipantType().getName().equals(ParticipantGroupTypeSeed.LICENSE_HOLDER_PARTICIPANT_TYPE)) {
                axonGateway.sendAndWait(new SaveAssociationCommand(dogEntity, dogHolderEntity));
                axonGateway.sendAndWait(new SaveAssociationCommand(dogHolderEntity, dogEntity));

                //associate to the dog owner address
                var primaryAddresses = dogHolderEntity.getParticipantAddresses().stream()
                        .filter(t -> t.getParticipantAddressType().getName().equalsIgnoreCase(ParticipantAddressTypeSeed.PRIMARY))
                        .map(ParticipantAddress::getAddress)
                        .toList();
                for (var address : primaryAddresses) {
                    axonGateway.sendAndWait(new SaveAssociationCommand(dogEntity, address));
                    axonGateway.sendAndWait(new SaveAssociationCommand(address, dogEntity));
                }
            }
        }

        if (licenseEntity.isPurebredDogLicense()) {
            var isSpayedOrNeutered = requestDto.getDogSpayedOrNeutered();
            var dogSpayOrNeut = new BooleanPropertyValue(isSpayedOrNeutered).getValueOptional().orElse(false);
            var licenseDuration = licenseEntity.getPropertyAsLong("licenseDuration").orElse(1L);
            licenseEntity.addPurebredLicenseActivity(dogSpayOrNeut, licenseDuration);

            licenseEntity.setModifier("");
            regenerateLicenseForm(licenseEntity.getUuid());
            axonGateway.sendAndWait(new ChangeStatusCommand(licenseEntity.getUuid(), "Pending Payment"));
        }
    }

    @Transactional(readOnly = true)
    public List<String> getOutstandingApprovals(UUID licenseEntityId) {
        var license = axonGateway.query(new FindLicenseByEntityIdOrElseThrowQuery(licenseEntityId));
        return license.getOutstandingApprovals();
    }

    public List<License> findAllById(Iterable<Long> licenseIds) {
        return licenseRepository.findAllById(licenseIds);
    }

    // @formatter:off
    public record FindLicenseByEntityIdQuery(UUID entityId) implements IRequestAxon<Optional<License>> { }
    public record FindLicenseByEntityIdOrElseThrowQuery(UUID entityId) implements IRequestAxon<License> {}
    public record FindLicenseByIdOrElseThrowQuery(Long id) implements IRequestAxon<License> {}
    public record DeleteParticipantCommand(UUID entityId) implements IRabbitFanoutPublisher {}
    public record RegenerateLicenseFormCommand(UUID entityId) implements IRequestVoidAxon {}
    public record GenerateLicenseFormEvent(String licenseEntityId, String licenseType) implements IRabbitFanoutPublisher { }
    public record GetLicenseFormResponse(String uuid){}
    // @formatter:on
}