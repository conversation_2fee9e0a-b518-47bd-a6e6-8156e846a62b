package com.scube.licensing.features.merge_by_code;

import com.scube.licensing.features.merge_by_code.dtos.MergeByCodeCommand;
import com.scube.licensing.infrastructure.axon.handler.IRequestHandlerVoidAxon;
import lombok.RequiredArgsConstructor;
import org.axonframework.commandhandling.CommandHandler;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class MergeByCodeCommandHandler implements IRequestHandlerVoidAxon<MergeByCodeCommand> {
    private final MergeByCodeService mergeByCodeService;

    @Override
    @CommandHandler
    public void handle(MergeByCodeCommand command) {
        mergeByCodeService.mergeByCode(command.participantEntityId(), command.registrationCode());
    }
}